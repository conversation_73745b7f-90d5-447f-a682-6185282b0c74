from django.urls import path
from .views import TechnicalIndicatorsAPIView, TokenDataAPIView, TokenAnalysisAPIView, RefreshAnalysisReportView

urlpatterns = [
    path(
        'technical-indicators/<str:symbol>/',
        TechnicalIndicatorsAPIView.as_view(),
        name='technical_indicators'
    ),
    path(
        'token-data/<str:token_id>/',
        TokenDataAPIView.as_view(),
        name='token_data'
    ),
    path(
        'token-analysis/<str:symbol>/',
        TokenAnalysisAPIView.as_view(),
        name='token_analysis'
    ),
    path(
        'refresh-analysis/<str:symbol>/',
        RefreshAnalysisReportView.as_view(),
        name='refresh_analysis'
    ),
] 