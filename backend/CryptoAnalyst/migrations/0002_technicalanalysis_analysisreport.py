# Generated by Django 5.0.2 on 2025-05-09 07:39

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("CryptoAnalyst", "0001_initial"),
        ("wallets", "0011_wallet_kadena_chain_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="TechnicalAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("rsi", models.FloatField(blank=True, null=True)),
                ("macd_line", models.FloatField(blank=True, null=True)),
                ("macd_signal", models.FloatField(blank=True, null=True)),
                ("macd_histogram", models.FloatField(blank=True, null=True)),
                ("bb_upper", models.FloatField(blank=True, null=True)),
                ("bb_middle", models.FloatField(blank=True, null=True)),
                ("bb_lower", models.FloatField(blank=True, null=True)),
                ("bias", models.FloatField(blank=True, null=True)),
                ("psy", models.FloatField(blank=True, null=True)),
                ("dmi_plus", models.FloatField(blank=True, null=True)),
                ("dmi_minus", models.FloatField(blank=True, null=True)),
                ("dmi_adx", models.FloatField(blank=True, null=True)),
                ("vwap", models.FloatField(blank=True, null=True)),
                (
                    "token",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="technical_analyses",
                        to="wallets.token",
                    ),
                ),
            ],
            options={
                "verbose_name": "技术分析数据",
                "verbose_name_plural": "技术分析数据",
                "ordering": ["-timestamp"],
                "get_latest_by": "timestamp",
            },
        ),
        migrations.CreateModel(
            name="AnalysisReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("snapshot_price", models.FloatField(default=0)),
                ("trend_up_probability", models.IntegerField(default=0)),
                ("trend_sideways_probability", models.IntegerField(default=0)),
                ("trend_down_probability", models.IntegerField(default=0)),
                ("trend_summary", models.TextField(blank=True)),
                ("rsi_analysis", models.TextField(blank=True)),
                ("rsi_support_trend", models.CharField(blank=True, max_length=20)),
                ("macd_analysis", models.TextField(blank=True)),
                ("macd_support_trend", models.CharField(blank=True, max_length=20)),
                ("bollinger_analysis", models.TextField(blank=True)),
                (
                    "bollinger_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("bias_analysis", models.TextField(blank=True)),
                ("bias_support_trend", models.CharField(blank=True, max_length=20)),
                ("psy_analysis", models.TextField(blank=True)),
                ("psy_support_trend", models.CharField(blank=True, max_length=20)),
                ("dmi_analysis", models.TextField(blank=True)),
                ("dmi_support_trend", models.CharField(blank=True, max_length=20)),
                ("vwap_analysis", models.TextField(blank=True)),
                ("vwap_support_trend", models.CharField(blank=True, max_length=20)),
                ("funding_rate_analysis", models.TextField(blank=True)),
                (
                    "funding_rate_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("exchange_netflow_analysis", models.TextField(blank=True)),
                (
                    "exchange_netflow_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("nupl_analysis", models.TextField(blank=True)),
                ("nupl_support_trend", models.CharField(blank=True, max_length=20)),
                ("mayer_multiple_analysis", models.TextField(blank=True)),
                (
                    "mayer_multiple_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("trading_action", models.CharField(default="等待", max_length=20)),
                ("trading_reason", models.TextField(blank=True)),
                ("entry_price", models.FloatField(default=0)),
                ("stop_loss", models.FloatField(default=0)),
                ("take_profit", models.FloatField(default=0)),
                ("risk_level", models.CharField(default="中", max_length=10)),
                ("risk_score", models.IntegerField(default=50)),
                ("risk_details", models.JSONField(default=list)),
                (
                    "token",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analysis_reports",
                        to="wallets.token",
                    ),
                ),
                (
                    "technical_analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analysis_reports",
                        to="CryptoAnalyst.technicalanalysis",
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "get_latest_by": "timestamp",
            },
        ),
    ]
