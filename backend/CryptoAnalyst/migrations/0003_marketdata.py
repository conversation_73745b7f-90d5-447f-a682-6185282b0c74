# Generated by Django 5.0.2 on 2025-05-09 07:47

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("CryptoAnalyst", "0002_technicalanalysis_analysisreport"),
        ("wallets", "0011_wallet_kadena_chain_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="MarketData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("price", models.FloatField(blank=True, null=True)),
                ("volume_24h", models.FloatField(blank=True, null=True)),
                ("price_change_24h", models.FloatField(blank=True, null=True)),
                ("market_cap", models.FloatField(blank=True, null=True)),
                ("total_volume", models.FloatField(blank=True, null=True)),
                ("high_24h", models.FloatField(blank=True, null=True)),
                ("low_24h", models.FloatField(blank=True, null=True)),
                (
                    "token",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="market_data",
                        to="wallets.token",
                    ),
                ),
            ],
            options={
                "verbose_name": "市场数据",
                "verbose_name_plural": "市场数据",
                "ordering": ["-timestamp"],
                "get_latest_by": "timestamp",
            },
        ),
    ]
