# Generated by Django 5.0.2 on 2025-04-21 13:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("wallets", "0011_wallet_kadena_chain_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="TokenAnalysisData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("price", models.FloatField(blank=True, null=True)),
                ("volume_24h", models.FloatField(blank=True, null=True)),
                ("price_change_24h", models.FloatField(blank=True, null=True)),
                ("fear_greed_index", models.FloatField(blank=True, null=True)),
                (
                    "nupl",
                    models.FloatField(blank=True, help_text="未实现盈亏", null=True),
                ),
                (
                    "exchange_netflow",
                    models.FloatField(blank=True, help_text="交易所净流入", null=True),
                ),
                (
                    "mayer_multiple",
                    models.FloatField(blank=True, help_text="梅耶倍数", null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "token",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analysis_data",
                        to="wallets.token",
                    ),
                ),
            ],
            options={
                "verbose_name": "代币分析数据",
                "verbose_name_plural": "代币分析数据",
            },
        ),
    ]
