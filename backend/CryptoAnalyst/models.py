from django.db import models
from django.utils import timezone
from wallets.models import Token

class TechnicalAnalysis(models.Model):
    """技术分析数据模型"""
    token = models.ForeignKey(Token, on_delete=models.CASCADE, related_name='technical_analyses')
    timestamp = models.DateTimeField(default=timezone.now)
    
    # 基础指标
    rsi = models.FloatField(null=True, blank=True)
    macd_line = models.FloatField(null=True, blank=True)
    macd_signal = models.FloatField(null=True, blank=True)
    macd_histogram = models.FloatField(null=True, blank=True)
    
    # 布林带
    bb_upper = models.FloatField(null=True, blank=True)
    bb_middle = models.FloatField(null=True, blank=True)
    bb_lower = models.FloatField(null=True, blank=True)
    
    # 其他指标
    bias = models.FloatField(null=True, blank=True)
    psy = models.FloatField(null=True, blank=True)
    dmi_plus = models.FloatField(null=True, blank=True)
    dmi_minus = models.FloatField(null=True, blank=True)
    dmi_adx = models.FloatField(null=True, blank=True)
    vwap = models.FloatField(null=True, blank=True)
    
    class Meta:
        verbose_name = "技术分析数据"
        verbose_name_plural = "技术分析数据"
        ordering = ['-timestamp']
        get_latest_by = 'timestamp'

    def __str__(self):
        return f"{self.token.symbol} - {self.timestamp}"

class AnalysisReport(models.Model):
    """分析报告模型 - 存储所有分析结果"""
    token = models.ForeignKey(Token, on_delete=models.CASCADE, related_name='analysis_reports')
    timestamp = models.DateTimeField(default=timezone.now)
    technical_analysis = models.ForeignKey(TechnicalAnalysis, on_delete=models.CASCADE, related_name='analysis_reports')
    snapshot_price = models.FloatField(default=0)  # 添加报告生成时的价格字段
    
    # 趋势分析
    trend_up_probability = models.IntegerField(default=0)  # 上涨概率
    trend_sideways_probability = models.IntegerField(default=0)  # 横盘概率
    trend_down_probability = models.IntegerField(default=0)  # 下跌概率
    trend_summary = models.TextField(blank=True)  # 趋势总结
    
    # 指标分析
    # RSI
    rsi_analysis = models.TextField(blank=True)
    rsi_support_trend = models.CharField(max_length=20, blank=True)
    
    # MACD
    macd_analysis = models.TextField(blank=True)
    macd_support_trend = models.CharField(max_length=20, blank=True)
    
    # 布林带
    bollinger_analysis = models.TextField(blank=True)
    bollinger_support_trend = models.CharField(max_length=20, blank=True)
    
    # BIAS
    bias_analysis = models.TextField(blank=True)
    bias_support_trend = models.CharField(max_length=20, blank=True)
    
    # PSY
    psy_analysis = models.TextField(blank=True)
    psy_support_trend = models.CharField(max_length=20, blank=True)
    
    # DMI
    dmi_analysis = models.TextField(blank=True)
    dmi_support_trend = models.CharField(max_length=20, blank=True)
    
    # VWAP
    vwap_analysis = models.TextField(blank=True)
    vwap_support_trend = models.CharField(max_length=20, blank=True)
    
    # 资金费率
    funding_rate_analysis = models.TextField(blank=True)
    funding_rate_support_trend = models.CharField(max_length=20, blank=True)
    
    # 交易所净流入
    exchange_netflow_analysis = models.TextField(blank=True)
    exchange_netflow_support_trend = models.CharField(max_length=20, blank=True)
    
    # NUPL
    nupl_analysis = models.TextField(blank=True)
    nupl_support_trend = models.CharField(max_length=20, blank=True)
    
    # Mayer Multiple
    mayer_multiple_analysis = models.TextField(blank=True)
    mayer_multiple_support_trend = models.CharField(max_length=20, blank=True)
    
    # 交易建议
    trading_action = models.CharField(max_length=20, default='等待')  # 买入/卖出/持有
    trading_reason = models.TextField(blank=True)  # 建议原因
    entry_price = models.FloatField(default=0)  # 入场价格
    stop_loss = models.FloatField(default=0)  # 止损价格
    take_profit = models.FloatField(default=0)  # 止盈价格
    
    # 风险评估
    risk_level = models.CharField(max_length=10, default='中')  # 高/中/低
    risk_score = models.IntegerField(default=50)  # 0-100
    risk_details = models.JSONField(default=list)  # 风险详情列表
    
    class Meta:
        ordering = ['-timestamp']
        get_latest_by = 'timestamp'
        
    def __str__(self):
        return f"{self.token.symbol} - {self.timestamp}"

class TokenAnalysisData(models.Model):
    """代币分析数据模型"""
    token = models.OneToOneField(Token, on_delete=models.CASCADE, related_name='analysis_data')
    price = models.FloatField(null=True, blank=True)
    volume_24h = models.FloatField(null=True, blank=True)
    price_change_24h = models.FloatField(null=True, blank=True)
    fear_greed_index = models.FloatField(null=True, blank=True)
    nupl = models.FloatField(null=True, blank=True, help_text="未实现盈亏")
    exchange_netflow = models.FloatField(null=True, blank=True, help_text="交易所净流入")
    mayer_multiple = models.FloatField(null=True, blank=True, help_text="梅耶倍数")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "代币分析数据"
        verbose_name_plural = "代币分析数据"

    def __str__(self):
        return f"{self.token.symbol} 分析数据"

    def to_json_data(self):
        """转换为JSON数据"""
        return {
            'price': self.price,
            'volume_24h': self.volume_24h,
            'price_change_24h': self.price_change_24h,
            'fear_greed_index': self.fear_greed_index,
            'nupl': self.nupl,
            'exchange_netflow': self.exchange_netflow,
            'mayer_multiple': self.mayer_multiple,
            'updated_at': self.updated_at.isoformat()
        }

class MarketData(models.Model):
    """市场数据模型"""
    token = models.ForeignKey(Token, on_delete=models.CASCADE, related_name='market_data')
    timestamp = models.DateTimeField(default=timezone.now)
    price = models.FloatField(null=True, blank=True)
    volume_24h = models.FloatField(null=True, blank=True)
    price_change_24h = models.FloatField(null=True, blank=True)
    market_cap = models.FloatField(null=True, blank=True)
    total_volume = models.FloatField(null=True, blank=True)
    high_24h = models.FloatField(null=True, blank=True)
    low_24h = models.FloatField(null=True, blank=True)
    
    class Meta:
        verbose_name = "市场数据"
        verbose_name_plural = "市场数据"
        ordering = ['-timestamp']
        get_latest_by = 'timestamp'
        
    def __str__(self):
        return f"{self.token.symbol} - {self.timestamp}" 