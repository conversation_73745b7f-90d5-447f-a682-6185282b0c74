import logging
from typing import Dict, Optional
from .okx_api import OKXAPI

logger = logging.getLogger(__name__)

class MarketDataService:
    """市场数据服务类"""
    
    def __init__(self):
        """初始化市场数据服务"""
        self.okx_api = OKXAPI()
    
    def get_market_data(self, symbol: str) -> Optional[Dict]:
        """获取代币的市场数据
        
        Args:
            symbol: 代币符号，例如 'BTC'
            
        Returns:
            Dict: 包含市场数据的字典，如果获取失败则返回 None
        """
        try:
            # 获取实时价格
            price = self.okx_api.get_realtime_price(symbol)
            if not price:
                logger.error(f"获取{symbol}实时价格失败")
                return None
            
            # 获取24小时行情
            ticker = self.okx_api.get_ticker(symbol)
            if not ticker:
                logger.error(f"获取{symbol}24小时行情失败")
                return None
            
            # 获取资金费率
            funding_rate = self.okx_api.get_funding_rate(symbol)
            
            # 构建市场数据
            market_data = {
                'price': float(price),
                'volume_24h': float(ticker.get('vol24h', 0)),
                'price_change_24h': float(ticker.get('priceChange24h', 0)),
                'high_24h': float(ticker.get('high24h', 0)),
                'low_24h': float(ticker.get('low24h', 0)),
                'funding_rate': float(funding_rate) if funding_rate else 0.0,
                'last_updated': ticker.get('timestamp', '')
            }
            
            return market_data
            
        except Exception as e:
            logger.error(f"获取{symbol}市场数据时发生错误: {str(e)}")
            return None
    
    def get_global_metrics(self) -> Optional[Dict]:
        """获取加密货币市场整体指标
        
        Returns:
            Dict: 包含市场整体指标的字典，如果获取失败则返回 None
        """
        try:
            # 获取市场整体指标
            metrics = self.okx_api.get_market_metrics()
            if not metrics:
                logger.error("获取市场整体指标失败")
                return None
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取市场整体指标时发生错误: {str(e)}")
            return None 