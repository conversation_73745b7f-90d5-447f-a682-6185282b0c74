from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.utils import timezone
from asgiref.sync import sync_to_async
from django.db.models import (
    F, Q, Count, Avg, Sum, Case, When, Value,
    CharField, IntegerField, BooleanField,
    ForeignKey, OneToOneField, CASCADE, SET_NULL,
    SET, PROTECT, DO_NOTHING, RESTRICT
)
from django.db.models.functions import Now
from .services.technical_analysis import TechnicalAnalysisService
from .services.token_data_service import TokenDataService
from .services.market_data_service import MarketDataService
from .models import TokenAnalysisData, TechnicalAnalysis, AnalysisReport, MarketData
from wallets.models import Token, Chain
import numpy as np
import logging
import requests
import json
from datetime import datetime
import os
import asyncio
import aiohttp
from typing import Dict, Any, Optional
import traceback

logger = logging.getLogger(__name__)

def format_timestamp(dt: datetime) -> str:
    """格式化时间戳为ISO格式字符串
    
    Args:
        dt: datetime对象
        
    Returns:
        str: ISO格式的时间戳字符串
    """
    return dt.isoformat()

class TokenAnalysisAPIView(APIView):
    """代币分析API视图"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.market_service = MarketDataService()
        self.ta_service = TechnicalAnalysisService()
        self.logger = logging.getLogger(__name__)
    
    def _update_analysis_data(self, token: Token, market_data: Dict) -> None:
        """更新代币分析数据
        
        Args:
            token: Token模型实例
            market_data: 市场数据字典
        """
        try:
            # 获取或创建分析数据记录
            analysis_data, created = TokenAnalysisData.objects.get_or_create(
                token=token,
                defaults={
                    'price': market_data.get('current_price'),
                    'volume_24h': market_data.get('volume_24h'),
                    'price_change_24h': market_data.get('price_change_24h'),
                    'fear_greed_index': market_data.get('fear_greed_index'),
                    'nupl': market_data.get('nupl'),
                    'exchange_netflow': market_data.get('exchange_netflow'),
                    'mayer_multiple': market_data.get('mayer_multiple')
                }
            )
            
            # 如果记录已存在，更新数据
            if not created:
                analysis_data.price = market_data.get('current_price')
                analysis_data.volume_24h = market_data.get('volume_24h')
                analysis_data.price_change_24h = market_data.get('price_change_24h')
                analysis_data.fear_greed_index = market_data.get('fear_greed_index')
                analysis_data.nupl = market_data.get('nupl')
                analysis_data.exchange_netflow = market_data.get('exchange_netflow')
                analysis_data.mayer_multiple = market_data.get('mayer_multiple')
                analysis_data.save()
                
            logger.info(f"成功更新代币 {token.symbol} 的分析数据")
            
        except Exception as e:
            logger.error(f"更新代币分析数据失败: {str(e)}")
            raise
    
    def _parse_analysis(self, analysis_str: str) -> Dict:
        """解析分析结果字符串
        
        Args:
            analysis_str: 分析结果字符串
            
        Returns:
            Dict: 解析后的分析结果
        """
        try:
            if not analysis_str:
                return {'analysis': '', 'support_trend': ''}
                
            # 尝试解析JSON字符串
            if isinstance(analysis_str, str) and analysis_str.startswith('{'):
                try:
                    parsed = json.loads(analysis_str.replace("'", '"'))
                    return {
                        'analysis': parsed.get('analysis', ''),
                        'support_trend': parsed.get('support_trend', '')
                    }
                except json.JSONDecodeError:
                    pass
            
            # 如果不是JSON字符串，直接返回
            return {
                'analysis': analysis_str,
                'support_trend': ''
            }
            
        except Exception as e:
            logger.error(f"解析分析结果时出错: {str(e)}")
            return {'analysis': '', 'support_trend': ''}

    def get(self, request, symbol: str):
        """获取代币分析报告
        
        Args:
            request: HTTP请求对象
            symbol: 代币符号，例如 'BTC'
            
        Returns:
            Response: 包含分析报告的响应
        """
        try:
            # 统一 symbol 格式，去除常见后缀
            clean_symbol = symbol.upper().replace('USDT', '').replace('-PERP', '').replace('_PERP', '').replace('PERP', '')
            
            # 获取最新的分析报告
            try:
                # 获取可能存在的多个Token
                tokens = Token.objects.filter(symbol=clean_symbol)
                
                # 构建OR查询条件
                q_objects = Q()
                for token in tokens:
                    q_objects |= Q(token=token)
                
                # 使用OR查询获取最新报告
                if tokens.exists():
                    latest_report = AnalysisReport.objects.filter(q_objects).latest('timestamp')
                else:
                    return Response({
                        'status': 'error',
                        'message': f"未找到{clean_symbol}的Token记录"
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # 获取技术分析数据
                technical_analysis = latest_report.technical_analysis
                
                # 获取快照价格（分析报告生成时的价格）
                snapshot_price = float(latest_report.snapshot_price)
                
                # 方案3：直接调用市场数据服务获取实时价格
                try:
                    # 初始化市场数据服务
                    market_service = MarketDataService()
                    
                    # 获取实时市场数据
                    real_time_market_data = market_service.get_market_data(clean_symbol)
                    
                    if real_time_market_data and 'price' in real_time_market_data:
                        current_price = float(real_time_market_data['price'])
                    else:
                        # 如果无法获取实时价格，使用数据库中最新的价格记录
                        try:
                            # 构建OR查询条件
                            market_q_objects = Q()
                            for token in tokens:
                                market_q_objects |= Q(token=token)
                                
                            market_data = MarketData.objects.filter(market_q_objects).latest('timestamp')
                            current_price = float(market_data.price)
                        except MarketData.DoesNotExist:
                            # 最后才使用报告的快照价格
                            current_price = snapshot_price
                
                except Exception as e:
                    # 如果调用市场数据服务失败，使用数据库中的记录或快照价格
                    self.logger.error(f"获取实时价格失败: {str(e)}")
                    try:
                        # 构建OR查询条件
                        market_q_objects = Q()
                        for token in tokens:
                            market_q_objects |= Q(token=token)
                            
                        market_data = MarketData.objects.filter(market_q_objects).latest('timestamp')
                        current_price = float(market_data.price)
                    except MarketData.DoesNotExist:
                        current_price = snapshot_price
                
                # 解析各个指标的分析结果
                def parse_analysis_text(text):
                    """解析分析文本，提取 analysis 和 support_trend"""
                    try:
                        if not text or text.strip() == '':
                            return {'analysis': '', 'support_trend': ''}
                        
                        # 尝试解析JSON
                        if text.startswith('{'):
                            try:
                                data = json.loads(text.replace("'", '"'))
                                return {
                                    'analysis': data.get('analysis', ''),
                                    'support_trend': data.get('support_trend', '')
                                }
                            except json.JSONDecodeError:
                                pass
                        
                        # 如果不是JSON格式，尝试查找常见支持趋势的短语
                        support_phrases = ['支持当前趋势', '反对当前趋势', '中性']
                        for phrase in support_phrases:
                            if phrase in text:
                                parts = text.split(phrase, 1)
                                return {
                                    'analysis': parts[0].strip(),
                                    'support_trend': phrase
                                }
                        
                        # 默认返回整个文本作为分析内容
                        return {'analysis': text, 'support_trend': ''}
                    except Exception as e:
                        logger.error(f"解析分析文本出错: {str(e)}")
                        return {'analysis': text, 'support_trend': ''}
                
                # 解析各指标分析
                rsi_parsed = parse_analysis_text(latest_report.rsi_analysis)
                macd_parsed = parse_analysis_text(latest_report.macd_analysis)
                bollinger_parsed = parse_analysis_text(latest_report.bollinger_analysis)
                bias_parsed = parse_analysis_text(latest_report.bias_analysis)
                psy_parsed = parse_analysis_text(latest_report.psy_analysis)
                dmi_parsed = parse_analysis_text(latest_report.dmi_analysis)
                vwap_parsed = parse_analysis_text(latest_report.vwap_analysis)
                
                # 构建响应数据
                response_data = {
                    'status': 'success',
                    'data': {
                        'symbol': clean_symbol,
                        'timestamp': latest_report.timestamp.isoformat(),
                        'analysis_report': {
                            'trend_analysis': {
                                'probabilities': {
                                    'up': float(latest_report.trend_up_probability) / 100,
                                    'sideways': float(latest_report.trend_sideways_probability) / 100,
                                    'down': float(latest_report.trend_down_probability) / 100
                                },
                                'summary': latest_report.trend_summary
                            },
                            'indicators_analysis': {
                                'RSI': {
                                    'value': float(technical_analysis.rsi) if technical_analysis.rsi is not None else None,
                                    'analysis': rsi_parsed['analysis'],
                                    'support_trend': rsi_parsed['support_trend']
                                },
                                'MACD': {
                                    'value': {
                                        'line': float(technical_analysis.macd_line) if technical_analysis.macd_line is not None else None,
                                        'signal': float(technical_analysis.macd_signal) if technical_analysis.macd_signal is not None else None,
                                        'histogram': float(technical_analysis.macd_histogram) if technical_analysis.macd_histogram is not None else None
                                    },
                                    'analysis': macd_parsed['analysis'],
                                    'support_trend': macd_parsed['support_trend']
                                },
                                'BollingerBands': {
                                    'value': {
                                        'upper': float(technical_analysis.bb_upper) if technical_analysis.bb_upper is not None else None,
                                        'middle': float(technical_analysis.bb_middle) if technical_analysis.bb_middle is not None else None,
                                        'lower': float(technical_analysis.bb_lower) if technical_analysis.bb_lower is not None else None
                                    },
                                    'analysis': bollinger_parsed['analysis'],
                                    'support_trend': bollinger_parsed['support_trend']
                                },
                                'BIAS': {
                                    'value': float(technical_analysis.bias) if technical_analysis.bias is not None else None,
                                    'analysis': bias_parsed['analysis'],
                                    'support_trend': bias_parsed['support_trend']
                                },
                                'PSY': {
                                    'value': float(technical_analysis.psy) if technical_analysis.psy is not None else None,
                                    'analysis': psy_parsed['analysis'],
                                    'support_trend': psy_parsed['support_trend']
                                },
                                'DMI': {
                                    'value': {
                                        'plus_di': float(technical_analysis.dmi_plus) if technical_analysis.dmi_plus is not None else None,
                                        'minus_di': float(technical_analysis.dmi_minus) if technical_analysis.dmi_minus is not None else None,
                                        'adx': float(technical_analysis.dmi_adx) if technical_analysis.dmi_adx is not None else None
                                    },
                                    'analysis': dmi_parsed['analysis'],
                                    'support_trend': dmi_parsed['support_trend']
                                },
                                'VWAP': {
                                    'value': float(technical_analysis.vwap) if technical_analysis.vwap is not None else None,
                                    'analysis': vwap_parsed['analysis'],
                                    'support_trend': vwap_parsed['support_trend']
                                }
                            },
                            'trading_advice': {
                                'action': latest_report.trading_action,
                                'reason': latest_report.trading_reason,
                                'entry_price': float(latest_report.entry_price),
                                'stop_loss': float(latest_report.stop_loss),
                                'take_profit': float(latest_report.take_profit)
                            },
                            'risk_assessment': {
                                'level': latest_report.risk_level,
                                'score': int(latest_report.risk_score),
                                'details': latest_report.risk_details if isinstance(latest_report.risk_details, list) else []
                            },
                            'current_price': current_price,
                            'snapshot_price': snapshot_price,
                            'price_change_percent': locals().get('price_change_percent', 0),
                            'last_update_time': latest_report.timestamp.isoformat()
                        }
                    }
                }
                
                return Response(response_data)
                
            except AnalysisReport.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': f"未找到{clean_symbol}的分析报告，请先调用刷新接口生成分析报告"
                }, status=status.HTTP_404_NOT_FOUND)
            
        except Exception as e:
            self.logger.error(f"获取{clean_symbol}的分析报告时出错: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TechnicalIndicatorsAPIView(APIView):
    """技术指标API视图"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ta_service = TechnicalAnalysisService()
    
    def _sanitize_float(self, value, min_value=-1000000.0, max_value=1000000.0):
        """确保浮点数值在合理范围内
        
        Args:
            value: 要检查的值
            min_value: 最小值
            max_value: 最大值
            
        Returns:
            float: 在范围内的值
        """
        try:
            if value is None:
                return 0.0
                
            float_value = float(value)
            
            # 检查是否为无穷大或NaN
            if not np.isfinite(float_value):
                return 0.0
                
            # 限制数值范围
            return max(min(float_value, max_value), min_value)
            
        except (ValueError, TypeError):
            return 0.0
    
    def _sanitize_indicators(self, indicators):
        """确保所有指标值都在合理范围内
        
        Args:
            indicators: 指标字典
            
        Returns:
            dict: 处理后的指标字典
        """
        try:
            # 处理简单数值
            for key in ['RSI', 'BIAS', 'PSY', 'VWAP', 'ExchangeNetflow', 'NUPL', 'MayerMultiple', 'FundingRate']:
                if key in indicators:
                    indicators[key] = self._sanitize_float(indicators[key])
            
            # 处理MACD
            if 'MACD' in indicators:
                macd = indicators['MACD']
                macd['line'] = self._sanitize_float(macd.get('line'), -10000.0, 10000.0)
                macd['signal'] = self._sanitize_float(macd.get('signal'), -10000.0, 10000.0)
                macd['histogram'] = self._sanitize_float(macd.get('histogram'), -10000.0, 10000.0)
            
            # 处理布林带
            if 'BollingerBands' in indicators:
                bb = indicators['BollingerBands']
                bb['upper'] = self._sanitize_float(bb.get('upper'), 0.0, 1000000.0)
                bb['middle'] = self._sanitize_float(bb.get('middle'), 0.0, 1000000.0)
                bb['lower'] = self._sanitize_float(bb.get('lower'), 0.0, 1000000.0)
            
            # 处理DMI
            if 'DMI' in indicators:
                dmi = indicators['DMI']
                dmi['plus_di'] = self._sanitize_float(dmi.get('plus_di'), 0.0, 100.0)
                dmi['minus_di'] = self._sanitize_float(dmi.get('minus_di'), 0.0, 100.0)
                dmi['adx'] = self._sanitize_float(dmi.get('adx'), 0.0, 100.0)
            
            return indicators
            
        except Exception as e:
            logger.error(f"处理指标数据时出错: {str(e)}")
            return {}
    
    def get(self, request, symbol: str):
        """获取指定交易对的技术指标
        
        Args:
            request: HTTP请求对象
            symbol: 交易对，例如 'BTCUSDT'
            
        Returns:
            Response: 包含技术指标的响应
        """
        try:
            # 统一 symbol 格式，去除常见后缀
            clean_symbol = symbol.upper().replace('USDT', '').replace('-PERP', '').replace('_PERP', '').replace('PERP', '')
            
            # 获取查询参数
            interval = request.query_params.get('interval', '1d')
            limit = int(request.query_params.get('limit', 100))
            
            # 获取技术指标
            result = self.ta_service.get_all_indicators(
                symbol=clean_symbol,
                interval=interval,
                limit=limit
            )
            
            if result['status'] == 'error':
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 处理指标数据
            if 'data' in result and 'indicators' in result['data']:
                result['data']['indicators'] = self._sanitize_indicators(result['data']['indicators'])
                
            return Response(result)
            
        except Exception as e:
            logger.error(f"获取技术指标失败: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TokenDataAPIView(APIView):
    """代币数据API视图"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.token_service = TokenDataService()  # 不传入API密钥，使用免费API
    
    def get(self, request, token_id: str):
        """获取指定代币的数据
        
        Args:
            request: HTTP请求对象
            token_id: 代币ID，例如 'bitcoin'
            
        Returns:
            Response: 包含代币数据的响应
        """
        try:
            # 获取代币数据
            token_data = self.token_service.get_token_data(token_id)
            
            return Response({
                'status': 'success',
                'data': token_data
            })
            
        except Exception as e:
            logger.error(f"获取代币数据失败: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class RefreshAnalysisReportView(APIView):
    """刷新分析报告视图"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.market_service = MarketDataService()
        self.ta_service = TechnicalAnalysisService()
        self.token_service = TokenDataService()
        self.logger = logging.getLogger(__name__)
        self._init_coze_api()
    
    def _init_coze_api(self):
        """初始化 Coze API 配置"""
        # 从 settings 中读取配置
        self.coze_api_key = settings.COZE_API_KEY
        self.coze_bot_id = settings.COZE_BOT_ID
        self.coze_api_url = settings.COZE_API_URL
        
        # 检查必要的配置
        if not self.coze_api_key:
            logger.warning("COZE_API_KEY 未在 settings 中设置，将使用默认分析报告")
        if not self.coze_bot_id:
            logger.warning("COZE_BOT_ID 未在 settings 中设置，使用默认值")
        if not self.coze_api_url:
            logger.warning("COZE_API_URL 未在 settings 中设置，使用默认值")
    
    def _update_analysis_data(self, token: Token, indicators: Dict, current_price: float) -> Dict:
        """更新代币分析数据
        
        Args:
            token: Token模型实例
            indicators: 技术指标数据
            current_price: 当前价格
            
        Returns:
            Dict: 更新后的分析数据
        """
        try:
            # 获取或创建分析数据记录
            analysis_data, created = TokenAnalysisData.objects.get_or_create(
                token=token,
                defaults={
                    'price': current_price,
                    'volume_24h': indicators.get('volume_24h', 0),
                    'price_change_24h': indicators.get('price_change_24h', 0),
                    'fear_greed_index': indicators.get('fear_greed_index', 0),
                    'nupl': indicators.get('nupl', 0),
                    'exchange_netflow': indicators.get('exchange_netflow', 0),
                    'mayer_multiple': indicators.get('mayer_multiple', 0)
                }
            )
            
            # 如果记录已存在，更新数据
            if not created:
                analysis_data.price = current_price
                analysis_data.volume_24h = indicators.get('volume_24h', 0)
                analysis_data.price_change_24h = indicators.get('price_change_24h', 0)
                analysis_data.fear_greed_index = indicators.get('fear_greed_index', 0)
                analysis_data.nupl = indicators.get('nupl', 0)
                analysis_data.exchange_netflow = indicators.get('exchange_netflow', 0)
                analysis_data.mayer_multiple = indicators.get('mayer_multiple', 0)
                analysis_data.save()
            
            # 返回更新后的数据
            return {
                'price': current_price,
                'volume_24h': indicators.get('volume_24h', 0),
                'price_change_24h': indicators.get('price_change_24h', 0),
                'fear_greed_index': indicators.get('fear_greed_index', 0),
                'nupl': indicators.get('nupl', 0),
                'exchange_netflow': indicators.get('exchange_netflow', 0),
                'mayer_multiple': indicators.get('mayer_multiple', 0)
            }
            
        except Exception as e:
            self.logger.error(f"更新代币分析数据失败: {str(e)}")
            raise

    async def _get_coze_analysis(self, symbol: str, indicators: Dict, technical_analysis: Dict) -> Dict:
        """异步获取 Coze 分析报告"""
        try:
            logger.info("=== 开始获取 Coze 分析报告 ===")
            # 获取市场数据
            market_data = await sync_to_async(self.market_service.get_market_data)(symbol)
            if not market_data:
                logger.error(f"获取市场数据失败: {symbol}")
                return None

            logger.info(f"获取到市场数据: {market_data}")

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.coze_api_key}",
                "Content-Type": "application/json",
                "Accept": "*/*",
                "Connection": "keep-alive"
            }

            # 构建请求体
            request_data = {
                "technical_indicators": {
                    "symbol": symbol,
                    "interval": "1d",
                    "timestamp": format_timestamp(timezone.now()),
                    "indicators": indicators
                },
                "market_data": {
                    "price": market_data['price']
                }
            }
            
            logger.info(f"准备发送到 Coze 的请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")

            additional_messages = [{
                "role": "user",
                "content": json.dumps(request_data, ensure_ascii=False),
                "content_type": "text"
            }]

            payload = {
                "bot_id": self.coze_bot_id,
                "user_id": "crypto_user_001",
                "stream": False,
                "auto_save_history": True,
                "additional_messages": additional_messages
            }

            # 设置超时
            timeout = aiohttp.ClientTimeout(total=30)

            # 发送请求创建对话
            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    logger.info("开始发送请求到 Coze API")
                    async with session.post(
                        f"{self.coze_api_url}/v3/chat",
                        headers=headers,
                        json=payload
                    ) as response:
                        response_text = await response.text()
                        logger.info(f"Coze API 初始响应: {response_text}")

                        if response.status != 200:
                            logger.error(f"Coze API请求失败: {response_text}")
                            return None

                        response_data = json.loads(response_text)
                        if response_data.get('code') != 0:
                            logger.error(f"Coze API响应错误: {response_data}")
                            return None

                        data = response_data.get('data', {})
                        chat_id = data.get('id')
                        conversation_id = data.get('conversation_id')

                        if not chat_id or not conversation_id:
                            logger.error("创建对话响应中缺少必要的ID")
                            return None

                        logger.info(f"成功创建对话，chat_id: {chat_id}, conversation_id: {conversation_id}")

                        # 轮询获取对话结果
                        max_retries = 20
                        retry_count = 0
                        retry_interval = 1  # 初始重试间隔（秒）

                        while retry_count < max_retries:
                            try:
                                # 构建获取对话状态的请求
                                retrieve_url = f"{self.coze_api_url}/v3/chat/retrieve"
                                retrieve_params = {
                                    "bot_id": self.coze_bot_id,
                                    "chat_id": chat_id,
                                    "conversation_id": conversation_id
                                }

                                logger.info(f"第 {retry_count + 1} 次尝试获取对话状态")

                                async with session.get(retrieve_url, headers=headers, params=retrieve_params) as status_response:
                                    status_text = await status_response.text()
                                    logger.info(f"状态响应: {status_text}")

                                    if status_response.status == 200:
                                        status_data = json.loads(status_text)
                                        if status_data.get('code') == 0:
                                            data = status_data.get('data', {})
                                            status = data.get('status')

                                            if status == "completed":
                                                logger.info("对话已完成，开始获取消息列表")
                                                # 获取消息列表
                                                message_list_url = f"{self.coze_api_url}/v3/chat/message/list"
                                                message_list_params = {
                                                    "bot_id": self.coze_bot_id,
                                                    "chat_id": chat_id,
                                                    "conversation_id": conversation_id
                                                }

                                                async with session.get(message_list_url, headers=headers, params=message_list_params) as messages_response:
                                                    messages_text = await messages_response.text()
                                                    logger.info(f"消息列表响应: {messages_text}")

                                                    if messages_response.status == 200:
                                                        messages_data = json.loads(messages_text)
                                                        if messages_data.get('code') == 0:
                                                            # 处理消息列表数据
                                                            if "data" in messages_data and isinstance(messages_data["data"], dict) and "messages" in messages_data["data"]:
                                                                messages = messages_data["data"]["messages"]
                                                            elif "data" in messages_data and isinstance(messages_data["data"], list):
                                                                messages = messages_data["data"]
                                                            else:
                                                                logger.error("无法解析消息列表格式")
                                                                return None

                                                            logger.info(f"找到 {len(messages)} 条消息")

                                                            # 查找助手的回复
                                                            for message in messages:
                                                                if message.get('role') == 'assistant' and message.get('type') == 'answer':
                                                                    content = message.get('content', '')
                                                                    logger.info(f"找到助手回复: {content}")
                                                                    
                                                                    if content and content != '###':
                                                                        try:
                                                                            if content.startswith('```json'):
                                                                                content = content[7:-3].strip()
                                                                            analysis_data = json.loads(content)
                                                                            
                                                                            # 打印原始分析报告
                                                                            logger.info("=== Coze 原始分析报告 ===")
                                                                            logger.info(json.dumps(analysis_data, ensure_ascii=False, indent=2))
                                                                            logger.info("========================")

                                                                            # 转换数据格式
                                                                            formatted_data = {
                                                                                'trend_up_probability': analysis_data.get('trend_analysis', {}).get('probabilities', {}).get('up', 0),
                                                                                'trend_sideways_probability': analysis_data.get('trend_analysis', {}).get('probabilities', {}).get('sideways', 0),
                                                                                'trend_down_probability': analysis_data.get('trend_analysis', {}).get('probabilities', {}).get('down', 0),
                                                                                'trend_summary': analysis_data.get('trend_analysis', {}).get('summary', ''),
                                                                                'indicators_analysis': analysis_data.get('indicators_analysis', {}),
                                                                                'trading_action': analysis_data.get('trading_advice', {}).get('action', '等待'),
                                                                                'trading_reason': analysis_data.get('trading_advice', {}).get('reason', ''),
                                                                                'entry_price': float(analysis_data.get('trading_advice', {}).get('entry_price', 0)),
                                                                                'stop_loss': float(analysis_data.get('trading_advice', {}).get('stop_loss', 0)),
                                                                                'take_profit': float(analysis_data.get('trading_advice', {}).get('take_profit', 0)),
                                                                                'risk_level': analysis_data.get('risk_assessment', {}).get('level', '中'),
                                                                                'risk_score': int(analysis_data.get('risk_assessment', {}).get('score', 50)),
                                                                                'risk_details': analysis_data.get('risk_assessment', {}).get('details', [])
                                                                            }

                                                                            logger.info("=== 转换后的数据 ===")
                                                                            logger.info(json.dumps(formatted_data, ensure_ascii=False, indent=2))
                                                                            logger.info("==================")

                                                                            return formatted_data
                                                                        except json.JSONDecodeError as e:
                                                                            logger.error(f"解析JSON失败: {str(e)}")
                                                                            return None

                                # 如果没有获取到完整结果，继续重试
                                await asyncio.sleep(retry_interval)
                                retry_interval = min(retry_interval * 1.5, 5)  # 指数退避，最大5秒
                                retry_count += 1

                            except asyncio.TimeoutError:
                                logger.error("获取对话状态超时")
                                retry_count += 1
                                await asyncio.sleep(retry_interval)
                            except Exception as e:
                                logger.error(f"获取对话状态时发生错误: {str(e)}")
                                retry_count += 1
                                await asyncio.sleep(retry_interval)

                        logger.error("所有重试失败，无法获取对话结果")
                        return None

                except asyncio.TimeoutError:
                    logger.error("Coze API 请求超时")
                    return None
                except aiohttp.ClientError as e:
                    logger.error(f"Coze API 请求错误: {str(e)}")
                    return None

        except Exception as e:
            logger.error(f"获取Coze分析时发生错误: {str(e)}")
            return None

    async def _test_coze_auth(self) -> bool:
        """测试Coze API认证"""
        try:
            url = f"{self.coze_api_url}/v3/chat"

            # 设置请求头
            headers = {
                "Authorization": f"Bearer {self.coze_api_key}",
                "Content-Type": "application/json",
                "Accept": "*/*",
                "Connection": "keep-alive"
            }

            # 构建最简单的请求体
            payload = {
                "bot_id": self.coze_bot_id,
                "user_id": "crypto_user_001",
                "stream": False,
                "auto_save_history": True,
                "additional_messages": [
                    {
                        "role": "user",
                        "content": "hi",
                        "content_type": "text"
                    }
                ]
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    response_text = await response.text()
                    logger.info("=== 测试认证响应详情 ===")
                    logger.info(f"响应状态码: {response.status}")
                    logger.info(f"响应头: {dict(response.headers)}")
                    logger.info(f"响应内容: {response_text}")

                    return response.status == 200

        except Exception as e:
            logger.error(f"测试认证失败: {str(e)}")
            return False

    def _create_default_analysis(self, indicators: Dict, current_price: float) -> Dict:
        """创建默认分析报告
        
        Args:
            indicators: 技术指标数据
            current_price: 当前价格
            
        Returns:
            Dict: 默认分析报告数据
        """
        try:
            # 基于技术指标创建默认分析
            rsi = indicators.get('RSI', 50)
            macd = indicators.get('MACD', {})
            bb = indicators.get('BollingerBands', {})
            
            # 计算趋势概率
            trend_up = 0.33
            trend_sideways = 0.34
            trend_down = 0.33
            
            # 根据RSI调整趋势概率
            if rsi > 70:
                trend_up = 0.2
                trend_sideways = 0.3
                trend_down = 0.5
            elif rsi < 30:
                trend_up = 0.5
                trend_sideways = 0.3
                trend_down = 0.2
            
            # 根据MACD调整趋势概率
            if macd and 'histogram' in macd:
                if macd['histogram'] > 0:
                    trend_up += 0.1
                    trend_down -= 0.1
                else:
                    trend_up -= 0.1
                    trend_down += 0.1
            
            # 根据布林带调整趋势概率
            if bb and 'upper' in bb and 'lower' in bb:
                if current_price > bb['upper']:
                    trend_up -= 0.1
                    trend_down += 0.1
                elif current_price < bb['lower']:
                    trend_up += 0.1
                    trend_down -= 0.1
            
            # 归一化概率
            total = trend_up + trend_sideways + trend_down
            trend_up /= total
            trend_sideways /= total
            trend_down /= total
            
            # 创建默认分析报告
            return {
                'trend_analysis': {
                    'probabilities': {
                        'up': round(trend_up, 2),
                        'sideways': round(trend_sideways, 2),
                        'down': round(trend_down, 2)
                    },
                    'summary': self._generate_trend_summary(trend_up, trend_sideways, trend_down)
                },
                'indicators_analysis': {
                    'RSI': self._analyze_rsi(rsi),
                    'MACD': self._analyze_macd(macd),
                    'BollingerBands': self._analyze_bollinger_bands(bb, current_price)
                },
                'trading_advice': {
                    'action': self._get_trading_action(trend_up, trend_down),
                    'reason': self._get_trading_reason(indicators),
                    'entry_price': current_price,
                    'stop_loss': self._calculate_stop_loss(current_price, trend_down),
                    'take_profit': self._calculate_take_profit(current_price, trend_up)
                },
                'risk_assessment': {
                    'level': self._assess_risk_level(indicators),
                    'score': self._calculate_risk_score(indicators),
                    'details': self._get_risk_details(indicators)
                }
            }
            
        except Exception as e:
            self.logger.error(f"创建默认分析报告失败: {str(e)}")
            return self._create_fallback_analysis()
    
    def _generate_trend_summary(self, up: float, sideways: float, down: float) -> str:
        """生成趋势摘要"""
        if up > 0.5:
            return "当前市场呈现上升趋势，但需注意可能的回调风险"
        elif down > 0.5:
            return "当前市场呈现下降趋势，建议谨慎操作"
        else:
            return "市场处于盘整状态，建议观望"
    
    def _analyze_rsi(self, rsi: float) -> str:
        """分析RSI指标"""
        if rsi > 70:
            return "RSI显示超买，可能存在回调风险"
        elif rsi < 30:
            return "RSI显示超卖，可能存在反弹机会"
        else:
            return "RSI处于中性区间，市场相对稳定"
    
    def _analyze_macd(self, macd: Dict) -> str:
        """分析MACD指标"""
        if not macd:
            return "MACD数据不足"
        
        if macd.get('histogram', 0) > 0:
            return "MACD柱状图为正，显示上升动能"
        else:
            return "MACD柱状图为负，显示下降动能"
    
    def _analyze_bollinger_bands(self, bb: Dict, current_price: float) -> str:
        """分析布林带指标"""
        if not bb or 'upper' not in bb or 'lower' not in bb:
            return "布林带数据不足"
        
        if current_price > bb['upper']:
            return "价格突破上轨，可能存在超买风险"
        elif current_price < bb['lower']:
            return "价格突破下轨，可能存在超卖机会"
        else:
            return "价格在布林带通道内运行，市场相对稳定"
    
    def _get_trading_action(self, trend_up: float, trend_down: float) -> str:
        """获取交易建议"""
        if trend_up > 0.6:
            return "考虑买入"
        elif trend_down > 0.6:
            return "考虑卖出"
        else:
            return "观望"
    
    def _get_trading_reason(self, indicators: Dict) -> str:
        """获取交易理由"""
        reasons = []
        
        rsi = indicators.get('RSI', 50)
        if rsi > 70:
            reasons.append("RSI超买")
        elif rsi < 30:
            reasons.append("RSI超卖")
            
        macd = indicators.get('MACD', {})
        if macd and macd.get('histogram', 0) > 0:
            reasons.append("MACD柱状图为正")
        elif macd and macd.get('histogram', 0) < 0:
            reasons.append("MACD柱状图为负")
            
        return "，".join(reasons) if reasons else "技术指标中性"
    
    def _calculate_stop_loss(self, current_price: float, trend_down: float) -> float:
        """计算止损价格"""
        if trend_down > 0.5:
            return round(current_price * 0.95, 2)  # 5%止损
        else:
            return round(current_price * 0.97, 2)  # 3%止损
    
    def _calculate_take_profit(self, current_price: float, trend_up: float) -> float:
        """计算止盈价格"""
        if trend_up > 0.5:
            return round(current_price * 1.1, 2)  # 10%止盈
        else:
            return round(current_price * 1.05, 2)  # 5%止盈
    
    def _assess_risk_level(self, indicators: Dict) -> str:
        """评估风险等级"""
        risk_score = self._calculate_risk_score(indicators)
        if risk_score > 70:
            return "高"
        elif risk_score > 40:
            return "中"
        else:
            return "低"
    
    def _calculate_risk_score(self, indicators: Dict) -> int:
        """计算风险分数"""
        score = 50  # 基础分数
        
        # 根据RSI调整分数
        rsi = indicators.get('RSI', 50)
        if rsi > 70 or rsi < 30:
            score += 20
            
        # 根据MACD调整分数
        macd = indicators.get('MACD', {})
        if macd and abs(macd.get('histogram', 0)) > 0.5:
            score += 10
            
        # 根据布林带调整分数
        bb = indicators.get('BollingerBands', {})
        if bb and 'upper' in bb and 'lower' in bb:
            if bb['upper'] - bb['lower'] > bb['middle'] * 0.1:  # 带宽过大
                score += 10
                
        return min(max(score, 0), 100)  # 确保分数在0-100之间
    
    def _get_risk_details(self, indicators: Dict) -> list:
        """获取风险详情"""
        details = []
        
        rsi = indicators.get('RSI', 50)
        if rsi > 70:
            details.append("RSI显示超买，存在回调风险")
        elif rsi < 30:
            details.append("RSI显示超卖，存在反弹机会")
            
        macd = indicators.get('MACD', {})
        if macd and abs(macd.get('histogram', 0)) > 0.5:
            details.append("MACD显示强烈趋势信号")
            
        return details
    
    def _create_fallback_analysis(self) -> Dict:
        """创建降级分析报告"""
        return {
            'trend_analysis': {
                'probabilities': {
                    'up': 0.33,
                    'sideways': 0.34,
                    'down': 0.33
                },
                'summary': "数据不足，无法进行准确分析"
            },
            'indicators_analysis': {
                'RSI': "数据不足",
                'MACD': "数据不足",
                'BollingerBands': "数据不足"
            },
            'trading_advice': {
                'action': "观望",
                'reason': "数据不足",
                'entry_price': 0,
                'stop_loss': 0,
                'take_profit': 0
            },
            'risk_assessment': {
                'level': "中",
                'score': 50,
                'details': ["数据不足，无法进行准确的风险评估"]
            }
        }

    def _safe_int(self, val, default=0):
        try:
            return int(float(val))
        except Exception:
            return default

    def _safe_float(self, val, default=0.0):
        try:
            return float(val)
        except Exception:
            return default

    def _save_analysis_report(self, token: Token, technical_data: Dict, market_data: Dict, analysis_data: Dict) -> None:
        """保存分析报告（修正版，支持多种格式）"""
        try:
            # 检测数据格式（优化：同时检查两个关键字段）
            has_trend_analysis = 'trend_analysis' in analysis_data
            has_indicators_analysis = 'indicators_analysis' in analysis_data
            
            # 根据不同格式提取数据
            # 1. 提取趋势分析数据
            if has_trend_analysis:
                # 嵌套格式：从 trend_analysis 提取
                trend_analysis = analysis_data.get('trend_analysis', {})
                probabilities = trend_analysis.get('probabilities', {})
                trend_up = probabilities.get('up')
                trend_sideways = probabilities.get('sideways')
                trend_down = probabilities.get('down')
                trend_summary = trend_analysis.get('summary', '')
            else:
                # 扁平格式：直接提取
                trend_up = analysis_data.get('trend_up_probability')
                trend_sideways = analysis_data.get('trend_sideways_probability')
                trend_down = analysis_data.get('trend_down_probability')
                trend_summary = analysis_data.get('trend_summary', '')
            
            # 2. 提取交易建议
            if 'trading_advice' in analysis_data:
                # 嵌套格式
                trading_advice = analysis_data.get('trading_advice', {})
                trading_action = trading_advice.get('action', '等待')
                trading_reason = trading_advice.get('reason', '')
                entry_price = trading_advice.get('entry_price', 0)
                stop_loss = trading_advice.get('stop_loss', 0)
                take_profit = trading_advice.get('take_profit', 0)
            else:
                # 扁平格式
                trading_action = analysis_data.get('trading_action', '等待')
                trading_reason = analysis_data.get('trading_reason', '')
                entry_price = analysis_data.get('entry_price', 0)
                stop_loss = analysis_data.get('stop_loss', 0)
                take_profit = analysis_data.get('take_profit', 0)
            
            # 3. 提取风险评估
            if 'risk_assessment' in analysis_data:
                # 嵌套格式
                risk_assessment = analysis_data.get('risk_assessment', {})
                risk_level = risk_assessment.get('level', '中')
                risk_score = risk_assessment.get('score', 50)
                risk_details = risk_assessment.get('details', [])
            else:
                # 扁平格式
                risk_level = analysis_data.get('risk_level', '中')
                risk_score = analysis_data.get('risk_score', 50)
                risk_details = analysis_data.get('risk_details', [])
            
            # 4. 无论何种格式，都直接获取 indicators_analysis（如果存在）
            indicators_analysis = analysis_data.get('indicators_analysis', {})
            
            # 保存技术分析数据
            technical_analysis = TechnicalAnalysis.objects.create(
                token=token,
                rsi=self._safe_float(technical_data.get('RSI')),
                macd_line=self._safe_float(technical_data.get('MACD', {}).get('line')),
                macd_signal=self._safe_float(technical_data.get('MACD', {}).get('signal')),
                macd_histogram=self._safe_float(technical_data.get('MACD', {}).get('histogram')),
                bb_upper=self._safe_float(technical_data.get('BollingerBands', {}).get('upper')),
                bb_middle=self._safe_float(technical_data.get('BollingerBands', {}).get('middle')),
                bb_lower=self._safe_float(technical_data.get('BollingerBands', {}).get('lower')),
                bias=self._safe_float(technical_data.get('BIAS')),
                psy=self._safe_float(technical_data.get('PSY')),
                dmi_plus=self._safe_float(technical_data.get('DMI', {}).get('plus_di')),
                dmi_minus=self._safe_float(technical_data.get('DMI', {}).get('minus_di')),
                dmi_adx=self._safe_float(technical_data.get('DMI', {}).get('adx')),
                vwap=self._safe_float(technical_data.get('VWAP'))
            )

            # 指标分析字段提取，保存为完整JSON对象
            def get_indicator_json(indicator):
                # 直接从 indicators_analysis 中提取（如果存在）
                if indicator in indicators_analysis:
                    val = indicators_analysis.get(indicator, {})
                    if isinstance(val, dict):
                        # 确保有基本字段
                        if 'analysis' not in val:
                            val['analysis'] = ''
                        if 'support_trend' not in val:
                            val['support_trend'] = ''
                        return json.dumps(val, ensure_ascii=False)
                
                # 如果不在 indicators_analysis 中，尝试从扁平结构提取
                indicator_key = f"{indicator.lower()}_analysis"
                if indicator_key in analysis_data:
                    analysis_text = analysis_data.get(indicator_key, '')
                    return json.dumps({'analysis': analysis_text, 'support_trend': ''}, ensure_ascii=False)
                
                # 默认值
                return json.dumps({'analysis': '', 'support_trend': ''}, ensure_ascii=False)
                
            # 准备要保存的数据
            report_data = {
                'token': token,
                'technical_analysis': technical_analysis,
                'snapshot_price': self._safe_float(market_data.get('price', 0)),

                # 趋势分析
                'trend_up_probability': self._safe_int(trend_up),
                'trend_sideways_probability': self._safe_int(trend_sideways),
                'trend_down_probability': self._safe_int(trend_down),
                'trend_summary': trend_summary,

                # 指标分析 - 保存完整JSON
                'rsi_analysis': get_indicator_json('RSI'),
                'macd_analysis': get_indicator_json('MACD'),
                'bollinger_analysis': get_indicator_json('BollingerBands'),
                'bias_analysis': get_indicator_json('BIAS'),
                'psy_analysis': get_indicator_json('PSY'),
                'dmi_analysis': get_indicator_json('DMI'),
                'vwap_analysis': get_indicator_json('VWAP'),
                'funding_rate_analysis': get_indicator_json('FundingRate'),
                'exchange_netflow_analysis': get_indicator_json('ExchangeNetflow'),
                'nupl_analysis': get_indicator_json('NUPL'),
                'mayer_multiple_analysis': get_indicator_json('MayerMultiple'),

                # 交易建议
                'trading_action': trading_action,
                'trading_reason': trading_reason,
                'entry_price': self._safe_float(entry_price),
                'stop_loss': self._safe_float(stop_loss),
                'take_profit': self._safe_float(take_profit),

                # 风险评估
                'risk_level': risk_level,
                'risk_score': self._safe_int(risk_score),
                'risk_details': risk_details
            }
            
            # 保存分析报告
            report = AnalysisReport.objects.create(**report_data)
            
            logger.info(f"成功保存{token.symbol}的分析报告（修正版2.0）")

        except Exception as e:
            logger.error(f"保存分析报告失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def post(self, request, symbol: str):
        """刷新代币分析报告
        
        Args:
            request: HTTP请求对象
            symbol: 代币符号，例如 'BTC'
            
        Returns:
            Response: 包含刷新结果的响应
        """
        try:
            # 统一符号格式
            symbol = symbol.upper()
            
            # 清理重复的Token记录
            try:
                duplicate_tokens = Token.objects.filter(symbol=symbol)
                if duplicate_tokens.count() > 1:
                    logger.warning(f"发现{duplicate_tokens.count()}个重复的{symbol}记录，保留最新的一个")
                    # 保留ID最大的那个（最新创建的）
                    latest_token = duplicate_tokens.order_by('-id').first()
                    # 删除其他重复的记录
                    for token in duplicate_tokens:
                        if token.id != latest_token.id:
                            # 检查是否有依赖此Token的数据
                            has_dependencies = False
                            try:
                                # 检查各种可能的依赖
                                if TokenAnalysisData.objects.filter(token=token).exists():
                                    has_dependencies = True
                                elif TechnicalAnalysis.objects.filter(token=token).exists():
                                    has_dependencies = True
                                elif AnalysisReport.objects.filter(token=token).exists():
                                    has_dependencies = True
                                elif MarketData.objects.filter(token=token).exists():
                                    has_dependencies = True
                                
                                if not has_dependencies:
                                    token.delete()
                                    logger.info(f"删除了重复的Token记录: {token.id}")
                            except Exception as e:
                                logger.error(f"检查Token依赖关系时出错: {str(e)}")
            except Exception as e:
                logger.error(f"清理重复Token记录时出错: {str(e)}")
            
            # 获取市场数据
            market_data = self.market_service.get_market_data(symbol)
            if not market_data:
                return Response({
                    'status': 'error',
                    'message': f"无法获取{symbol}的市场数据"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 获取技术指标
            technical_data = self.ta_service.get_all_indicators(symbol)
            if technical_data['status'] == 'error':
                return Response(technical_data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 获取或创建Token记录，使用更安全的方法避免重复
            try:
                # 修改查询逻辑，避免多个Token记录问题
                token = Token.objects.filter(symbol=symbol).first()
                if token:
                    logger.info(f"使用已存在的 Token 记录: {token.id}")
                else:
                    # 如果不存在，创建新的 Chain 和 Token 记录
                    try:
                        # 为每个代币使用对应的原生链，而不是统一用 BTC
                        chain_mapping = {
                            'BTC': {'chain': 'BTC', 'decimals': 8},
                            'ETH': {'chain': 'ETH', 'decimals': 18},
                            'SOL': {'chain': 'SOL', 'decimals': 9},
                            'DOGE': {'chain': 'DOGE', 'decimals': 8},
                            # 添加更多代币映射
                        }
                        
                        chain_info = chain_mapping.get(symbol, {'chain': symbol, 'decimals': 8})
                        
                        # 获取或创建 Chain 记录
                        chain, _ = Chain.objects.get_or_create(
                            chain=chain_info['chain'],
                            defaults={
                                'is_active': True,
                                'is_testnet': False
                            }
                        )
                        
                        # 使用代币特定的地址作为唯一标识，而不是统一用 0x0
                        default_addresses = {
                            'BTC': '0xBTC' + '0' * 34,  # 示例唯一地址
                            'ETH': '0xETH' + '0' * 34,
                            'SOL': '0xSOL' + '0' * 34,
                            'DOGE': '0xDOGE' + '0' * 33,
                            # 添加其他代币
                        }
                        
                        address = default_addresses.get(symbol, f"0x{symbol}" + '0' * (40 - len(symbol)))
                        
                        # 创建 Token 记录
                        token = Token.objects.create(
                            symbol=symbol,
                            chain=chain,
                            name=symbol,
                            address=address,
                            decimals=chain_info['decimals']
                        )
                        logger.info(f"创建新的 Token 记录: {token.id}, 链: {chain.chain}, 地址: {address}")
                    except Exception as e:
                        # 如果创建过程中出错，再次尝试获取（可能是并发创建导致的）
                        logger.warning(f"创建 Token 记录时出错: {str(e)}，尝试再次获取")
                        token = Token.objects.filter(symbol=symbol).first()
                        if not token:
                            # 如果还是不存在，则返回错误
                            logger.error(f"无法创建或获取 Token: {symbol}")
                            return Response({
                                'status': 'error',
                                'message': f"无法创建或获取代币: {symbol}"
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                logger.error(f"获取Token记录时发生错误: {str(e)}")
                return Response({
                    'status': 'error',
                    'message': f"获取Token记录时发生错误: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 更新技术分析数据
            technical_analysis = self._update_analysis_data(token, technical_data['data']['indicators'], market_data['price'])
            
            # 尝试使用Coze API获取分析结果
            try:
                # 如果有Coze API配置，使用异步调用
                analysis_data = None
                
                if hasattr(self, 'coze_api_key') and self.coze_api_key:
                    logger.info(f"准备获取Coze分析: {symbol}")
                    # 借助异步转同步执行
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        # 首先测试认证
                        auth_ok = loop.run_until_complete(self._test_coze_auth())
                        if auth_ok:
                            logger.info("Coze API认证成功，获取分析报告")
                            analysis_data = loop.run_until_complete(
                                self._get_coze_analysis(symbol, technical_data['data']['indicators'], technical_analysis)
                            )
                        else:
                            logger.warning("Coze API认证失败，使用默认分析报告")
                    finally:
                        loop.close()
                
                # 如果没有获取到Coze分析，使用默认分析报告
                if not analysis_data:
                    logger.info("使用默认分析报告")
                    analysis_data = self._create_default_analysis(technical_data['data']['indicators'], float(market_data['price']))
                
                # 保存分析报告
                self._save_analysis_report(token, technical_data['data']['indicators'], market_data, analysis_data)
                
                # 保存市场数据
                try:
                    MarketData.objects.create(
                        token=token,
                        price=float(market_data['price']),
                        volume_24h=float(market_data.get('volume_24h', 0)),
                        price_change_24h=float(market_data.get('price_change_24h', 0)),
                        market_cap=float(market_data.get('market_cap', 0)),
                        total_volume=float(market_data.get('total_volume', 0)),
                        high_24h=float(market_data.get('high_24h', 0)),
                        low_24h=float(market_data.get('low_24h', 0))
                    )
                except Exception as e:
                    logger.warning(f"保存市场数据失败: {str(e)}")
                
                return Response({
                    'status': 'success',
                    'message': f"成功刷新{symbol}的分析报告",
                    'data': {
                        'symbol': symbol,
                        'timestamp': datetime.utcnow().isoformat()
                    }
                })
                
            except Exception as e:
                logger.error(f"保存分析报告时发生错误: {str(e)}")
                return Response({
                    'status': 'error',
                    'message': f"保存分析报告失败: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        except Exception as e:
            self.logger.error(f"刷新{symbol}的分析报告时出错: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 