#!/usr/bin/env python3
"""
找出 KDA 余额在哪个平行链上
"""

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed


def check_balance_on_chain(address: str, chain_id: int) -> dict:
    """检查特定链上的余额"""
    try:
        # 构建查询 URL
        url = f"https://api.chainweb.com/chainweb/0.0/mainnet01/chain/{chain_id}/pact/api/v1/local"

        # 构建查询数据 - 使用最简单的格式
        creation_time = int(time.time())
        payload = {
            "networkId": "mainnet01",
            "payload": {
                "exec": {
                    "data": {},
                    "code": f'(coin.get-balance "{address}")'
                }
            },
            "signers": [],
            "meta": {
                "creationTime": creation_time,
                "ttl": 7200,
                "gasLimit": 100000,
                "chainId": str(chain_id),
                "gasPrice": 1.0e-7,
                "sender": ""
            },
            "nonce": f"balance-check-{chain_id}-{creation_time}"
        }

        # 使用正确的 Blake2b 哈希计算
        import hashlib
        import base64

        # 使用与 SDK 相同的序列化方法
        payload_str = json.dumps(payload, separators=(',', ':'))

        # 计算 Blake2b 哈希并转换为 Base64URL
        blake2b_hash = hashlib.blake2b(payload_str.encode('utf-8'), digest_size=32)
        hash_bytes = blake2b_hash.digest()
        base64_hash = base64.urlsafe_b64encode(hash_bytes).decode('ascii')
        payload_hash = base64_hash.rstrip('=')

        query_data = {
            "hash": payload_hash,
            "sigs": [],
            "cmd": json.dumps(payload, separators=(',', ':'))
        }

        # 发送请求
        response = requests.post(url, json=query_data, timeout=10)

        # 调试信息
        if chain_id == 0:  # 只为链 0 打印调试信息
            print(f"[DEBUG] 链 {chain_id} 响应状态: {response.status_code}")
            if response.status_code != 200:
                print(f"[DEBUG] 链 {chain_id} 错误响应: {response.text[:200]}")

        if response.status_code == 200:
            result = response.json()

            if 'result' in result and 'status' in result['result']:
                if result['result']['status'] == 'success':
                    balance = float(result['result']['data'])
                    return {
                        'chain_id': chain_id,
                        'balance': balance,
                        'status': 'success',
                        'has_balance': balance > 0
                    }
                else:
                    return {
                        'chain_id': chain_id,
                        'balance': 0,
                        'status': 'no_account',
                        'has_balance': False,
                        'error': result['result'].get('error', 'Account not found')
                    }
            else:
                return {
                    'chain_id': chain_id,
                    'balance': 0,
                    'status': 'error',
                    'has_balance': False,
                    'error': 'Invalid response format'
                }
        else:
            return {
                'chain_id': chain_id,
                'balance': 0,
                'status': 'error',
                'has_balance': False,
                'error': f'HTTP {response.status_code}'
            }

    except Exception as e:
        return {
            'chain_id': chain_id,
            'balance': 0,
            'status': 'error',
            'has_balance': False,
            'error': str(e)
        }


def find_kda_balance(address: str):
    """并行查询所有链，找出 KDA 余额"""

    print(f"🔍 查找 KDA 余额位置")
    print(f"   账户: {address}")
    print(f"   网络: Kadena 主网")
    print(f"   查询范围: 链 0-19")
    print("=" * 60)

    # 并行查询所有 20 条链
    results = []
    chains_with_balance = []
    total_balance = 0

    with ThreadPoolExecutor(max_workers=20) as executor:
        # 提交所有查询任务
        future_to_chain = {
            executor.submit(check_balance_on_chain, address, chain_id): chain_id
            for chain_id in range(20)
        }

        # 处理结果
        for future in as_completed(future_to_chain):
            result = future.result()
            results.append(result)

            chain_id = result['chain_id']
            balance = result['balance']
            status = result['status']

            if balance > 0:
                chains_with_balance.append(result)
                total_balance += balance
                print(f"✅ 链 {chain_id:2d}: {balance:.6f} KDA")
            else:
                if status == 'no_account':
                    print(f"⚪ 链 {chain_id:2d}: 账户不存在")
                elif status == 'error':
                    print(f"❌ 链 {chain_id:2d}: 错误 - {result.get('error', 'Unknown')}")
                else:
                    print(f"⚪ 链 {chain_id:2d}: 0 KDA")

    # 按链 ID 排序结果
    results.sort(key=lambda x: x['chain_id'])
    chains_with_balance.sort(key=lambda x: x['chain_id'])

    print("=" * 60)
    print(f"📊 查询结果汇总:")
    print(f"   总余额: {total_balance:.6f} KDA")
    print(f"   有余额的链数: {len(chains_with_balance)}")

    if chains_with_balance:
        print(f"\n💰 余额分布:")
        for chain_info in chains_with_balance:
            print(f"   链 {chain_info['chain_id']:2d}: {chain_info['balance']:.6f} KDA")

        # 推荐最佳转账链
        best_chain = max(chains_with_balance, key=lambda x: x['balance'])
        print(f"\n🎯 推荐转账链:")
        print(f"   链 ID: {best_chain['chain_id']}")
        print(f"   余额: {best_chain['balance']:.6f} KDA")
        print(f"   可转账金额: 最多 {best_chain['balance'] - 0.001:.6f} KDA (预留 gas 费)")

        return {
            'found': True,
            'total_balance': total_balance,
            'chains_with_balance': chains_with_balance,
            'recommended_chain': best_chain,
            'all_results': results
        }
    else:
        print(f"\n❌ 未找到任何余额")
        print(f"   可能原因:")
        print(f"   1. 账户确实没有余额")
        print(f"   2. 网络连接问题")
        print(f"   3. 账户在测试网而不是主网")

        return {
            'found': False,
            'total_balance': 0,
            'chains_with_balance': [],
            'recommended_chain': None,
            'all_results': results
        }


def test_transfer_on_correct_chain(address: str, chain_id: int, amount: str = "0.01"):
    """在正确的链上测试转账"""

    print(f"\n🧪 测试在链 {chain_id} 上转账")
    print(f"   发送地址: {address}")
    print(f"   接收地址: k:b236608e9856d04d031cb7f5ff78c594c4e3a57bc27a38bbe9941641d31dd7b3")
    print(f"   金额: {amount} KDA")
    print(f"   链 ID: {chain_id}")

    # 构建 curl 命令
    curl_command = f"""curl -X POST 'http://************:8001/api/v1/wallets/1/transfer/' \\
-H 'Content-Type: application/json' \\
-d '{{
  "to_address": "k:b236608e9856d04d031cb7f5ff78c594c4e3a57bc27a38bbe9941641d31dd7b3",
  "amount": "{amount}",
  "payment_password": "123456",
  "device_id": "your-device-id",
  "kadena_chain_id": "{chain_id}"
}}'"""

    print(f"\n📋 使用以下命令测试转账:")
    print(curl_command)

    return curl_command


if __name__ == "__main__":
    # 接收钱包地址
    wallet_address = "k:b236608e9856d04d031cb7f5ff78c594c4e3a57bc27a38bbe9941641d31dd7b3"

    print("🚀 Kadena 余额定位工具")
    print("=" * 60)

    # 查找余额
    result = find_kda_balance(wallet_address)

    if result['found']:
        # 生成测试转账命令
        best_chain = result['recommended_chain']
        test_transfer_on_correct_chain(
            wallet_address,
            best_chain['chain_id'],
            "0.01"
        )

        print(f"\n🎉 找到了你的 KDA！")
        print(f"   请使用上面的命令在正确的链上进行转账。")
    else:
        print(f"\n😕 没有找到 KDA 余额")
        print(f"   请检查账户地址或网络连接。")
