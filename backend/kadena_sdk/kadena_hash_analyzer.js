#!/usr/bin/env node

/**
 * Kadena 官方哈希计算分析器
 * 
 * 使用 Kadena 官方 SDK 来分析正确的哈希计算方法
 */

const { Pact } = require('@kadena/client');
const { sign } = require('@kadena/cryptography-utils');

console.log('🔍 Kadena 官方哈希计算分析器');
console.log('================================');

// 测试数据
const testData = {
    from_address: "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7",
    to_address: "k:test",
    amount: "0.01",
    chain_id: "0",
    network_id: "mainnet01",
    private_key: "your-private-key-here" // 这里需要真实的私钥
};

async function analyzeKadenaHash() {
    try {
        console.log('\n📋 测试数据:');
        console.log(`   发送地址: ${testData.from_address}`);
        console.log(`   接收地址: ${testData.to_address}`);
        console.log(`   转账金额: ${testData.amount}`);
        console.log(`   链 ID: ${testData.chain_id}`);
        console.log(`   网络 ID: ${testData.network_id}`);

        // 1. 使用官方 SDK 构建交易
        console.log('\n🔨 使用官方 SDK 构建交易...');
        
        const pactCode = `(coin.transfer "${testData.from_address}" "${testData.to_address}" ${testData.amount})`;
        console.log(`   Pact 代码: ${pactCode}`);

        // 构建交易
        const transaction = Pact.builder
            .execution(pactCode)
            .addSigner(testData.from_address.replace('k:', ''), (withCapability) => [
                withCapability('coin.TRANSFER', testData.from_address, testData.to_address, testData.amount)
            ])
            .setMeta({
                chainId: testData.chain_id,
                gasLimit: 100000,
                gasPrice: 0.0000001,
                ttl: 7200,
                creationTime: Math.floor(Date.now() / 1000),
                sender: testData.from_address
            })
            .setNetworkId(testData.network_id)
            .createTransaction();

        console.log('\n📦 官方 SDK 生成的交易结构:');
        console.log(JSON.stringify(transaction, null, 2));

        // 2. 分析命令数据
        if (transaction.cmd) {
            console.log('\n🔍 分析命令数据:');
            const cmdData = JSON.parse(transaction.cmd);
            console.log('   命令数据:');
            console.log(JSON.stringify(cmdData, null, 2));

            // 3. 分析哈希计算
            console.log('\n🧮 分析哈希计算:');
            console.log(`   官方计算的哈希: ${transaction.hash}`);
            console.log(`   命令 JSON: ${transaction.cmd}`);

            // 4. 尝试重现哈希计算
            console.log('\n🔬 尝试重现哈希计算:');
            
            // 方法1: 直接对 cmd 字符串计算哈希
            const crypto = require('crypto');
            
            const methods = [
                {
                    name: 'Blake2b-256 直接',
                    hash: crypto.createHash('blake2b512').update(transaction.cmd, 'utf8').digest('hex').substring(0, 64)
                },
                {
                    name: 'SHA256 直接',
                    hash: crypto.createHash('sha256').update(transaction.cmd, 'utf8').digest('hex')
                },
                {
                    name: 'Blake2b-512 截取',
                    hash: crypto.createHash('blake2b512').update(transaction.cmd, 'utf8').digest('hex')
                }
            ];

            methods.forEach(method => {
                console.log(`   ${method.name}: ${method.hash}`);
                console.log(`   匹配官方: ${method.hash === transaction.hash ? '✅' : '❌'}`);
            });

            // 5. 分析 JSON 序列化
            console.log('\n📝 分析 JSON 序列化:');
            
            // 重新序列化命令数据
            const reserializedMethods = [
                {
                    name: '标准序列化',
                    json: JSON.stringify(cmdData, null, 0)
                },
                {
                    name: '紧凑序列化',
                    json: JSON.stringify(cmdData)
                },
                {
                    name: '排序键序列化',
                    json: JSON.stringify(cmdData, Object.keys(cmdData).sort())
                },
                {
                    name: '自定义排序',
                    json: JSON.stringify(cmdData, (key, value) => value, 0)
                }
            ];

            reserializedMethods.forEach(method => {
                console.log(`\n   ${method.name}:`);
                console.log(`   JSON: ${method.json.substring(0, 100)}...`);
                console.log(`   匹配官方: ${method.json === transaction.cmd ? '✅' : '❌'}`);
                
                if (method.json === transaction.cmd) {
                    console.log(`   🎯 找到匹配的序列化方法: ${method.name}`);
                }
            });

            // 6. 输出 Python 实现建议
            console.log('\n🐍 Python 实现建议:');
            console.log('   基于分析结果，Python 实现应该:');
            console.log('   1. 使用与官方相同的 JSON 序列化方法');
            console.log('   2. 使用正确的哈希算法和参数');
            console.log('   3. 确保数据类型和格式完全匹配');

            return {
                official_hash: transaction.hash,
                official_cmd: transaction.cmd,
                cmd_data: cmdData,
                analysis_complete: true
            };
        }

    } catch (error) {
        console.error('\n❌ 分析过程中出错:', error.message);
        console.error('   错误详情:', error);
        return {
            error: error.message,
            analysis_complete: false
        };
    }
}

// 运行分析
analyzeKadenaHash()
    .then(result => {
        console.log('\n✅ 分析完成!');
        if (result.analysis_complete) {
            console.log('   官方哈希已获取，可以用于 Python 实现对比');
        } else {
            console.log('   分析未完成，需要进一步调试');
        }
    })
    .catch(error => {
        console.error('\n💥 分析失败:', error);
    });
