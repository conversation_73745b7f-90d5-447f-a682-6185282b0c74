#!/usr/bin/env node

/**
 * Kadena 加密工具分析器
 * 
 * 分析 @kadena/cryptography-utils 模块的哈希实现
 */

console.log('🔐 Kadena 加密工具分析器');
console.log('========================');

try {
    // 导入加密工具
    const cryptoUtils = require('@kadena/cryptography-utils');
    
    console.log('\n📦 @kadena/cryptography-utils 可用功能:');
    console.log(Object.keys(cryptoUtils));
    
    // 查找哈希相关的函数
    const hashFunctions = Object.keys(cryptoUtils).filter(key => 
        key.toLowerCase().includes('hash') || 
        key.toLowerCase().includes('blake') ||
        key.toLowerCase().includes('sha')
    );
    
    console.log('\n🔍 哈希相关函数:');
    hashFunctions.forEach(func => {
        console.log(`   ${func}: ${typeof cryptoUtils[func]}`);
    });
    
    // 测试可能的哈希函数
    const testData = '{"test": "data"}';
    
    console.log('\n🧪 测试哈希函数:');
    
    // 尝试各种可能的哈希函数
    const possibleHashFunctions = [
        'hash',
        'hashBin',
        'blake2b',
        'blake2bHash',
        'pactHash',
        'commandHash',
        'transactionHash'
    ];
    
    possibleHashFunctions.forEach(funcName => {
        if (cryptoUtils[funcName] && typeof cryptoUtils[funcName] === 'function') {
            try {
                console.log(`\n   测试 ${funcName}:`);
                const result = cryptoUtils[funcName](testData);
                console.log(`   结果: ${result}`);
                console.log(`   类型: ${typeof result}`);
                console.log(`   长度: ${result ? result.length : 'N/A'}`);
            } catch (error) {
                console.log(`   ${funcName} 错误: ${error.message}`);
            }
        } else {
            console.log(`   ${funcName}: 不存在或不是函数`);
        }
    });
    
    // 尝试使用真实的交易数据
    console.log('\n🎯 使用真实交易数据测试:');
    
    const realTransactionCmd = `{"payload":{"exec":{"code":"(coin.transfer \\"k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7\\" \\"k:test\\" 0.01)","data":{}}},"nonce":"kjs:nonce:1748834657816","signers":[{"pubKey":"5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7","scheme":"ED25519","clist":[{"name":"coin.TRANSFER","args":["k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7","k:test","0.01"]}]}],"meta":{"gasLimit":100000,"gasPrice":1e-7,"sender":"k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7","ttl":7200,"creationTime":1748834585,"chainId":"0"},"networkId":"mainnet01"}`;
    
    const expectedHash = "FBsm35EsJIQXZN4MvWFLlLoVKd4JS2EvI1EmmKNBG7s";
    
    possibleHashFunctions.forEach(funcName => {
        if (cryptoUtils[funcName] && typeof cryptoUtils[funcName] === 'function') {
            try {
                const result = cryptoUtils[funcName](realTransactionCmd);
                const matches = result === expectedHash;
                console.log(`   ${funcName}: ${matches ? '✅' : '❌'} ${result ? result.substring(0, 20) + '...' : 'null'}`);
                
                if (matches) {
                    console.log(`   🎯 找到正确的哈希函数: ${funcName}`);
                }
            } catch (error) {
                console.log(`   ${funcName}: ❌ 错误 - ${error.message}`);
            }
        }
    });
    
    // 检查是否有签名相关的函数
    console.log('\n🔑 签名相关函数:');
    const signFunctions = Object.keys(cryptoUtils).filter(key => 
        key.toLowerCase().includes('sign') || 
        key.toLowerCase().includes('ed25519') ||
        key.toLowerCase().includes('key')
    );
    
    signFunctions.forEach(func => {
        console.log(`   ${func}: ${typeof cryptoUtils[func]}`);
        
        // 如果是函数，尝试获取更多信息
        if (typeof cryptoUtils[func] === 'function') {
            try {
                console.log(`   ${func} 参数数量: ${cryptoUtils[func].length}`);
            } catch (e) {
                // 忽略错误
            }
        }
    });
    
    // 尝试查看模块的详细信息
    console.log('\n📋 模块详细信息:');
    console.log(`   模块类型: ${typeof cryptoUtils}`);
    console.log(`   是否为对象: ${typeof cryptoUtils === 'object'}`);
    console.log(`   构造函数: ${cryptoUtils.constructor ? cryptoUtils.constructor.name : 'N/A'}`);
    
    // 尝试查看原型链
    if (cryptoUtils.prototype) {
        console.log(`   原型方法: ${Object.getOwnPropertyNames(cryptoUtils.prototype)}`);
    }
    
} catch (error) {
    console.error('\n❌ 分析 @kadena/cryptography-utils 失败:');
    console.error(`   错误: ${error.message}`);
    console.error(`   堆栈: ${error.stack}`);
}

console.log('\n✅ 加密工具分析完成!');
