#!/usr/bin/env node

/**
 * Kadena 深度分析器
 * 
 * 深入分析 Kadena 官方 SDK 的哈希计算实现
 */

const { Pact } = require('@kadena/client');
const crypto = require('crypto');

console.log('🔬 Kadena 深度分析器');
console.log('===================');

// 创建多个测试用例
const testCases = [
    {
        name: '测试用例 1',
        from: "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7",
        to: "k:test",
        amount: "0.01"
    },
    {
        name: '测试用例 2',
        from: "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7",
        to: "k:receiver",
        amount: "1.0"
    },
    {
        name: '测试用例 3',
        from: "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7",
        to: "k:test",
        amount: "0.001"
    }
];

async function deepAnalysis() {
    console.log('\n📊 分析多个测试用例...');
    
    for (const testCase of testCases) {
        console.log(`\n🧪 ${testCase.name}:`);
        console.log(`   从: ${testCase.from}`);
        console.log(`   到: ${testCase.to}`);
        console.log(`   金额: ${testCase.amount}`);
        
        try {
            // 创建固定时间戳的交易
            const fixedTime = 1748834585;
            
            const pactCode = `(coin.transfer "${testCase.from}" "${testCase.to}" ${testCase.amount})`;
            
            const transaction = Pact.builder
                .execution(pactCode)
                .addSigner(testCase.from.replace('k:', ''), (withCapability) => [
                    withCapability('coin.TRANSFER', testCase.from, testCase.to, testCase.amount)
                ])
                .setMeta({
                    chainId: "0",
                    gasLimit: 100000,
                    gasPrice: 0.0000001,
                    ttl: 7200,
                    creationTime: fixedTime,
                    sender: testCase.from
                })
                .setNetworkId("mainnet01")
                .createTransaction();
            
            console.log(`   官方哈希: ${transaction.hash}`);
            console.log(`   命令长度: ${transaction.cmd.length}`);
            
            // 解析命令数据
            const cmdData = JSON.parse(transaction.cmd);
            
            // 分析数据结构
            console.log(`   Nonce: ${cmdData.nonce}`);
            console.log(`   创建时间: ${cmdData.meta.creationTime}`);
            console.log(`   Gas 价格: ${cmdData.meta.gasPrice}`);
            
            // 尝试手动重建相同的数据结构
            const manualCmd = {
                "payload": {
                    "exec": {
                        "code": pactCode,
                        "data": {}
                    }
                },
                "nonce": cmdData.nonce,
                "signers": [
                    {
                        "pubKey": testCase.from.replace('k:', ''),
                        "scheme": "ED25519",
                        "clist": [
                            {
                                "name": "coin.TRANSFER",
                                "args": [testCase.from, testCase.to, testCase.amount]
                            }
                        ]
                    }
                ],
                "meta": {
                    "gasLimit": 100000,
                    "gasPrice": 1e-7,
                    "sender": testCase.from,
                    "ttl": 7200,
                    "creationTime": fixedTime,
                    "chainId": "0"
                },
                "networkId": "mainnet01"
            };
            
            const manualCmdJson = JSON.stringify(manualCmd);
            console.log(`   手动构建匹配: ${manualCmdJson === transaction.cmd ? '✅' : '❌'}`);
            
            if (manualCmdJson !== transaction.cmd) {
                console.log(`   差异分析:`);
                console.log(`   官方长度: ${transaction.cmd.length}`);
                console.log(`   手动长度: ${manualCmdJson.length}`);
                
                // 找出差异
                for (let i = 0; i < Math.min(transaction.cmd.length, manualCmdJson.length); i++) {
                    if (transaction.cmd[i] !== manualCmdJson[i]) {
                        console.log(`   首个差异位置: ${i}`);
                        console.log(`   官方: "${transaction.cmd.substring(i-5, i+10)}"`);
                        console.log(`   手动: "${manualCmdJson.substring(i-5, i+10)}"`);
                        break;
                    }
                }
            }
            
            // 尝试各种哈希方法
            const hashMethods = [
                {
                    name: 'Blake2b-256',
                    hash: crypto.createHash('blake2b512').update(transaction.cmd, 'utf8').digest().subarray(0, 32).toString('base64')
                },
                {
                    name: 'SHA256',
                    hash: crypto.createHash('sha256').update(transaction.cmd, 'utf8').digest().toString('base64')
                },
                {
                    name: 'Blake2b-512 前32字节',
                    hash: crypto.createHash('blake2b512').update(transaction.cmd, 'utf8').digest().subarray(0, 32).toString('base64')
                }
            ];
            
            hashMethods.forEach(method => {
                const matches = method.hash === transaction.hash;
                console.log(`   ${method.name}: ${matches ? '✅' : '❌'} ${method.hash.substring(0, 20)}...`);
            });
            
        } catch (error) {
            console.error(`   ❌ 错误: ${error.message}`);
        }
    }
    
    // 尝试分析 Kadena SDK 的内部实现
    console.log('\n🔍 尝试分析 SDK 内部实现...');
    
    try {
        // 检查是否可以访问内部哈希函数
        const clientModule = require('@kadena/client');
        console.log('   可用的导出:', Object.keys(clientModule));
        
        // 尝试找到哈希相关的函数
        for (const key of Object.keys(clientModule)) {
            if (key.toLowerCase().includes('hash')) {
                console.log(`   找到哈希相关函数: ${key}`);
            }
        }
        
    } catch (error) {
        console.log(`   无法分析内部实现: ${error.message}`);
    }
    
    console.log('\n💡 分析结论:');
    console.log('   1. 官方 SDK 使用特定的哈希算法');
    console.log('   2. 可能使用了自定义的哈希实现');
    console.log('   3. 需要进一步研究 @kadena/cryptography-utils');
    console.log('   4. 可能需要查看 SDK 源码');
}

// 运行深度分析
deepAnalysis()
    .then(() => {
        console.log('\n✅ 深度分析完成!');
    })
    .catch(error => {
        console.error('\n💥 深度分析失败:', error);
    });
