#!/usr/bin/env python3
"""
检查 Kadena 账户详细信息，包括密钥集配置
"""

import requests
import json
import time
import hashlib
import base64


def check_account_details(address: str, chain_id: int = 1):
    """检查账户详细信息"""
    
    print(f"🔍 检查账户详细信息")
    print(f"   账户: {address}")
    print(f"   链 ID: {chain_id}")
    print("=" * 60)
    
    try:
        # 构建查询 URL
        url = f"https://api.chainweb.com/chainweb/0.0/mainnet01/chain/{chain_id}/pact/api/v1/local"
        
        # 构建查询数据 - 查询账户详情
        creation_time = int(time.time())
        payload = {
            "networkId": "mainnet01",
            "payload": {
                "exec": {
                    "data": {},
                    "code": f'(coin.details "{address}")'
                }
            },
            "signers": [],
            "meta": {
                "creationTime": creation_time,
                "ttl": 7200,
                "gasLimit": 100000,
                "chainId": str(chain_id),
                "gasPrice": 1.0e-7,
                "sender": ""
            },
            "nonce": f"account-details-{chain_id}-{creation_time}"
        }

        # 使用正确的 Blake2b 哈希计算
        payload_str = json.dumps(payload, separators=(',', ':'))
        blake2b_hash = hashlib.blake2b(payload_str.encode('utf-8'), digest_size=32)
        hash_bytes = blake2b_hash.digest()
        base64_hash = base64.urlsafe_b64encode(hash_bytes).decode('ascii')
        payload_hash = base64_hash.rstrip('=')

        query_data = {
            "hash": payload_hash,
            "sigs": [],
            "cmd": json.dumps(payload, separators=(',', ':'))
        }

        # 发送请求
        response = requests.post(url, json=query_data, timeout=10)
        
        print(f"📋 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'result' in result and 'status' in result['result']:
                if result['result']['status'] == 'success':
                    account_data = result['result']['data']
                    
                    print(f"✅ 账户详情获取成功:")
                    print(f"   账户: {account_data.get('account', 'N/A')}")
                    print(f"   余额: {account_data.get('balance', 'N/A')} KDA")
                    print(f"   守卫: {account_data.get('guard', 'N/A')}")
                    
                    # 分析守卫信息
                    guard = account_data.get('guard', {})
                    if isinstance(guard, dict):
                        print(f"\n🔐 守卫配置:")
                        print(f"   类型: {guard.get('pred', 'N/A')}")
                        print(f"   密钥集: {guard.get('keys', 'N/A')}")
                        
                        # 检查密钥集
                        keys = guard.get('keys', [])
                        if keys:
                            print(f"\n🔑 账户密钥:")
                            for i, key in enumerate(keys):
                                print(f"   密钥 {i+1}: {key}")
                                
                            # 检查我们的公钥是否匹配
                            our_pubkey = address.replace("k:", "")
                            if our_pubkey in keys:
                                print(f"\n✅ 我们的公钥在账户密钥列表中")
                            else:
                                print(f"\n❌ 我们的公钥不在账户密钥列表中")
                                print(f"   我们的公钥: {our_pubkey}")
                                print(f"   账户密钥: {keys}")
                        
                        # 检查预测类型
                        pred = guard.get('pred', '')
                        if pred == 'keys-all':
                            print(f"\n📋 权限要求: 需要所有密钥签名")
                        elif pred == 'keys-any':
                            print(f"\n📋 权限要求: 需要任意一个密钥签名")
                        elif pred.startswith('keys-'):
                            print(f"\n📋 权限要求: {pred}")
                        else:
                            print(f"\n📋 权限要求: 自定义守卫 - {pred}")
                    
                    return {
                        'success': True,
                        'account_data': account_data,
                        'guard': guard
                    }
                else:
                    error_msg = result['result'].get('error', {}).get('message', 'Unknown error')
                    print(f"❌ 查询失败: {error_msg}")
                    return {
                        'success': False,
                        'error': error_msg
                    }
            else:
                print(f"❌ 无效的响应格式")
                return {
                    'success': False,
                    'error': 'Invalid response format'
                }
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            return {
                'success': False,
                'error': f'HTTP {response.status_code}'
            }
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def suggest_solution(account_details):
    """根据账户详情建议解决方案"""
    
    print(f"\n🎯 解决方案建议:")
    print("=" * 40)
    
    if not account_details['success']:
        print(f"❌ 无法获取账户详情，无法提供建议")
        return
    
    guard = account_details.get('guard', {})
    keys = guard.get('keys', [])
    pred = guard.get('pred', '')
    
    if not keys:
        print(f"❌ 账户没有配置密钥，这很不寻常")
        return
    
    # 检查我们的公钥
    our_pubkey = "5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    
    if our_pubkey in keys:
        print(f"✅ 我们的公钥在账户密钥列表中")
        
        if pred == 'keys-all' and len(keys) > 1:
            print(f"⚠️ 账户需要所有 {len(keys)} 个密钥签名")
            print(f"   这是多重签名账户，需要所有密钥持有者签名")
            print(f"   解决方案: 获取其他密钥持有者的签名")
        elif pred == 'keys-any':
            print(f"✅ 账户允许任意一个密钥签名")
            print(f"   我们的签名应该足够了")
            print(f"   可能的问题: 签名格式或交易结构")
        else:
            print(f"🔍 账户使用自定义权限: {pred}")
            print(f"   需要进一步分析权限逻辑")
    else:
        print(f"❌ 我们的公钥不在账户密钥列表中")
        print(f"   我们的公钥: {our_pubkey}")
        print(f"   账户密钥: {keys}")
        print(f"   解决方案: 使用正确的私钥/公钥对")
    
    print(f"\n💡 建议:")
    print(f"   1. 确认使用正确的私钥")
    print(f"   2. 检查账户是否为多重签名")
    print(f"   3. 验证签名格式是否正确")
    print(f"   4. 考虑使用不同的权限配置")


if __name__ == "__main__":
    # 你的钱包地址
    wallet_address = "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    
    print("🔍 Kadena 账户详情检查工具")
    print("=" * 60)
    
    # 检查账户详情
    result = check_account_details(wallet_address, chain_id=1)
    
    # 建议解决方案
    suggest_solution(result)
