#!/usr/bin/env node

/**
 * Kadena 哈希桥接器
 * 
 * 为 Python 提供正确的 Kadena 哈希计算服务
 */

const { hash } = require('@kadena/cryptography-utils');

console.log('🌉 Kadena 哈希桥接器');
console.log('==================');

// 从命令行参数获取要哈希的数据
const args = process.argv.slice(2);

if (args.length === 0) {
    console.error('❌ 错误: 需要提供要哈希的 JSON 数据');
    console.error('用法: node kadena_hash_bridge.js <json_data>');
    process.exit(1);
}

const jsonData = args[0];

try {
    console.log('📥 输入数据:');
    console.log(`   JSON: ${jsonData.substring(0, 100)}...`);
    console.log(`   长度: ${jsonData.length}`);
    
    // 使用官方哈希函数
    const hashResult = hash(jsonData);
    
    console.log('\n🧮 哈希计算结果:');
    console.log(`   哈希: ${hashResult}`);
    console.log(`   类型: ${typeof hashResult}`);
    console.log(`   长度: ${hashResult.length}`);
    
    // 输出结果供 Python 使用
    const result = {
        success: true,
        hash: hashResult,
        input_length: jsonData.length,
        hash_length: hashResult.length
    };
    
    console.log('\n📤 输出结果:');
    console.log(JSON.stringify(result));
    
} catch (error) {
    console.error('\n❌ 哈希计算失败:');
    console.error(`   错误: ${error.message}`);
    console.error(`   堆栈: ${error.stack}`);
    
    const result = {
        success: false,
        error: error.message,
        input_length: jsonData.length
    };
    
    console.log('\n📤 错误结果:');
    console.log(JSON.stringify(result));
    
    process.exit(1);
}
