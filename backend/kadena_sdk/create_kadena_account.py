#!/usr/bin/env python3
"""
在 Kadena 链上创建账户
"""

import os
import django
import json
import time

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from kadena_sdk.kadena_pure_python_hash import KadenaPurePythonHash


def create_kadena_account_transaction(target_address: str, initial_amount: str = "0.01",
                                    from_address: str = None, private_key: str = None,
                                    chain_id: str = "1", network_id: str = "mainnet01"):
    """
    创建 Kadena 账户初始化交易
    
    Args:
        target_address: 要创建的账户地址
        initial_amount: 初始转账金额
        from_address: 发送账户地址
        private_key: 发送账户私钥
        chain_id: 链 ID
        network_id: 网络 ID
    
    Returns:
        完整的账户创建交易
    """
    try:
        print(f"🏗️ 创建 Kadena 账户初始化交易")
        print(f"   目标账户: {target_address}")
        print(f"   初始金额: {initial_amount} KDA")
        print(f"   链 ID: {chain_id}")
        
        # 当前时间戳
        current_time = int(time.time())
        
        # 生成 nonce
        nonce = f"kjs:nonce:{int(time.time() * 1000)}"
        
        # 提取公钥
        target_pubkey = target_address.replace("k:", "")
        from_pubkey = from_address.replace("k:", "")
        
        # Pact 代码 - 使用 coin.transfer-create 来创建账户并转账
        pact_code = f'(coin.transfer-create "{from_address}" "{target_address}" (read-keyset "ks") {float(initial_amount)})'
        
        # 构建命令数据
        cmd_data = {
            "payload": {
                "exec": {
                    "code": pact_code,
                    "data": {
                        "ks": {
                            "keys": [target_pubkey],
                            "pred": "keys-all"
                        }
                    }
                }
            },
            "nonce": nonce,
            "signers": [
                {
                    "pubKey": from_pubkey,
                    "scheme": "ED25519",
                    "clist": [
                        {
                            "name": "coin.TRANSFER",
                            "args": [from_address, target_address, float(initial_amount)]
                        },
                        {
                            "name": "coin.GAS",
                            "args": []
                        }
                    ]
                }
            ],
            "meta": {
                "gasLimit": 100000,
                "gasPrice": 1e-7,
                "sender": from_address,
                "ttl": 7200,
                "creationTime": current_time,
                "chainId": chain_id
            },
            "networkId": network_id
        }
        
        # 计算哈希
        correct_hash = KadenaPurePythonHash.calculate_kadena_hash(cmd_data)
        
        # 生成签名
        signature = KadenaPurePythonHash._sign_transaction(correct_hash, private_key)
        
        # 构建完整交易
        signed_tx = KadenaPurePythonHash._build_signed_transaction(cmd_data, correct_hash, signature)
        
        # 验证交易
        validation = {
            'valid': True,
            'message': '账户创建交易验证通过',
            'details': {
                'hash': correct_hash,
                'signature_length': len(signature),
                'pact_code': pact_code,
                'target_account': target_address,
                'initial_amount': initial_amount
            }
        }
        
        return {
            'cmd_data': cmd_data,
            'correct_hash': correct_hash,
            'signature': signature,
            'signed_transaction': signed_tx,
            'validation': validation,
            'success': True,
            'transaction_type': 'account_creation'
        }
        
    except Exception as e:
        print(f"[ERROR] 创建账户交易失败: {str(e)}")
        raise Exception(f"创建账户交易失败: {str(e)}")


def submit_account_creation(target_address: str, initial_amount: str = "0.01"):
    """提交账户创建交易"""
    
    print(f"🚀 提交账户创建交易")
    print("=" * 60)
    
    # 发送账户信息（需要从钱包获取）
    from_address = "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    # 注意：这里需要真实的私钥，这只是示例
    private_key = "your-real-private-key-here"
    
    try:
        # 创建交易
        result = create_kadena_account_transaction(
            target_address=target_address,
            initial_amount=initial_amount,
            from_address=from_address,
            private_key=private_key,
            chain_id="1"
        )
        
        print(f"✅ 账户创建交易构建成功:")
        print(f"   哈希: {result['correct_hash']}")
        print(f"   目标账户: {target_address}")
        print(f"   初始金额: {initial_amount} KDA")
        
        # 显示 Pact 代码
        pact_code = result['cmd_data']['payload']['exec']['code']
        print(f"\n📋 Pact 代码:")
        print(f"   {pact_code}")
        
        # 显示 keyset 配置
        keyset = result['cmd_data']['payload']['exec']['data']['ks']
        print(f"\n🔑 Keyset 配置:")
        print(f"   密钥: {keyset['keys']}")
        print(f"   预测: {keyset['pred']}")
        
        print(f"\n💡 说明:")
        print(f"   这个交易将:")
        print(f"   1. 在链 1 上创建账户 {target_address}")
        print(f"   2. 设置账户的密钥集")
        print(f"   3. 转账 {initial_amount} KDA 作为初始余额")
        
        return result
        
    except Exception as e:
        print(f"❌ 账户创建失败: {str(e)}")
        return None


def explain_kadena_account_system():
    """解释 Kadena 账户系统"""
    
    print(f"\n📚 Kadena 账户系统说明:")
    print("=" * 40)
    
    print(f"🔍 问题原因:")
    print(f"   在 Kadena 中，账户必须先在链上存在才能接收转账")
    print(f"   这与以太坊等其他区块链不同")
    
    print(f"\n🛠️ 解决方案:")
    print(f"   1. 使用 coin.transfer-create 创建账户并转账")
    print(f"   2. 或者先用 coin.create-account 创建账户")
    print(f"   3. 然后再进行正常转账")
    
    print(f"\n🎯 最佳实践:")
    print(f"   1. 检查目标账户是否存在")
    print(f"   2. 如果不存在，使用 transfer-create")
    print(f"   3. 如果存在，使用普通 transfer")
    
    print(f"\n🔧 修复当前问题:")
    print(f"   1. 创建目标账户")
    print(f"   2. 重新进行转账")
    print(f"   3. 或者使用 transfer-create 一步完成")


if __name__ == "__main__":
    print("🏗️ Kadena 账户创建工具")
    print("=" * 60)
    
    # 目标账户
    target_address = "k:b236608e9856d04d031cb7f5ff78c594c4e3a57bc27a38bbe9941641d31dd7b3"
    
    # 解释问题
    explain_kadena_account_system()
    
    # 创建账户交易（演示）
    print(f"\n🧪 演示账户创建交易构建:")
    print("=" * 40)
    
    try:
        # 注意：这里使用测试私钥，实际使用时需要真实私钥
        result = create_kadena_account_transaction(
            target_address=target_address,
            initial_amount="0.01",
            from_address="k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7",
            private_key="test-private-key",  # 测试用
            chain_id="1"
        )
        
        print(f"✅ 账户创建交易构建成功（演示）")
        print(f"   实际使用时需要提供真实私钥")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
    
    print(f"\n" + "=" * 60)
    print(f"说明完成！现在你知道如何解决这个问题了。")
