#!/usr/bin/env python3
"""
调试 Kadena 交易格式，找出 Keyset failure 的原因
"""

import os
import django
import json
import time

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from kadena_sdk.kadena_pure_python_hash import KadenaPurePythonHash


def debug_transaction_format():
    """调试交易格式"""
    
    print("🔍 调试 Kadena 交易格式")
    print("=" * 60)
    
    # 测试参数
    from_address = "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    to_address = "k:b236608e9856d04d031cb7f5ff78c594c4e3a57bc27a38bbe9941641d31dd7b3"
    amount = "0.01"
    private_key = "your-test-private-key"  # 这不是真实私钥，只是为了格式测试
    chain_id = "1"
    
    try:
        # 创建交易
        result = KadenaPurePythonHash.create_kadena_transaction(
            from_address, to_address, amount, private_key, chain_id
        )
        
        # 分析交易结构
        cmd_data = result['cmd_data']
        signed_tx = result['signed_transaction']
        
        print(f"\n📋 交易结构分析:")
        print(f"   哈希: {result['correct_hash']}")
        print(f"   签名长度: {len(result['signature'])}")
        
        # 分析 signers 配置
        signers = cmd_data['signers']
        print(f"\n🔑 Signers 配置:")
        for i, signer in enumerate(signers):
            print(f"   Signer {i}:")
            print(f"     公钥: {signer['pubKey']}")
            print(f"     方案: {signer['scheme']}")
            print(f"     权限: {signer['clist']}")
        
        # 分析签名配置
        sigs = signed_tx['cmds'][0]['sigs']
        print(f"\n✍️ 签名配置:")
        for i, sig in enumerate(sigs):
            print(f"   签名 {i}:")
            print(f"     签名: {sig['sig'][:16]}...")
        
        # 检查签名与 signers 的对应关系
        print(f"\n🔍 签名对应关系:")
        print(f"   Signers 数量: {len(signers)}")
        print(f"   签名数量: {len(sigs)}")
        print(f"   对应关系: {'✅ 匹配' if len(signers) == len(sigs) else '❌ 不匹配'}")
        
        # 分析 capabilities
        print(f"\n🛡️ Capabilities 分析:")
        for i, signer in enumerate(signers):
            clist = signer.get('clist', [])
            print(f"   Signer {i} 权限:")
            for j, cap in enumerate(clist):
                print(f"     权限 {j}: {cap['name']}")
                print(f"     参数: {cap['args']}")
                
                # 检查参数类型
                args = cap['args']
                print(f"     参数类型: {[type(arg).__name__ for arg in args]}")
        
        # 分析 meta 配置
        meta = cmd_data['meta']
        print(f"\n⚙️ Meta 配置:")
        print(f"   发送者: {meta['sender']}")
        print(f"   链 ID: {meta['chainId']}")
        print(f"   Gas 限制: {meta['gasLimit']}")
        print(f"   Gas 价格: {meta['gasPrice']}")
        print(f"   TTL: {meta['ttl']}")
        print(f"   创建时间: {meta['creationTime']}")
        
        # 生成完整的交易 JSON 用于调试
        print(f"\n📄 完整交易 JSON:")
        print(json.dumps(signed_tx, indent=2))
        
        return result
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def suggest_fixes():
    """建议修复方案"""
    
    print(f"\n🎯 可能的修复方案:")
    print("=" * 40)
    
    print(f"1. 🔑 签名顺序问题:")
    print(f"   - 确保签名顺序与 signers 顺序完全匹配")
    print(f"   - Kadena 要求签名按照 signers 数组的顺序提供")
    
    print(f"\n2. 📊 数据类型问题:")
    print(f"   - 金额参数可能需要是数字而不是字符串")
    print(f"   - 检查所有参数的数据类型")
    
    print(f"\n3. 🛡️ Capabilities 问题:")
    print(f"   - 确保 capabilities 名称完全正确")
    print(f"   - 检查 capabilities 参数的格式和类型")
    
    print(f"\n4. 🔐 密钥集问题:")
    print(f"   - 账户可能需要特殊的权限配置")
    print(f"   - 检查账户的守卫配置是否有特殊要求")
    
    print(f"\n5. 🌐 网络问题:")
    print(f"   - 确保使用正确的网络 ID")
    print(f"   - 检查链 ID 是否正确")
    
    print(f"\n💡 建议的调试步骤:")
    print(f"   1. 使用 Kadena 官方工具创建一个成功的交易")
    print(f"   2. 比较我们的交易格式与官方交易格式")
    print(f"   3. 逐个字段检查差异")
    print(f"   4. 特别关注 signers 和 sigs 的对应关系")


def test_different_amount_formats():
    """测试不同的金额格式"""
    
    print(f"\n🧪 测试不同的金额格式:")
    print("=" * 40)
    
    from_address = "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    to_address = "k:test"
    private_key = "test-key"
    
    # 测试不同的金额格式
    amount_formats = [
        ("字符串", "0.01"),
        ("浮点数", 0.01),
        ("整数", 1),
        ("Decimal", "0.01"),  # 这里用字符串表示，实际会转换
    ]
    
    for format_name, amount in amount_formats:
        try:
            print(f"\n测试 {format_name} 格式: {amount} ({type(amount).__name__})")
            
            # 构建命令数据
            cmd_data = KadenaPurePythonHash._build_command_data(
                from_address, to_address, str(amount), "1", "mainnet01"
            )
            
            # 检查 capabilities 中的金额类型
            clist = cmd_data['signers'][0]['clist'][0]
            amount_in_args = clist['args'][2]
            
            print(f"   Capabilities 中的金额: {amount_in_args} ({type(amount_in_args).__name__})")
            
        except Exception as e:
            print(f"   ❌ 失败: {str(e)}")


if __name__ == "__main__":
    print("🔍 Kadena 交易调试工具")
    print("=" * 60)
    
    # 调试交易格式
    result = debug_transaction_format()
    
    # 测试不同金额格式
    test_different_amount_formats()
    
    # 建议修复方案
    suggest_fixes()
    
    print(f"\n" + "=" * 60)
    print(f"调试完成！请根据上述分析调整交易格式。")
