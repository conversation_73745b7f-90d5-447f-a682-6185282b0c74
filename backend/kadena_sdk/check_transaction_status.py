#!/usr/bin/env python3
"""
检查 Kadena 交易状态
"""

import requests
import json
import time


def check_transaction_status(tx_hash: str, chain_id: int = 1):
    """检查交易状态"""
    
    print(f"🔍 检查交易状态")
    print(f"   交易哈希: {tx_hash}")
    print(f"   链 ID: {chain_id}")
    print("=" * 60)
    
    try:
        # 使用 listen API 检查交易状态
        url = f"https://api.chainweb.com/chainweb/0.0/mainnet01/chain/{chain_id}/pact/api/v1/listen"
        
        data = {
            "listen": tx_hash
        }
        
        print(f"📋 发送请求到: {url}")
        print(f"📋 请求数据: {data}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📋 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 交易状态获取成功:")
            print(json.dumps(result, indent=2))
            
            # 分析结果
            if isinstance(result, dict):
                status = result.get('result', {}).get('status')
                if status == 'success':
                    print(f"\n🎉 交易成功确认！")
                    
                    # 检查事件
                    events = result.get('result', {}).get('events', [])
                    print(f"\n📋 交易事件:")
                    for i, event in enumerate(events):
                        print(f"   事件 {i+1}: {event}")
                        
                    return True
                elif status == 'failure':
                    print(f"\n❌ 交易失败！")
                    error = result.get('result', {}).get('error')
                    print(f"   错误: {error}")
                    return False
                else:
                    print(f"\n⏳ 交易状态: {status}")
                    return None
            else:
                print(f"\n⏳ 交易可能还在确认中...")
                return None
                
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def check_account_balance_directly(address: str, chain_id: int = 1):
    """直接检查账户余额"""
    
    print(f"\n🔍 直接检查账户余额")
    print(f"   账户: {address}")
    print(f"   链 ID: {chain_id}")
    print("=" * 40)
    
    try:
        # 使用 local API 直接查询余额
        url = f"https://api.chainweb.com/chainweb/0.0/mainnet01/chain/{chain_id}/pact/api/v1/local"
        
        # 构建查询
        creation_time = int(time.time())
        payload = {
            "networkId": "mainnet01",
            "payload": {
                "exec": {
                    "data": {},
                    "code": f'(coin.get-balance "{address}")'
                }
            },
            "signers": [],
            "meta": {
                "creationTime": creation_time,
                "ttl": 7200,
                "gasLimit": 100000,
                "chainId": str(chain_id),
                "gasPrice": 1.0e-7,
                "sender": ""
            },
            "nonce": f"balance-check-{chain_id}-{creation_time}"
        }

        # 计算哈希
        import hashlib
        import base64
        
        payload_str = json.dumps(payload, separators=(',', ':'))
        blake2b_hash = hashlib.blake2b(payload_str.encode('utf-8'), digest_size=32)
        hash_bytes = blake2b_hash.digest()
        base64_hash = base64.urlsafe_b64encode(hash_bytes).decode('ascii')
        payload_hash = base64_hash.rstrip('=')

        query_data = {
            "hash": payload_hash,
            "sigs": [],
            "cmd": json.dumps(payload, separators=(',', ':'))
        }

        response = requests.post(url, json=query_data, timeout=10)
        
        print(f"📋 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'result' in result and 'status' in result['result']:
                if result['result']['status'] == 'success':
                    balance = float(result['result']['data'])
                    print(f"✅ 账户余额: {balance:.6f} KDA")
                    return balance
                else:
                    error_msg = result['result'].get('error', {}).get('message', 'Unknown error')
                    if "No value found in table coin_coin-table" in error_msg:
                        print(f"⚪ 账户在链 {chain_id} 上不存在")
                    else:
                        print(f"❌ 查询失败: {error_msg}")
                    return 0
            else:
                print(f"❌ 无效的响应格式")
                return 0
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return 0


def analyze_transaction_issue(tx_hash: str, from_address: str, to_address: str, chain_id: int = 1):
    """分析交易问题"""
    
    print(f"\n🔍 分析交易问题")
    print("=" * 40)
    
    # 1. 检查交易状态
    tx_status = check_transaction_status(tx_hash, chain_id)
    
    # 2. 检查发送账户余额
    print(f"\n📊 检查发送账户余额:")
    from_balance = check_account_balance_directly(from_address, chain_id)
    
    # 3. 检查接收账户余额
    print(f"\n📊 检查接收账户余额:")
    to_balance = check_account_balance_directly(to_address, chain_id)
    
    # 4. 分析结果
    print(f"\n🎯 分析结果:")
    print("=" * 20)
    
    if tx_status is True:
        print(f"✅ 交易已确认成功")
        if to_balance > 0:
            print(f"✅ 接收账户有余额: {to_balance:.6f} KDA")
            print(f"🎉 转账完全成功！")
        else:
            print(f"❌ 接收账户余额为 0")
            print(f"🤔 可能的原因:")
            print(f"   1. 账户需要在链上初始化")
            print(f"   2. 余额查询有延迟")
            print(f"   3. 链 ID 配置问题")
    elif tx_status is False:
        print(f"❌ 交易失败")
        print(f"💡 建议检查交易错误信息")
    else:
        print(f"⏳ 交易可能还在确认中")
        print(f"💡 建议等待几分钟后重新检查")
    
    return {
        'tx_status': tx_status,
        'from_balance': from_balance,
        'to_balance': to_balance
    }


if __name__ == "__main__":
    # 交易信息
    tx_hash = "kF-KkzR_bNGfX4QSxgIA8PuOHpKwelUa8yGzQdBqO5I"
    from_address = "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    to_address = "k:b236608e9856d04d031cb7f5ff78c594c4e3a57bc27a38bbe9941641d31dd7b3"
    chain_id = 1
    
    print("🔍 Kadena 交易状态检查工具")
    print("=" * 60)
    
    # 分析交易
    result = analyze_transaction_issue(tx_hash, from_address, to_address, chain_id)
    
    print(f"\n" + "=" * 60)
    print(f"检查完成！")
