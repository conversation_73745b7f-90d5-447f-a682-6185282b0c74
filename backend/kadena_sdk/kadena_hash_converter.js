#!/usr/bin/env node

/**
 * Kadena 哈希转换器
 * 
 * 分析官方哈希的确切计算方法
 */

const crypto = require('crypto');

console.log('🔄 Kadena 哈希转换器');
console.log('===================');

// 官方数据
const officialCmd = `{"payload":{"exec":{"code":"(coin.transfer \\"k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7\\" \\"k:test\\" 0.01)","data":{}}},"nonce":"kjs:nonce:1748834585765","signers":[{"pubKey":"5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7","scheme":"ED25519","clist":[{"name":"coin.TRANSFER","args":["k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7","k:test","0.01"]}]}],"meta":{"gasLimit":100000,"gasPrice":1e-7,"sender":"k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7","ttl":7200,"creationTime":1748834585,"chainId":"0"},"networkId":"mainnet01"}`;

const officialHash = "7slWM19C5vh9yVEpN2KEedosChdGg9jUma4H1xwqQ88";

console.log('📋 官方数据:');
console.log(`   哈希: ${officialHash}`);
console.log(`   命令长度: ${officialCmd.length}`);

// 尝试各种哈希算法和编码组合
console.log('\n🧮 尝试各种哈希计算方法:');

const hashMethods = [
    {
        name: 'Blake2b-256 -> Base64',
        calculate: (data) => {
            const hash = crypto.createHash('blake2b512').update(data, 'utf8').digest();
            return hash.subarray(0, 32).toString('base64');
        }
    },
    {
        name: 'Blake2b-256 -> Base64 URL Safe',
        calculate: (data) => {
            const hash = crypto.createHash('blake2b512').update(data, 'utf8').digest();
            return hash.subarray(0, 32).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        }
    },
    {
        name: 'SHA256 -> Base64',
        calculate: (data) => {
            const hash = crypto.createHash('sha256').update(data, 'utf8').digest();
            return hash.toString('base64');
        }
    },
    {
        name: 'SHA256 -> Base64 URL Safe',
        calculate: (data) => {
            const hash = crypto.createHash('sha256').update(data, 'utf8').digest();
            return hash.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        }
    },
    {
        name: 'Blake2b-512 前32字节 -> Base64',
        calculate: (data) => {
            const hash = crypto.createHash('blake2b512').update(data, 'utf8').digest();
            return hash.subarray(0, 32).toString('base64');
        }
    },
    {
        name: 'Blake2b-512 -> Base64 截取',
        calculate: (data) => {
            const hash = crypto.createHash('blake2b512').update(data, 'utf8').digest();
            const base64 = hash.toString('base64');
            return base64.substring(0, 43); // 截取到与官方相同长度
        }
    }
];

hashMethods.forEach(method => {
    try {
        const calculatedHash = method.calculate(officialCmd);
        const matches = calculatedHash === officialHash;
        
        console.log(`\n   ${method.name}:`);
        console.log(`   计算结果: ${calculatedHash}`);
        console.log(`   匹配官方: ${matches ? '✅' : '❌'}`);
        
        if (matches) {
            console.log(`   🎯 找到正确的哈希方法: ${method.name}`);
        }
    } catch (error) {
        console.log(`\n   ${method.name}: ❌ 错误 - ${error.message}`);
    }
});

// 分析 Base64 解码
console.log('\n🔍 分析官方哈希的 Base64 解码:');
try {
    const decodedHash = Buffer.from(officialHash, 'base64');
    console.log(`   解码后的字节: ${decodedHash.toString('hex')}`);
    console.log(`   字节长度: ${decodedHash.length}`);
    
    // 检查是否匹配我们之前计算的十六进制哈希
    const ourHexHash = "086a69bc282ee22175e5e0b4d7a992626d227234899eb18068e94e88a5798286";
    const matches = decodedHash.toString('hex') === ourHexHash;
    console.log(`   匹配我们的十六进制哈希: ${matches ? '✅' : '❌'}`);
    
    if (matches) {
        console.log('   🎯 发现：我们的哈希计算是正确的，只是编码格式不同！');
    }
} catch (error) {
    console.log(`   Base64 解码错误: ${error.message}`);
}

// 验证我们的哈希转换为 Base64
console.log('\n🔄 验证哈希格式转换:');
const ourHexHash = "086a69bc282ee22175e5e0b4d7a992626d227234899eb18068e94e88a5798286";
const ourBase64Hash = Buffer.from(ourHexHash, 'hex').toString('base64');
console.log(`   我们的十六进制哈希: ${ourHexHash}`);
console.log(`   转换为 Base64: ${ourBase64Hash}`);
console.log(`   匹配官方: ${ourBase64Hash === officialHash ? '✅' : '❌'}`);

// 输出 Python 实现指导
console.log('\n🐍 Python 实现指导:');
console.log('   基于分析结果，Python 需要:');
console.log('   1. 使用 Blake2b 哈希算法');
console.log('   2. 将结果编码为 Base64 格式');
console.log('   3. 确保 JSON 序列化格式完全匹配');
console.log('   4. 添加 "scheme": "ED25519" 字段');
console.log('   5. 使用正确的 nonce 格式');

console.log('\n✅ 分析完成!');
