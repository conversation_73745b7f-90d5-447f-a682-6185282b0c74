#!/usr/bin/env python3
"""
Kadena 纯 Python 哈希实现

基于对 Kadena 官方 JavaScript SDK 的深入分析，实现纯 Python 的正确哈希计算。

关键发现：
1. Kadena 使用 Blake2b 哈希算法
2. 输出格式是 Base64 编码
3. 需要特定的 JSON 序列化格式
4. 需要包含 "scheme": "ED25519" 字段
"""

import json
import hashlib
import time
import copy
import base64
from typing import Dict, Any
from decimal import Decimal


class KadenaPurePythonHash:
    """Kadena 纯 Python 哈希计算器"""

    @staticmethod
    def calculate_kadena_hash(cmd_data: Dict[str, Any]) -> str:
        """
        计算 Kadena 交易哈希 - 纯 Python 实现

        Args:
            cmd_data: 命令数据字典

        Returns:
            Base64 格式的哈希字符串
        """
        try:
            print(f"[PURE] 🐍 使用纯 Python 计算 Kadena 哈希")

            # 1. 标准化数据
            normalized_data = KadenaPurePythonHash._normalize_data(cmd_data)

            # 2. 序列化为 JSON
            json_str = KadenaPurePythonHash._serialize_to_json(normalized_data)

            # 3. 计算 Blake2b 哈希
            hash_result = KadenaPurePythonHash._calculate_blake2b_hash(json_str)

            print(f"[PURE] 纯 Python 哈希计算成功: {hash_result}")
            return hash_result

        except Exception as e:
            print(f"[ERROR] 纯 Python 哈希计算失败: {str(e)}")
            raise Exception(f"纯 Python 哈希计算失败: {str(e)}")

    @staticmethod
    def _normalize_data(cmd_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化数据以匹配 Kadena 官方格式"""

        # 深拷贝数据
        data = copy.deepcopy(cmd_data)

        # 确保数据结构完全匹配官方格式
        if 'meta' in data:
            meta = data['meta']

            # 确保数字类型正确
            meta['creationTime'] = int(meta['creationTime'])
            meta['ttl'] = int(meta['ttl'])
            meta['gasLimit'] = int(meta['gasLimit'])
            meta['chainId'] = str(meta['chainId'])
            meta['gasPrice'] = float(meta['gasPrice'])  # 确保是浮点数

        # 确保 signers 包含 scheme 字段
        if 'signers' in data:
            for signer in data['signers']:
                if 'scheme' not in signer:
                    signer['scheme'] = 'ED25519'

        # 确保 payload.exec.data 存在
        if 'payload' in data and 'exec' in data['payload']:
            if 'data' not in data['payload']['exec']:
                data['payload']['exec']['data'] = {}
            elif data['payload']['exec']['data'] is None:
                data['payload']['exec']['data'] = {}

        return data

    @staticmethod
    def _serialize_to_json(data: Dict[str, Any]) -> str:
        """序列化数据为 JSON - 匹配 Kadena 官方格式"""

        # 使用与 Kadena 官方相同的序列化方法
        # 基于我们的分析，Kadena 使用紧凑格式，不排序键
        json_str = json.dumps(
            data,
            separators=(',', ':'),
            sort_keys=False,  # 不排序键，保持原始顺序
            ensure_ascii=True,
            allow_nan=False,
            indent=None
        )

        print(f"[PURE] JSON 序列化: {json_str[:100]}...")
        print(f"[PURE] JSON 长度: {len(json_str)}")

        return json_str

    @staticmethod
    def _calculate_blake2b_hash(json_str: str) -> str:
        """计算 Blake2b 哈希并转换为 Base64URL"""

        # 将 JSON 字符串转换为字节
        json_bytes = json_str.encode('utf-8')

        # 计算 Blake2b 哈希（32字节 = 256位）
        blake2b_hash = hashlib.blake2b(json_bytes, digest_size=32)
        hash_bytes = blake2b_hash.digest()

        # 转换为 Base64URL（Kadena 要求的格式）
        base64url_hash = base64.urlsafe_b64encode(hash_bytes).decode('ascii')

        # 移除末尾的 = 填充（Base64URL 标准）
        base64url_hash = base64url_hash.rstrip('=')

        print(f"[PURE] Blake2b 哈希计算:")
        print(f"[PURE]   输入字节长度: {len(json_bytes)}")
        print(f"[PURE]   哈希字节长度: {len(hash_bytes)}")
        print(f"[PURE]   Base64URL 哈希: {base64url_hash}")

        return base64url_hash

    @staticmethod
    def create_kadena_transaction(from_address: str, to_address: str, amount: str,
                                private_key: str, chain_id: str = "0",
                                network_id: str = "mainnet01") -> Dict[str, Any]:
        """
        创建使用纯 Python 哈希的完整 Kadena 交易

        Args:
            from_address: 发送地址
            to_address: 接收地址
            amount: 转账金额
            private_key: 私钥
            chain_id: 链 ID
            network_id: 网络 ID

        Returns:
            完整的签名交易
        """
        try:
            print(f"[PURE] 🚀 创建纯 Python Kadena 交易")

            # 1. 构建命令数据
            cmd_data = KadenaPurePythonHash._build_command_data(
                from_address, to_address, amount, chain_id, network_id
            )

            # 2. 计算正确哈希
            correct_hash = KadenaPurePythonHash.calculate_kadena_hash(cmd_data)

            # 3. 生成签名
            signature = KadenaPurePythonHash._sign_transaction(correct_hash, private_key)

            # 4. 构建完整交易
            signed_tx = KadenaPurePythonHash._build_signed_transaction(cmd_data, correct_hash, signature)

            # 5. 验证交易
            validation = KadenaPurePythonHash._validate_transaction(signed_tx, from_address, to_address, amount)

            return {
                'cmd_data': cmd_data,
                'correct_hash': correct_hash,
                'signature': signature,
                'signed_transaction': signed_tx,
                'validation': validation,
                'success': True
            }

        except Exception as e:
            print(f"[ERROR] 创建纯 Python 交易失败: {str(e)}")
            raise Exception(f"创建纯 Python 交易失败: {str(e)}")

    @staticmethod
    def _build_command_data(from_address: str, to_address: str, amount: str,
                           chain_id: str, network_id: str) -> Dict[str, Any]:
        """构建命令数据 - 匹配 Kadena 官方格式"""

        # 当前时间戳
        current_time = int(time.time())

        # 生成 nonce（使用与官方 SDK 相似的格式）
        nonce = f"kjs:nonce:{int(time.time() * 1000)}"

        # Pact 代码 - 使用 transfer-create 来自动创建账户
        to_pubkey = to_address.replace("k:", "")
        pact_code = f'(coin.transfer-create "{from_address}" "{to_address}" (read-keyset "ks") {amount})'

        # 公钥
        pub_key = from_address.replace("k:", "")

        # 构建命令数据（字段顺序很重要！）
        # 基于官方 SDK 的分析，使用这个特定的字段顺序
        cmd_data = {
            "payload": {
                "exec": {
                    "code": pact_code,
                    "data": {
                        "ks": {
                            "keys": [to_pubkey],
                            "pred": "keys-all"
                        }
                    }
                }
            },
            "nonce": nonce,
            "signers": [
                {
                    "pubKey": pub_key,
                    "scheme": "ED25519",
                    "clist": [
                        {
                            "name": "coin.TRANSFER",
                            "args": [from_address, to_address, float(amount)]
                        },
                        {
                            "name": "coin.GAS",
                            "args": []
                        }
                    ]
                }
            ],
            "meta": {
                "gasLimit": 100000,
                "gasPrice": 1e-7,
                "sender": from_address,
                "ttl": 7200,
                "creationTime": current_time,
                "chainId": chain_id
            },
            "networkId": network_id
        }

        return cmd_data

    @staticmethod
    def _sign_transaction(cmd_hash: str, private_key: str) -> str:
        """签名交易"""
        try:
            import nacl.signing
            import nacl.encoding

            # 处理私钥格式
            if private_key.startswith('0x'):
                private_key = private_key[2:]

            # 将 Base64URL 哈希转换为字节
            # 需要添加填充以正确解码 Base64URL
            padding = 4 - (len(cmd_hash) % 4)
            if padding != 4:
                cmd_hash_padded = cmd_hash + ('=' * padding)
            else:
                cmd_hash_padded = cmd_hash

            # 将 Base64URL 转换为标准 Base64
            base64_hash = cmd_hash_padded.replace('-', '+').replace('_', '/')
            hash_bytes = base64.b64decode(base64_hash)

            # 转换私钥为字节
            private_key_bytes = bytes.fromhex(private_key)

            # 创建签名密钥
            signing_key = nacl.signing.SigningKey(private_key_bytes)

            # 签名
            signature = signing_key.sign(hash_bytes, encoder=nacl.encoding.RawEncoder)
            signature_hex = signature.signature.hex()

            print(f"[PURE] 纯 Python 签名生成:")
            print(f"[PURE]   Base64URL 哈希: {cmd_hash}")
            print(f"[PURE]   签名: {signature_hex[:16]}...")
            return signature_hex

        except Exception as e:
            print(f"[ERROR] 签名失败: {str(e)}")
            raise Exception(f"签名失败: {str(e)}")

    @staticmethod
    def _build_signed_transaction(cmd_data: Dict[str, Any], cmd_hash: str, signature: str) -> Dict[str, Any]:
        """构建签名交易"""

        # 序列化命令
        cmd_json = KadenaPurePythonHash._serialize_to_json(cmd_data)

        # 构建签名交易
        signed_tx = {
            "cmds": [
                {
                    "hash": cmd_hash,
                    "sigs": [
                        {
                            "sig": signature
                        }
                    ],
                    "cmd": cmd_json
                }
            ]
        }

        return signed_tx

    @staticmethod
    def _validate_transaction(signed_tx: Dict[str, Any], from_address: str,
                            to_address: str, amount: str) -> Dict[str, Any]:
        """验证交易"""
        try:
            # 基本验证
            if 'cmds' not in signed_tx or not signed_tx['cmds']:
                return {'valid': False, 'error': '缺少 cmds'}

            cmd = signed_tx['cmds'][0]

            # 验证必要字段
            required_fields = ['hash', 'sigs', 'cmd']
            for field in required_fields:
                if field not in cmd:
                    return {'valid': False, 'error': f'缺少 {field}'}

            # 验证签名
            if not cmd['sigs'] or not cmd['sigs'][0].get('sig'):
                return {'valid': False, 'error': '签名无效'}

            # 验证命令内容
            cmd_data = json.loads(cmd['cmd'])
            pact_code = cmd_data['payload']['exec']['code']

            # 验证地址和金额
            if from_address not in pact_code:
                return {'valid': False, 'error': '发送地址不匹配'}
            if to_address not in pact_code:
                return {'valid': False, 'error': '接收地址不匹配'}
            if amount not in pact_code:
                return {'valid': False, 'error': '金额不匹配'}

            return {
                'valid': True,
                'message': '纯 Python 验证通过',
                'details': {
                    'hash': cmd['hash'],
                    'signature_length': len(cmd['sigs'][0]['sig']),
                    'pact_code': pact_code
                }
            }

        except Exception as e:
            return {'valid': False, 'error': f'验证异常: {str(e)}'}


def test_pure_python_hash():
    """测试纯 Python 哈希计算"""
    print("🐍 测试 Kadena 纯 Python 哈希计算")

    # 测试参数
    from_address = "k:5870543114edc85764179d125f6fbf1363aaaf3af2921e9c5cc7405c6ba9cae7"
    to_address = "k:test"
    amount = "0.01"
    private_key = "your-private-key-here"  # 需要真实私钥

    try:
        # 创建纯 Python 交易
        result = KadenaPurePythonHash.create_kadena_transaction(
            from_address, to_address, amount, private_key
        )

        print(f"\n✅ 纯 Python 交易创建成功:")
        print(f"   哈希: {result['correct_hash']}")
        print(f"   签名: {result['signature'][:16]}...")
        print(f"   验证: {result['validation']['valid']}")

        return result

    except Exception as e:
        print(f"\n❌ 纯 Python 交易创建失败: {str(e)}")
        return None


if __name__ == "__main__":
    test_pure_python_hash()
