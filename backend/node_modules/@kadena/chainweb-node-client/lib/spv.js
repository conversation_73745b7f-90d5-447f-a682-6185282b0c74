"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.spv = void 0;
const parseResponseTEXT_1 = require("./parseResponseTEXT");
const stringifyAndMakePOSTRequest_1 = require("./stringifyAndMakePOSTRequest");
const fetch_1 = require("./utils/fetch");
/**
 * Blocking request to fetch spv proof of a cross chain transaction.
 * Request must be sent to the chain where the transaction is initiated.
 *
 * @param requestBody -
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
async function spv(requestBody, apiHost, requestInit) {
    const request = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(requestBody, requestInit);
    const spvUrl = new URL(`${apiHost}/spv`);
    const response = await (0, fetch_1.fetch)(spvUrl.toString(), request);
    return (0, parseResponseTEXT_1.parseResponseTEXT)(response);
}
exports.spv = spv;
//# sourceMappingURL=spv.js.map