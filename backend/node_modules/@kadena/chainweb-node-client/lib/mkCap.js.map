{"version": 3, "file": "mkCap.js", "sourceRoot": "", "sources": ["../src/mkCap.ts"], "names": [], "mappings": ";;;AAEA;;;;;;GAMG;AACH,SAAgB,KAAK,CAAC,IAAY,EAAE,OAAyB,EAAE;IAC7D,OAAO;QACL,IAAI;QACJ,IAAI;KACL,CAAC;AACJ,CAAC;AALD,sBAKC", "sourcesContent": ["import type { ICap, PactValue } from '@kadena/types';\n\n/**\n * Helper function for creating a pact capability object.\n * Output can be used with the `mkSignerCList` function.\n * @param name - Qualified name of the capability.\n * @param args - Array of PactValue arguments the capability expects (default: empty array).\n * @alpha\n */\nexport function mkCap(name: string, args: Array<PactValue> = []): ICap {\n  return {\n    name,\n    args,\n  };\n}\n"]}