import type { IBase64Url, ICommand, IMetaData, IPactEvent, IPactExec, IUnsignedCommand, PactValue, SPVProof } from '@kadena/types';
/**
 * The result of a pact execution when no errors occur.
 * The data will be the value returned or generated by the pact code.
 *
 * @alpha
 */
export interface IPactResultSuccess {
    status: 'success';
    data: PactValue;
}
/**
 * The result of a pact execution when an error occurs.
 * If the result is from a committed transaction from the /send endpoint, the error will only list the type of error that occured.
 * If the result is from the /local endpoint, the error will be an object.
 *
 * @alpha
 */
export interface IPactResultError {
    status: 'failure';
    error: {
        callStack: any;
        type: string;
        message: string;
        info: string;
    };
}
/**
 * Platform-specific information on the block that executed a transaction.
 *
 * @param blockHash - Block hash of the block containing the transaction.
 * @param blockTime - POSIX time when the block was mined.
 * @param blockHeight - Block height of the block.
 * @param prevBlockHash - Parent Block hash of the containing block.
 * @param publicMeta - Platform-specific data provided by the request.
 *
 *
 */
interface IChainwebResponseMetaData {
    blockHash: string;
    blockTime: number;
    blockHeight: number;
    prevBlockHash: string;
    publicMeta?: IMetaData;
}
/**
 * @alpha
 */
export declare const CHAINS: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19"];
/**
 * Stringified Chainweb chain numbers.
 *
 * @see https://stackoverflow.com/a/45257357/1463352
 * @alpha
 */
export type ChainwebChainId = (typeof CHAINS)[number];
/**
 * Different Chainweb network versions.
 * @alpha
 */
export type ChainwebNetworkId = 'mainnet01' | 'testnet04' | 'development';
/**
 * A Request Key is the blake2b-256 bit hash of a Pact command.
 * They might also be called Transaction IDs.
 * A list of them are returned by the /send endpoint.
 * @alpha
 */
export interface IRequestKeys {
    requestKeys: Array<IBase64Url>;
}
/**
 * Request type of /send endpoint.
 *
 * @param cmds - Non-empty array of Pact commands (or transactions) to submit to server.
 * @alpha
 */
export interface ISendRequestBody {
    cmds: Array<ICommand>;
}
/**
 * Response type of /send endpoint.
 *
 * @param requestKeys - List of request keys (or command hashes) of the transactions submitted.
 *                      Can be sent to /poll and /listen to retrieve transaction results.
 *
 * @alpha
 */
export type SendResponse = IRequestKeys;
/**
 * @alpha
 */
export type LocalRequestBody = ICommand | IUnsignedCommand;
/**
 * @alpha
 */
export type ILocalResult = IPreflightResult | ICommandResult;
/**
 * @alpha
 */
export type LocalResultWithoutPreflight = Omit<ILocalCommandResult, 'preflightWarnings'>;
/**
 * Request type of /poll endpoint.
 *
 * @param requestKeys - List of request keys (or command hashes) to poll for.
 *
 * @alpha
 */
export interface IPollRequestBody {
    requestKeys: Array<IBase64Url>;
}
/**
 * @alpha
 */
export interface IPollResponse {
    [key: IBase64Url]: ICommandResult;
}
/**
 * Request type of /listen endpoint.
 *
 * @param listen - Single request key (or command hash) to listen for.
 *
 * @alpha
 */
export interface IListenRequestBody {
    listen: IBase64Url;
}
/**
 * @alpha
 */
export type ListenResponse = ICommandResult;
/**
 * Request type of /spv endpoint.
 *
 * @param requestKey - Request Key of an initiated cross chain transaction at the source chain.
 * @param targetChainId - Target chain id of the cross chain transaction.
 *
 * @alpha
 */
export interface ISPVRequestBody {
    requestKey: IBase64Url;
    targetChainId: ChainwebChainId;
}
/**
 * Response type of /spv endpoint.
 *
 * Returns backend-specific data for continuing a cross-chain proof.
 *
 * @alpha
 */
export type SPVResponse = SPVProof;
/**
 * @alpha
 */
export interface IPreflightResult {
    preflightResult: ICommandResult;
    preflightWarnings: [];
}
/**
 * API result of attempting to execute a pact transaction.
 *
 * @param reqKey - Unique ID of a pact transaction, equivalent to the payload hash.
 * @param txId - Database-internal transaction tracking ID.
 *               Absent when transaction was not successful.
 *               Expected to be non-negative 64-bit integers and
 *               are expected to be monotonically increasing.
 * @param result - Pact execution result, either a Pact error or the output (a PactValue) of the last pact expression in the transaction.
 * @param gas - Gas units consummed by the transaction as a 64-bit integer.
 * @param logs - Backend-specific value providing image of database logs.
 * @param continuation - Describes the result of a defpact execution, if one occurred.
 * @param metaData - Platform-specific information on the block that executed the transaction.
 * @param events - Optional list of Pact events emitted during the transaction.
 *
 *
 * @alpha
 */
export interface ICommandResult {
    reqKey: IBase64Url;
    txId: number | null;
    result: IPactResultSuccess | IPactResultError;
    gas: number;
    logs: string | null;
    continuation: IPactExec | null;
    metaData: IChainwebResponseMetaData | null;
    events?: Array<IPactEvent>;
}
/**
 * API result of attempting to execute a pact transaction.
 *
 * @param reqKey - Unique ID of a pact transaction, equivalent to the payload hash.
 * @param txId - Database-internal transaction tracking ID.
 *               Absent when transaction was not successful.
 *               Expected to be non-negative 64-bit integers and
 *               are expected to be monotonically increasing.
 * @param result - Pact execution result, either a Pact error or the output (a PactValue) of the last pact expression in the transaction.
 * @param gas - Gas units consummed by the transaction as a 64-bit integer.
 * @param logs - Backend-specific value providing image of database logs.
 * @param continuation - Describes the result of a defpact execution, if one occurred.
 * @param metaData - Platform-specific information on the block that executed the transaction.
 * @param events - Optional list of Pact events emitted during the transaction.
 * @param preflightWarnings - Optional list of Preflight warnings on /local result.
 *
 *
 * @alpha
 */
export interface ILocalCommandResult extends ICommandResult {
    preflightWarnings?: Array<string>;
}
export {};
//# sourceMappingURL=PactAPI.d.ts.map