{"version": 3, "file": "PactAPI.js", "sourceRoot": "", "sources": ["../../src/interfaces/PactAPI.ts"], "names": [], "mappings": ";;;AA2DA,qCAAqC;AAErC;;GAEG;AACH,kDAAkD;AACrC,QAAA,MAAM,GAAG;IACpB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACI,CAAC", "sourcesContent": ["import type {\n  IBase<PERSON>Url,\n  ICommand,\n  IMetaData,\n  IPactEvent,\n  IPactExec,\n  IUnsignedCommand,\n  PactValue,\n  SPVProof,\n} from '@kadena/types';\n\n/**\n * The result of a pact execution when no errors occur.\n * The data will be the value returned or generated by the pact code.\n *\n * @alpha\n */\nexport interface IPactResultSuccess {\n  status: 'success';\n  data: PactValue;\n}\n\n/**\n * The result of a pact execution when an error occurs.\n * If the result is from a committed transaction from the /send endpoint, the error will only list the type of error that occured.\n * If the result is from the /local endpoint, the error will be an object.\n *\n * @alpha\n */\nexport interface IPactResultError {\n  status: 'failure';\n  error: {\n    callStack: any;\n    type: string;\n    message: string;\n    info: string;\n  };\n}\n\n/**\n * Platform-specific information on the block that executed a transaction.\n *\n * @param blockHash - Block hash of the block containing the transaction.\n * @param blockTime - POSIX time when the block was mined.\n * @param blockHeight - Block height of the block.\n * @param prevBlockHash - Parent Block hash of the containing block.\n * @param publicMeta - Platform-specific data provided by the request.\n *\n *\n */\n// @TODO Add `publicMeta` to Open API spec.\ninterface IChainwebResponseMetaData {\n  blockHash: string;\n  blockTime: number;\n  blockHeight: number;\n  prevBlockHash: string;\n  publicMeta?: IMetaData;\n}\n\n// TODO: Move Chainweb Specific Types\n\n/**\n * @alpha\n */\n// eslint-disable-next-line @rushstack/typedef-var\nexport const CHAINS = [\n  '0',\n  '1',\n  '2',\n  '3',\n  '4',\n  '5',\n  '6',\n  '7',\n  '8',\n  '9',\n  '10',\n  '11',\n  '12',\n  '13',\n  '14',\n  '15',\n  '16',\n  '17',\n  '18',\n  '19',\n] as const;\n\n/**\n * Stringified Chainweb chain numbers.\n *\n * @see https://stackoverflow.com/a/45257357/1463352\n * @alpha\n */\nexport type ChainwebChainId = (typeof CHAINS)[number];\n\n/**\n * Different Chainweb network versions.\n * @alpha\n */\nexport type ChainwebNetworkId = 'mainnet01' | 'testnet04' | 'development';\n\n/**\n * A Request Key is the blake2b-256 bit hash of a Pact command.\n * They might also be called Transaction IDs.\n * A list of them are returned by the /send endpoint.\n * @alpha\n */\nexport interface IRequestKeys {\n  requestKeys: Array<IBase64Url>;\n}\n\n/**\n * Request type of /send endpoint.\n *\n * @param cmds - Non-empty array of Pact commands (or transactions) to submit to server.\n * @alpha\n */\nexport interface ISendRequestBody {\n  cmds: Array<ICommand>;\n}\n\n/**\n * Response type of /send endpoint.\n *\n * @param requestKeys - List of request keys (or command hashes) of the transactions submitted.\n *                      Can be sent to /poll and /listen to retrieve transaction results.\n *\n * @alpha\n */\nexport type SendResponse = IRequestKeys;\n\n/**\n * @alpha\n */\nexport type LocalRequestBody = ICommand | IUnsignedCommand;\n\n/**\n * @alpha\n */\nexport type ILocalResult = IPreflightResult | ICommandResult;\n\n/**\n * @alpha\n */\nexport type LocalResultWithoutPreflight = Omit<\n  ILocalCommandResult,\n  'preflightWarnings'\n>;\n\n/**\n * Request type of /poll endpoint.\n *\n * @param requestKeys - List of request keys (or command hashes) to poll for.\n *\n * @alpha\n */\nexport interface IPollRequestBody {\n  requestKeys: Array<IBase64Url>;\n}\n\n/**\n * @alpha\n */\nexport interface IPollResponse {\n  [key: IBase64Url]: ICommandResult;\n}\n\n/**\n * Request type of /listen endpoint.\n *\n * @param listen - Single request key (or command hash) to listen for.\n *\n * @alpha\n */\nexport interface IListenRequestBody {\n  listen: IBase64Url;\n}\n\n/**\n * @alpha\n */\nexport type ListenResponse = ICommandResult;\n\n/**\n * Request type of /spv endpoint.\n *\n * @param requestKey - Request Key of an initiated cross chain transaction at the source chain.\n * @param targetChainId - Target chain id of the cross chain transaction.\n *\n * @alpha\n */\nexport interface ISPVRequestBody {\n  requestKey: IBase64Url;\n  targetChainId: ChainwebChainId;\n}\n\n/**\n * Response type of /spv endpoint.\n *\n * Returns backend-specific data for continuing a cross-chain proof.\n *\n * @alpha\n */\nexport type SPVResponse = SPVProof;\n\n/**\n * @alpha\n */\nexport interface IPreflightResult {\n  preflightResult: ICommandResult;\n  preflightWarnings: [];\n}\n\n/**\n * API result of attempting to execute a pact transaction.\n *\n * @param reqKey - Unique ID of a pact transaction, equivalent to the payload hash.\n * @param txId - Database-internal transaction tracking ID.\n *               Absent when transaction was not successful.\n *               Expected to be non-negative 64-bit integers and\n *               are expected to be monotonically increasing.\n * @param result - Pact execution result, either a Pact error or the output (a PactValue) of the last pact expression in the transaction.\n * @param gas - Gas units consummed by the transaction as a 64-bit integer.\n * @param logs - Backend-specific value providing image of database logs.\n * @param continuation - Describes the result of a defpact execution, if one occurred.\n * @param metaData - Platform-specific information on the block that executed the transaction.\n * @param events - Optional list of Pact events emitted during the transaction.\n *\n *\n * @alpha\n */\n// @TODO Should `txId` and `gas` be a BigInt since Haskell defines it as int64?\n// @TODO Add `gas` to OpenApi spec?\nexport interface ICommandResult {\n  reqKey: IBase64Url;\n  /* eslint-disable-next-line @rushstack/no-new-null*/\n  txId: number | null;\n  result: IPactResultSuccess | IPactResultError;\n  gas: number;\n  /* eslint-disable-next-line @rushstack/no-new-null*/\n  logs: string | null;\n  /* eslint-disable-next-line @rushstack/no-new-null*/\n  continuation: IPactExec | null;\n  /* eslint-disable-next-line @rushstack/no-new-null*/\n  metaData: IChainwebResponseMetaData | null;\n  events?: Array<IPactEvent>;\n}\n\n/**\n * API result of attempting to execute a pact transaction.\n *\n * @param reqKey - Unique ID of a pact transaction, equivalent to the payload hash.\n * @param txId - Database-internal transaction tracking ID.\n *               Absent when transaction was not successful.\n *               Expected to be non-negative 64-bit integers and\n *               are expected to be monotonically increasing.\n * @param result - Pact execution result, either a Pact error or the output (a PactValue) of the last pact expression in the transaction.\n * @param gas - Gas units consummed by the transaction as a 64-bit integer.\n * @param logs - Backend-specific value providing image of database logs.\n * @param continuation - Describes the result of a defpact execution, if one occurred.\n * @param metaData - Platform-specific information on the block that executed the transaction.\n * @param events - Optional list of Pact events emitted during the transaction.\n * @param preflightWarnings - Optional list of Preflight warnings on /local result.\n *\n *\n * @alpha\n */\n// @TODO Should `txId` and `gas` be a BigInt since Haskell defines it as int64?\n// @TODO Add `gas` to OpenApi spec?\nexport interface ILocalCommandResult extends ICommandResult {\n  preflightWarnings?: Array<string>;\n}\n"]}