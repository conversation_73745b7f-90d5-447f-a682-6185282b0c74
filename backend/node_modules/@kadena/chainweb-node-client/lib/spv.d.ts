import type { ISPVRequestBody, SPVResponse } from './interfaces/PactAPI';
import type { ClientRequestInit } from './local';
/**
 * Blocking request to fetch spv proof of a cross chain transaction.
 * Request must be sent to the chain where the transaction is initiated.
 *
 * @param requestBody -
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function spv(requestBody: ISPVRequestBody, apiHost: string, requestInit?: ClientRequestInit): Promise<SPVResponse | Response>;
//# sourceMappingURL=spv.d.ts.map