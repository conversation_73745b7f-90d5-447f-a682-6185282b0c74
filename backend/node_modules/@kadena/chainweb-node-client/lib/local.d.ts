import type { ICommand, IUnsignedCommand } from '@kadena/types';
import type { ICommandResult, ILocalCommandResult, IPreflightResult, LocalRequestBody } from './interfaces/PactAPI';
/**
 * @alpha
 */
export type ClientRequestInit = Omit<RequestInit, 'method' | 'body'>;
/**
 * @alpha
 */
export interface ILocalOptions extends ClientRequestInit {
    preflight?: boolean;
    signatureVerification?: boolean;
}
/**
 * @alpha
 */
export type LocalResponse<Opt extends ILocalOptions> = Opt extends {
    preflight?: true;
} ? ILocalCommandResult : ICommandResult;
/**
 * Blocking/sync call to submit a command for non-transactional execution.
 * In a blockchain environment this would be a node-local “dirty read”.
 * Any database writes or changes to the environment are rolled back.
 *
 * @param requestBody - Pact command to submit to server (non-transactional).
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function local<T extends ILocalOptions>(requestBody: LocalRequestBody, apiHost: string, options?: T): Promise<LocalResponse<T>>;
/**
 * Blocking/sync call to submit a command for non-transactional execution.
 * In a blockchain environment this would be a node-local “dirty read”.
 * Any database writes or changes to the environment are rolled back.
 *
 * @param requestBody - Pact command to submit to server (non-transactional).
 * @param apiHost - API host running a Pact-enabled server.
 * @param options - option query to enable preflight and signatureVerification
 * @alpha
 */
export declare function localRaw(requestBody: LocalRequestBody, apiHost: string, { preflight, signatureVerification, ...requestInit }: ILocalOptions & {
    signatureVerification: boolean;
    preflight: boolean;
}): Promise<IPreflightResult | ICommandResult>;
/**
 * @alpha
 */
export declare function convertIUnsignedTransactionToNoSig(transaction: IUnsignedCommand): ICommand;
//# sourceMappingURL=local.d.ts.map