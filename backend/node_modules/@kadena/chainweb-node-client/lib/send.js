"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.send = void 0;
const parseResponse_1 = require("./parseResponse");
const stringifyAndMakePOSTRequest_1 = require("./stringifyAndMakePOSTRequest");
const fetch_1 = require("./utils/fetch");
/**
 * Asynchronous submission of one or more public (unencrypted) commands to the blockchain for execution.
 *
 * Corresponds to `fetchSendRaw` and `fetchSend` functions:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L601
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L589
 *
 * @param requestBody - Non-empty array of Pact commands to submit to server.
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
async function send(requestBody, apiHost, requestInit) {
    const request = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(requestBody, requestInit);
    const sendUrl = new URL(`${apiHost}/api/v1/send`);
    const response = await (0, fetch_1.fetch)(sendUrl.toString(), request);
    return (0, parseResponse_1.parseResponse)(response);
}
exports.send = send;
//# sourceMappingURL=send.js.map