{"version": 3, "file": "createListenRequest.js", "sourceRoot": "", "sources": ["../src/createListenRequest.ts"], "names": [], "mappings": ";;;AAAA,mEAAoD;AAMpD;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,EAClC,IAAI,GACa;IACjB,OAAO,EAAE,MAAM,EAAE,IAAA,2BAAM,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7D,CAAC;AAJD,kDAIC", "sourcesContent": ["import { unique } from '@kadena/cryptography-utils';\nimport type {\n  IListenRequestBody,\n  ISendRequestBody,\n} from './interfaces/PactAPI';\n\n/**\n * Given an exec 'send' message, prepare a message for 'listen' endpoint.\n * @alpha\n * @param request - The JSON request object with \"cmds\" field, see 'mkPublicSend'. Only takes first element.\n * @returns Object with \"requestKey\" for polling.\n */\nexport function createListenRequest({\n  cmds,\n}: ISendRequestBody): IListenRequestBody {\n  return { listen: unique(cmds.map(({ hash }) => hash))[0] };\n}\n"]}