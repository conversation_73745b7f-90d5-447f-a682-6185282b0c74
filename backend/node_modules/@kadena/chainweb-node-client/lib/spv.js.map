{"version": 3, "file": "spv.js", "sourceRoot": "", "sources": ["../src/spv.ts"], "names": [], "mappings": ";;;AAEA,2DAAwD;AACxD,+EAA4E;AAC5E,yCAAsC;AAEtC;;;;;;;GAOG;AACI,KAAK,UAAU,GAAG,CACvB,WAA4B,EAC5B,OAAe,EACf,WAA+B;IAE/B,MAAM,OAAO,GAAG,IAAA,yDAA2B,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtE,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC;IAEzC,MAAM,QAAQ,GAAG,MAAM,IAAA,aAAK,EAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACzD,OAAO,IAAA,qCAAiB,EAAC,QAAQ,CAAC,CAAC;AACrC,CAAC;AAVD,kBAUC", "sourcesContent": ["import type { ISPVRequestBody, SPVResponse } from './interfaces/PactAPI';\nimport type { ClientRequestInit } from './local';\nimport { parseResponseTEXT } from './parseResponseTEXT';\nimport { stringifyAndMakePOSTRequest } from './stringifyAndMakePOSTRequest';\nimport { fetch } from './utils/fetch';\n\n/**\n * Blocking request to fetch spv proof of a cross chain transaction.\n * Request must be sent to the chain where the transaction is initiated.\n *\n * @param requestBody -\n * @param apiHost - API host running a Pact-enabled server.\n * @alpha\n */\nexport async function spv(\n  requestBody: ISPVRequestBody,\n  apiHost: string,\n  requestInit?: ClientRequestInit,\n): Promise<SPVResponse | Response> {\n  const request = stringifyAndMakePOSTRequest(requestBody, requestInit);\n  const spvUrl = new URL(`${apiHost}/spv`);\n\n  const response = await fetch(spvUrl.toString(), request);\n  return parseResponseTEXT(response);\n}\n"]}