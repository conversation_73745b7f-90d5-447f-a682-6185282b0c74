import type { IListenRequestBody, ISendRequestBody } from './interfaces/PactAPI';
/**
 * Given an exec 'send' message, prepare a message for 'listen' endpoint.
 * @alpha
 * @param request - The JSON request object with "cmds" field, see 'mkPublicSend'. Only takes first element.
 * @returns Object with "requestKey" for polling.
 */
export declare function createListenRequest({ cmds, }: ISendRequestBody): IListenRequestBody;
//# sourceMappingURL=createListenRequest.d.ts.map