{"version": 3, "file": "mkCap.test.js", "sourceRoot": "", "sources": ["../../src/tests/mkCap.test.ts"], "names": [], "mappings": ";;AAAA,2CAA4C;AAC5C,mCAAsC;AACtC,oCAAiC;AAEjC,IAAA,aAAI,EAAC,8CAA8C,EAAE,GAAG,EAAE;IACxD,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,UAAU,CAAC,CAAC;IACjC,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAEhD,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,6CAA6C,EAAE,GAAG,EAAE;IACvD,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,eAAe,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3E,MAAM,QAAQ,GAAG;QACf,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,GAAG,CAAC;KAC1C,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,6CAA6C,EAAE,GAAG,EAAE;IACvD,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;IAErD,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,kDAAkD,EAAE,GAAG,EAAE;IAC5D,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1E,MAAM,QAAQ,GAAG;QACf,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC;KAC7C,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,+DAA+D,EAAE,GAAG,EAAE;IACzE,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,WAAW,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;IAEjE,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,+DAA+D,EAAE,GAAG,EAAE;IACzE,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,WAAW,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC;IAElE,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,iEAAiE,EAAE,GAAG,EAAE;IAC3E,MAAM,MAAM,GAAG,IAAI,mBAAU,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,CAAC;IACtE,MAAM,QAAQ,GAAG,IAAI,mBAAU,CAAC,yBAAyB,CAAC,CAAC,aAAa,EAAE,CAAC;IAC3E,MAAM,MAAM,GAAG,IAAA,aAAK,EAAC,WAAW,EAAE;QAChC,MAAM,CAAC,gBAAgB;QACvB,MAAM;QACN,QAAQ;KACT,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG;QACf,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE;YACJ,CAAC,gBAAgB;YACjB,EAAE,GAAG,EAAE,sBAAsB,EAAE;YAC/B,EAAE,OAAO,EAAE,qBAAqB,EAAE;SACnC;KACF,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { PactNumber } from '@kadena/pactjs';\nimport { expect, test } from 'vitest';\nimport { mkCap } from '../mkCap';\n\ntest('should create a baseline cap with empty args', () => {\n  const actual = mkCap('coin.GAS');\n  const expected = { name: 'coin.GAS', args: [] };\n\n  expect(expected).toEqual(actual);\n});\n\ntest('should create a cap with multiple arguments', () => {\n  const actual = mkCap('coin.TRANSFER', ['fromAcctName', 'toAcctName', 0.1]);\n  const expected = {\n    name: 'coin.TRANSFER',\n    args: ['fromAcctName', 'toAcctName', 0.1],\n  };\n\n  expect(expected).toEqual(actual);\n});\n\ntest('should create a cap with a boolean argument', () => {\n  const actual = mkCap('coin.TEST', [true]);\n  const expected = { name: 'coin.TEST', args: [true] };\n\n  expect(expected).toEqual(actual);\n});\n\ntest('should create a cap with an array of pact values', () => {\n  const actual = mkCap('coin.TEST', [[true, 'randomStr', [1.234]], 200000]);\n  const expected = {\n    name: 'coin.TEST',\n    args: [[true, 'randomStr', [1.234]], 200000],\n  };\n\n  expect(expected).toEqual(actual);\n});\n\ntest(\"should create a cap with JavaScript's Number.MAX_SAFE_INTEGER\", () => {\n  const actual = mkCap('coin.TEST', [Number.MAX_SAFE_INTEGER]);\n  const expected = { name: 'coin.TEST', args: [9007199254740991] };\n\n  expect(expected).toEqual(actual);\n});\n\ntest(\"should create a cap with JavaScript's Number.MIN_SAFE_INTEGER\", () => {\n  const actual = mkCap('coin.TEST', [Number.MIN_SAFE_INTEGER]);\n  const expected = { name: 'coin.TEST', args: [-9007199254740991] };\n\n  expect(expected).toEqual(actual);\n});\n\ntest('should create a cap with number, pact integer, and pact decimal', () => {\n  const bigInt = new PactNumber('90071992547409910000').toPactInteger();\n  const smallDec = new PactNumber('-0.90071992547409910000').toPactDecimal();\n  const actual = mkCap('coin.TEST', [\n    Number.MIN_SAFE_INTEGER,\n    bigInt,\n    smallDec,\n  ]);\n  const expected = {\n    name: 'coin.TEST',\n    args: [\n      -9007199254740991,\n      { int: '90071992547409910000' },\n      { decimal: '-0.9007199254740991' },\n    ],\n  };\n\n  expect(expected).toEqual(actual);\n});\n"]}