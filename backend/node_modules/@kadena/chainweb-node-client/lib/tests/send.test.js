"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cryptography_utils_1 = require("@kadena/cryptography-utils");
const pactjs_1 = require("@kadena/pactjs");
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const createSendRequest_1 = require("../createSendRequest");
const send_1 = require("../send");
const Pact_1 = require("./mockdata/Pact");
const httpHandlers = [
    msw_1.http.post(`${Pact_1.testURL}/api/v1/send`, async ({ request }) => {
        const body = await request.json();
        const requestKeys = body.cmds.map((cmd) => cmd.hash);
        return msw_1.HttpResponse.json({ requestKeys });
    }, { once: true }),
    msw_1.http.post(`${Pact_1.testURL}/wrongChain/api/v1/send`, async ({ request }) => {
        const body = await request.json();
        const requestKeys = body.cmds.map((cmd) => cmd.hash);
        const errorMsg = requestKeys
            .map((rk) => `Error: Validation failed for hash "${rk}": Transaction metadata (chain id, chainweb version) conflicts with this endpoint`)
            .join('\n');
        return new msw_1.HttpResponse(errorMsg, { status: 403 });
    }, { once: true }),
    msw_1.http.post(`${Pact_1.testURL}/duplicate/api/v1/send`, async ({ request }) => {
        const body = await request.json();
        const requestKeys = body.cmds.map((cmd) => cmd.hash);
        const errorMsg = requestKeys
            .map((rk) => `Error: Validation failed for hash "${rk}": Transaction already exists on chain`)
            .join('\n');
        return new msw_1.HttpResponse(errorMsg, { status: 403 });
    }, { once: true }),
];
const server = (0, node_1.setupServer)(...httpHandlers);
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
(0, vitest_1.test)('/send should return request keys of txs submitted', async () => {
    const commandStr = JSON.stringify(cryptography_utils_1.pactTestCommand);
    const keyPair = {
        publicKey: 'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',
        secretKey: '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',
    };
    const cmdWithOneSignature1 = (0, cryptography_utils_1.sign)(commandStr, keyPair);
    const sampleCommand1 = {
        cmd: commandStr,
        hash: cmdWithOneSignature1.hash,
        sigs: [
            typeof cmdWithOneSignature1.sig === 'string'
                ? { sig: cmdWithOneSignature1.sig }
                : undefined,
        ],
    };
    const signedCommand1 = (0, pactjs_1.ensureSignedCommand)(sampleCommand1);
    const expectedRequestKey1 = signedCommand1.hash;
    // A tx created for chain 0 of devnet using `pact -a`.
    const signedCommand2 = {
        hash: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',
        sigs: [
            {
                sig: '0df98906e0c7a6e380f72dac6211b37c321f6555f3eb20ee2736f37784a3edda54da3a15398079b44f474b1fc7f522ffa3ae004a67a0a0266ecc8c82b9a0220b',
            },
        ],
        cmd: '{"networkId":"development","payload":{"exec":{"data":null,"code":"(+ 1 2)"}},"signers":[{"pubKey":"f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f"}],"meta":{"creationTime":1655142318,"ttl":28800,"gasLimit":10000,"chainId":"0","gasPrice":1.0e-5,"sender":"k:f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f"},"nonce":"2022-06-13 17:45:18.211131 UTC"}',
    };
    const expectedRequestKey2 = 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q';
    const sendReq = (0, createSendRequest_1.createSendRequest)([
        signedCommand1,
        signedCommand2,
    ]);
    const responseExpected = {
        requestKeys: [expectedRequestKey1, expectedRequestKey2],
    };
    const responseActual = await (0, send_1.send)(sendReq, Pact_1.testURL);
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
(0, vitest_1.test)('/send should return error if sent to wrong chain id', async () => {
    // A tx created for chain 0 of devnet using `pact -a`.
    const signedCommand = {
        hash: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',
        sigs: [
            {
                sig: '0df98906e0c7a6e380f72dac6211b37c321f6555f3eb20ee2736f37784a3edda54da3a15398079b44f474b1fc7f522ffa3ae004a67a0a0266ecc8c82b9a0220b',
            },
        ],
        cmd: '{"networkId":"development","payload":{"exec":{"data":null,"code":"(+ 1 2)"}},"signers":[{"pubKey":"f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f"}],"meta":{"creationTime":1655142318,"ttl":28800,"gasLimit":10000,"chainId":"0","gasPrice":1.0e-5,"sender":"k:f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f"},"nonce":"2022-06-13 17:45:18.211131 UTC"}',
    };
    const sendReq = (0, createSendRequest_1.createSendRequest)([signedCommand]);
    const expectedErrorMsg = 'Error: Validation failed for hash "ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q": Transaction metadata (chain id, chainweb version) conflicts with this endpoint';
    const responseActual = (0, send_1.send)(sendReq, `${Pact_1.testURL}/wrongChain`);
    return (0, vitest_1.expect)(responseActual).rejects.toThrowError(expectedErrorMsg);
});
(0, vitest_1.test)('/send should return error if tx already exists on chain', async () => {
    // A tx created for chain 0 of devnet using `pact -a`.
    const signedCommand = {
        hash: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',
        sigs: [
            {
                sig: '0df98906e0c7a6e380f72dac6211b37c321f6555f3eb20ee2736f37784a3edda54da3a15398079b44f474b1fc7f522ffa3ae004a67a0a0266ecc8c82b9a0220b',
            },
        ],
        cmd: '{"networkId":"development","payload":{"exec":{"data":null,"code":"(+ 1 2)"}},"signers":[{"pubKey":"f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f"}],"meta":{"creationTime":1655142318,"ttl":28800,"gasLimit":10000,"chainId":"0","gasPrice":1.0e-5,"sender":"k:f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f"},"nonce":"2022-06-13 17:45:18.211131 UTC"}',
    };
    const sendReq = (0, createSendRequest_1.createSendRequest)([signedCommand]);
    const expectedErrorMsg = 'Error: Validation failed for hash "ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q": Transaction already exists on chain';
    const responseActual = (0, send_1.send)(sendReq, `${Pact_1.testURL}/duplicate`);
    return (0, vitest_1.expect)(responseActual).rejects.toThrowError(expectedErrorMsg);
});
//# sourceMappingURL=send.test.js.map