"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const pactjs_1 = require("@kadena/pactjs");
const vitest_1 = require("vitest");
const mkCap_1 = require("../mkCap");
(0, vitest_1.test)('should create a baseline cap with empty args', () => {
    const actual = (0, mkCap_1.mkCap)('coin.GAS');
    const expected = { name: 'coin.GAS', args: [] };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)('should create a cap with multiple arguments', () => {
    const actual = (0, mkCap_1.mkCap)('coin.TRANSFER', ['fromAcctName', 'toAcctName', 0.1]);
    const expected = {
        name: 'coin.TRANSFER',
        args: ['fromAcctName', 'toAcctName', 0.1],
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)('should create a cap with a boolean argument', () => {
    const actual = (0, mkCap_1.mkCap)('coin.TEST', [true]);
    const expected = { name: 'coin.TEST', args: [true] };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)('should create a cap with an array of pact values', () => {
    const actual = (0, mkCap_1.mkCap)('coin.TEST', [[true, 'randomStr', [1.234]], 200000]);
    const expected = {
        name: 'coin.TEST',
        args: [[true, 'randomStr', [1.234]], 200000],
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)("should create a cap with JavaScript's Number.MAX_SAFE_INTEGER", () => {
    const actual = (0, mkCap_1.mkCap)('coin.TEST', [Number.MAX_SAFE_INTEGER]);
    const expected = { name: 'coin.TEST', args: [9007199254740991] };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)("should create a cap with JavaScript's Number.MIN_SAFE_INTEGER", () => {
    const actual = (0, mkCap_1.mkCap)('coin.TEST', [Number.MIN_SAFE_INTEGER]);
    const expected = { name: 'coin.TEST', args: [-9007199254740991] };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)('should create a cap with number, pact integer, and pact decimal', () => {
    const bigInt = new pactjs_1.PactNumber('90071992547409910000').toPactInteger();
    const smallDec = new pactjs_1.PactNumber('-0.90071992547409910000').toPactDecimal();
    const actual = (0, mkCap_1.mkCap)('coin.TEST', [
        Number.MIN_SAFE_INTEGER,
        bigInt,
        smallDec,
    ]);
    const expected = {
        name: 'coin.TEST',
        args: [
            -9007199254740991,
            { int: '90071992547409910000' },
            { decimal: '-0.9007199254740991' },
        ],
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=mkCap.test.js.map