"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const listen_1 = require("../listen");
const execCommand_1 = require("./mockdata/execCommand");
const Pact_1 = require("./mockdata/Pact");
const httpHandlers = [
    msw_1.http.post(`${Pact_1.testURL}/api/v1/listen`, () => msw_1.HttpResponse.json(execCommand_1.localCommandResult), { once: true }),
];
const server = (0, node_1.setupServer)(...httpHandlers);
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
(0, vitest_1.test)('/listen should return result of tx queried', async () => {
    // A tx created for chain 0 of devnet using `pact -a`.
    const requestKey = {
        listen: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',
    };
    const commandResult1 = execCommand_1.localCommandResult;
    const localReq = requestKey;
    const responseExpected = commandResult1;
    const responseActual = await (0, listen_1.listen)(localReq, Pact_1.testURL);
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
//# sourceMappingURL=listen.test.js.map