{"version": 3, "file": "createListenRequest.test.js", "sourceRoot": "", "sources": ["../../src/tests/createListenRequest.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,gEAA6D;AAC7D,4DAAyD;AACzD,wDAAiD;AAEjD,IAAA,aAAI,EAAC,wFAAwF,EAAE,GAAG,EAAE;IAClG,MAAM,MAAM,GAAG,IAAA,yCAAmB,EAAC,IAAA,qCAAiB,EAAC,qBAAO,CAAC,CAAC,CAAC;IAC/D,MAAM,QAAQ,GAAG;QACf,MAAM,EAAE,qBAAO,CAAC,IAAI;KACrB,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { createListenRequest } from '../createListenRequest';\nimport { createSendRequest } from '../createSendRequest';\nimport { command } from './mockdata/execCommand';\n\ntest('Takes in command formatted for /send endpoint and outputs request for /listen endpoint', () => {\n  const actual = createListenRequest(createSendRequest(command));\n  const expected = {\n    listen: command.hash,\n  };\n\n  expect(expected).toEqual(actual);\n});\n"]}