"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const parseResponseTEXT_1 = require("../parseResponseTEXT");
(0, vitest_1.test)('should parse successful Response as expected type', async () => {
    const mockSuccessResponse = 'hello_world';
    const mockPromise = Promise.resolve(new Response(mockSuccessResponse));
    const mockedData = await mockPromise;
    const parsedResponse = await (0, parseResponseTEXT_1.parseResponseTEXT)(mockedData);
    (0, vitest_1.expect)(mockSuccessResponse).toEqual(parsedResponse);
});
(0, vitest_1.test)('should fail if Response promise was an error', async () => {
    const mockFailureResponse = 'Some mock error was thrown.';
    async function parseFailedResponse() {
        const mockPromise = Promise.reject(new Error(mockFailureResponse));
        const mockedData = await mockPromise;
        return (0, parseResponseTEXT_1.parseResponseTEXT)(mockedData);
    }
    return (0, vitest_1.expect)(parseFailedResponse).rejects.toThrowError(mockFailureResponse);
});
(0, vitest_1.test)('should fail if Response status not `ok`', async () => {
    const mockFailureResponse = 'Some API error message.';
    async function parseFailedResponse() {
        const mockPromise = Promise.resolve(new Response(mockFailureResponse, { status: 404 }));
        const mockedData = await mockPromise;
        return (0, parseResponseTEXT_1.parseResponseTEXT)(mockedData);
    }
    return (0, vitest_1.expect)(parseFailedResponse).rejects.toThrowError(mockFailureResponse);
});
//# sourceMappingURL=parseResponseTEXT.test.js.map