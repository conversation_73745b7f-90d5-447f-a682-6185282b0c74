{"version": 3, "file": "spv.test.js", "sourceRoot": "", "sources": ["../../src/tests/spv.test.ts"], "names": [], "mappings": ";;AAAA,6BAAyC;AACzC,mCAAuC;AACvC,mCAAsE;AAEtE,gCAA6B;AAC7B,0CAAwE;AAExE,MAAM,YAAY,GAAG;IACnB,UAAI,CAAC,IAAI,CAAC,GAAG,cAAO,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,kBAAY,CAAC,mBAAY,CAAC,CAAC;IACjE,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,eAAe,EACzB,GAAG,EAAE,CACH,IAAI,kBAAY,CACd,sFAAsF,EACtF,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EACH,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;CACF,CAAC;AAEF,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,GAAG,YAAY,CAAC,CAAC;AAE5C,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,IAAA,aAAI,EAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;IACxC,MAAM,MAAM,GAAsB,MAAM,IAAA,SAAG,EAAC,qBAAc,EAAE,cAAO,CAAC,CAAC;IACrE,MAAM,QAAQ,GAAgB,mBAAY,CAAC;IAC3C,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,gDAAgD,EAAE,GAAG,EAAE;IAC1D,MAAM,MAAM,GAA+B,IAAA,SAAG,EAC5C,qBAAc,EACd,GAAG,cAAO,WAAW,CACtB,CAAC;IACF,MAAM,gBAAgB,GACpB,sFAAsF,CAAC;IACzF,OAAO,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC", "sourcesContent": ["import { http, HttpResponse } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, afterEach, beforeAll, expect, test } from 'vitest';\nimport type { SPVResponse } from '../interfaces/PactAPI';\nimport { spv } from '../spv';\nimport { testSPVProof, testSPVRequest, testURL } from './mockdata/Pact';\n\nconst httpHandlers = [\n  http.post(`${testURL}/spv`, () => new HttpResponse(testSPVProof)),\n  http.post(\n    `${testURL}/tooyoung/spv`,\n    () =>\n      new HttpResponse(\n        'SPV target not reachable: target chain not reachable. Chainweb instance is too young',\n        { status: 400 },\n      ),\n    { once: true },\n  ),\n];\n\nconst server = setupServer(...httpHandlers);\n\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\ntest('/spv returns SPV proof', async () => {\n  const actual: string | Response = await spv(testSPVRequest, testURL);\n  const expected: SPVResponse = testSPVProof;\n  expect(actual).toEqual(expected);\n});\n\ntest('/spv returns error message when proof is young', () => {\n  const actual: Promise<string | Response> = spv(\n    testSPVRequest,\n    `${testURL}/tooyoung`,\n  );\n  const expectedErrorMsg =\n    'SPV target not reachable: target chain not reachable. Chainweb instance is too young';\n  return expect(actual).rejects.toThrowError(expectedErrorMsg);\n});\n"]}