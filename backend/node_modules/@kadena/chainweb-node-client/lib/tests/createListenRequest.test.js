"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createListenRequest_1 = require("../createListenRequest");
const createSendRequest_1 = require("../createSendRequest");
const execCommand_1 = require("./mockdata/execCommand");
(0, vitest_1.test)('Takes in command formatted for /send endpoint and outputs request for /listen endpoint', () => {
    const actual = (0, createListenRequest_1.createListenRequest)((0, createSendRequest_1.createSendRequest)(execCommand_1.command));
    const expected = {
        listen: execCommand_1.command.hash,
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=createListenRequest.test.js.map