{"version": 3, "file": "createSendRequest.test.js", "sourceRoot": "", "sources": ["../../src/tests/createSendRequest.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,4DAAyD;AACzD,wDAAiD;AAEjD,IAAA,aAAI,EAAC,4FAA4F,EAAE,GAAG,EAAE;IACtG,MAAM,MAAM,GAAG,IAAA,qCAAiB,EAAC,qBAAO,CAAC,CAAC;IAC1C,MAAM,QAAQ,GAAG;QACf,IAAI,EAAE,CAAC,qBAAO,CAAC;KAChB,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { createSendRequest } from '../createSendRequest';\nimport { command } from './mockdata/execCommand';\n\ntest('Takes in Pact command object and outputs command formatted specifically for a send request', () => {\n  const actual = createSendRequest(command);\n  const expected = {\n    cmds: [command],\n  };\n\n  expect(expected).toEqual(actual);\n});\n"]}