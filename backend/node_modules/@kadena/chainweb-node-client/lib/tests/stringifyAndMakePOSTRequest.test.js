"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const stringifyAndMakePOSTRequest_1 = require("../stringifyAndMakePOSTRequest");
(0, vitest_1.test)('should stringify body and create POST request', () => {
    const body = { name: 'hello', val: "'world'" };
    const actual = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(body);
    const expected = {
        headers: {
            'Content-Type': 'application/json',
        },
        method: 'POST',
        body: '{"name":"hello","val":"\'world\'"}',
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)('should stringify body and create POST request and add options', () => {
    const body = { name: 'hello', val: "'world'" };
    const actual = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(body, { keepalive: true });
    const expected = {
        headers: {
            'Content-Type': 'application/json',
        },
        keepalive: true,
        method: 'POST',
        body: '{"name":"hello","val":"\'world\'"}',
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=stringifyAndMakePOSTRequest.test.js.map