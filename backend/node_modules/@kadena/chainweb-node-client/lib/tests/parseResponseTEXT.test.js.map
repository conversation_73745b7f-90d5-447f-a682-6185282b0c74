{"version": 3, "file": "parseResponseTEXT.test.js", "sourceRoot": "", "sources": ["../../src/tests/parseResponseTEXT.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,4DAAyD;AAEzD,IAAA,aAAI,EAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;IAEnE,MAAM,mBAAmB,GAAiB,aAAa,CAAC;IACxD,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAEvE,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;IAErC,MAAM,cAAc,GAAiB,MAAM,IAAA,qCAAiB,EAC1D,UAAsB,CACvB,CAAC;IACF,IAAA,eAAM,EAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;IAC9D,MAAM,mBAAmB,GAAG,6BAA6B,CAAC;IAC1D,KAAK,UAAU,mBAAmB;QAChC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAEnE,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;QAErC,OAAO,IAAA,qCAAiB,EAAC,UAAsB,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,IAAA,eAAM,EAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;IACzD,MAAM,mBAAmB,GAAG,yBAAyB,CAAC;IACtD,KAAK,UAAU,mBAAmB;QAChC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CACjC,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CACnD,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;QAErC,OAAO,IAAA,qCAAiB,EAAC,UAAsB,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,IAAA,eAAM,EAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { parseResponseTEXT } from '../parseResponseTEXT';\n\ntest('should parse successful Response as expected type', async () => {\n  type MockTestType = string;\n  const mockSuccessResponse: MockTestType = 'hello_world';\n  const mockPromise = Promise.resolve(new Response(mockSuccessResponse));\n\n  const mockedData = await mockPromise;\n\n  const parsedResponse: MockTestType = await parseResponseTEXT(\n    mockedData as Response,\n  );\n  expect(mockSuccessResponse).toEqual(parsedResponse);\n});\n\ntest('should fail if Response promise was an error', async () => {\n  const mockFailureResponse = 'Some mock error was thrown.';\n  async function parseFailedResponse(): Promise<string> {\n    const mockPromise = Promise.reject(new Error(mockFailureResponse));\n\n    const mockedData = await mockPromise;\n\n    return parseResponseTEXT(mockedData as Response);\n  }\n\n  return expect(parseFailedResponse).rejects.toThrowError(mockFailureResponse);\n});\n\ntest('should fail if Response status not `ok`', async () => {\n  const mockFailureResponse = 'Some API error message.';\n  async function parseFailedResponse(): Promise<string> {\n    const mockPromise = Promise.resolve(\n      new Response(mockFailureResponse, { status: 404 }),\n    );\n\n    const mockedData = await mockPromise;\n\n    return parseResponseTEXT(mockedData as Response);\n  }\n\n  return expect(parseFailedResponse).rejects.toThrowError(mockFailureResponse);\n});\n"]}