{"version": 3, "file": "execCommand.js", "sourceRoot": "", "sources": ["../../../src/tests/mockdata/execCommand.ts"], "names": [], "mappings": ";;;AAIa,QAAA,OAAO,GAAa;IAC/B,IAAI,EAAE,6CAA6C;IACnD,IAAI,EAAE;QACJ;YACE,GAAG,EAAE,kIAAkI;SACxI;KACF;IACD,GAAG,EAAE,8gBAA8gB;CACphB,CAAC;AAEW,QAAA,kBAAkB,GAAwB;IACrD,MAAM,EAAE,6CAA6C;IACrD,IAAI,EAAE,IAAI;IACV,MAAM,EAAE;QACN,IAAI,EAAE,CAAC;QACP,MAAM,EAAE,SAAS;KAClB;IACD,GAAG,EAAE,CAAC;IACN,YAAY,EAAE,IAAI;IAClB,QAAQ,EAAE,IAAI;IACd,IAAI,EAAE,6CAA6C;CACpD,CAAC", "sourcesContent": ["// Test Case 1 - 01-system.yaml code from https://github.com/kadena-io/pact/blob/master/examples/accounts/scripts/01-system.yaml\nimport type { ICommand } from '@kadena/types';\nimport type { ILocalCommandResult } from '../../interfaces/PactAPI';\n\nexport const command: ICommand = {\n  hash: 'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  sigs: [\n    {\n      sig: '26d765e3b812d59d80ffbd034d4fc4a1a24f8d0c3929586575617089e5098d967955d348608b515ae9ff7871b46726ffc71252d53b9e562d5bcf3bfe66292906',\n    },\n  ],\n  cmd: '{\"networkId\":null,\"payload\":{\"exec\":{\"data\":{\"accounts-admin-keyset\":[\"ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d\"]},\"code\":\"(define-keyset \\'k (read-keyset \\\\\"accounts-admin-keyset\\\\\"))\\\\n(module system \\'k\\\\n  (defun get-system-time ()\\\\n    (time \\\\\"2017-10-31T12:00:00Z\\\\\")))\\\\n(get-system-time)\"}},\"signers\":[{\"pubKey\":\"ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d\"}],\"meta\":{\"creationTime\":0,\"ttl\":0,\"gasLimit\":0,\"chainId\":\"0\",\"gasPrice\":0,\"sender\":\"\"},\"nonce\":\"\\\\\"step01\\\\\"\"}',\n};\n\nexport const localCommandResult: ILocalCommandResult = {\n  reqKey: 'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  txId: null,\n  result: {\n    data: 3,\n    status: 'success',\n  },\n  gas: 0,\n  continuation: null,\n  metaData: null,\n  logs: 'wsATyGqckuIvlm89hhd2j4t6RMkCrcwJe_oeCYr7Th8',\n};\n"]}