"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const spv_1 = require("../spv");
const Pact_1 = require("./mockdata/Pact");
const httpHandlers = [
    msw_1.http.post(`${Pact_1.testURL}/spv`, () => new msw_1.HttpResponse(Pact_1.testSPVProof)),
    msw_1.http.post(`${Pact_1.testURL}/tooyoung/spv`, () => new msw_1.HttpResponse('SPV target not reachable: target chain not reachable. Chainweb instance is too young', { status: 400 }), { once: true }),
];
const server = (0, node_1.setupServer)(...httpHandlers);
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
(0, vitest_1.test)('/spv returns SPV proof', async () => {
    const actual = await (0, spv_1.spv)(Pact_1.testSPVRequest, Pact_1.testURL);
    const expected = Pact_1.testSPVProof;
    (0, vitest_1.expect)(actual).toEqual(expected);
});
(0, vitest_1.test)('/spv returns error message when proof is young', () => {
    const actual = (0, spv_1.spv)(Pact_1.testSPVRequest, `${Pact_1.testURL}/tooyoung`);
    const expectedErrorMsg = 'SPV target not reachable: target chain not reachable. Chainweb instance is too young';
    return (0, vitest_1.expect)(actual).rejects.toThrowError(expectedErrorMsg);
});
//# sourceMappingURL=spv.test.js.map