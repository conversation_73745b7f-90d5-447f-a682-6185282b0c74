{"version": 3, "file": "createPollRequest.test.js", "sourceRoot": "", "sources": ["../../src/tests/createPollRequest.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,4DAAyD;AACzD,4DAAyD;AACzD,wDAAiD;AAEjD,IAAA,aAAI,EAAC,sFAAsF,EAAE,GAAG,EAAE;IAChG,MAAM,MAAM,GAAG,IAAA,qCAAiB,EAAC,IAAA,qCAAiB,EAAC,qBAAO,CAAC,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG;QACf,WAAW,EAAE,CAAC,qBAAO,CAAC,IAAI,CAAC;KAC5B,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { createPollRequest } from '../createPollRequest';\nimport { createSendRequest } from '../createSendRequest';\nimport { command } from './mockdata/execCommand';\n\ntest('Takes in command formatted for /send endpoint and outputs request for /poll endpoint', () => {\n  const actual = createPollRequest(createSendRequest(command));\n  const expected = {\n    requestKeys: [command.hash],\n  };\n\n  expect(expected).toEqual(actual);\n});\n"]}