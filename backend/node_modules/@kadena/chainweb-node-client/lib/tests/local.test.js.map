{"version": 3, "file": "local.test.js", "sourceRoot": "", "sources": ["../../src/tests/local.test.ts"], "names": [], "mappings": ";;AAAA,mEAAkD;AAClD,2CAAqD;AAMrD,6BAAyC;AACzC,mCAAuC;AACvC,mCAAsE;AAMtE,oCAAiC;AACjC,0CAA2D;AAC3D,wDAA4D;AAE5D,MAAM,YAAY,GAAG;IACnB,UAAI,CAAC,IAAI,CAAC,GAAG,cAAO,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;QACnD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC;QACjE,OAAO,kBAAY,CAAC,IAAI,CAAC;YACvB,eAAe,EAAE,gCAAkB;YACnC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAClD,CAAC,CAAC;IACL,CAAC,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,GAAG,YAAY,CAAC,CAAC;AAE5C,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,IAAA,aAAI,EAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;IACrE,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAe,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG;QACf,SAAS,EACP,kEAAkE;QACpE,SAAS,EACP,kEAAkE;KACrE,CAAC;IACF,MAAM,oBAAoB,GAAsB,IAAA,yBAAI,EAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC5E,MAAM,cAAc,GAAqB;QACvC,GAAG,EAAE,WAAW;QAChB,IAAI,EAAE,oBAAoB,CAAC,IAAI;QAC/B,IAAI,EAAE;YACJ,OAAO,oBAAoB,CAAC,GAAG,KAAK,QAAQ;gBAC1C,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAoB,CAAC,GAAG,EAAE;gBACnC,CAAC,CAAC,SAAS;SACd;KACF,CAAC;IACF,MAAM,cAAc,GAAa,IAAA,4BAAmB,EAAC,cAAc,CAAC,CAAC;IAErE,MAAM,cAAc,GAAwB;QAC1C,GAAG,gCAAkB;QACrB,iBAAiB,EAAE,EAAE;KACtB,CAAC;IACF,MAAM,gBAAgB,GAAwB,cAAc,CAAC;IAC7D,MAAM,cAAc,GAA8B,MAAM,IAAA,aAAK,EAC3D,cAAc,EACd,cAAO,CACR,CAAC;IAEF,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,qEAAqE,EAAE,KAAK,IAAI,EAAE;IACrF,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAe,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG;QACf,SAAS,EACP,kEAAkE;QACpE,SAAS,EACP,kEAAkE;KACrE,CAAC;IACF,MAAM,oBAAoB,GAAsB,IAAA,yBAAI,EAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC5E,MAAM,cAAc,GAAqB;QACvC,GAAG,EAAE,WAAW;QAChB,IAAI,EAAE,oBAAoB,CAAC,IAAI;QAC/B,IAAI,EAAE;YACJ,OAAO,oBAAoB,CAAC,GAAG,KAAK,QAAQ;gBAC1C,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAoB,CAAC,GAAG,EAAE;gBACnC,CAAC,CAAC,SAAS;SACd;KACF,CAAC;IACF,MAAM,cAAc,GAAa,IAAA,4BAAmB,EAAC,cAAc,CAAC,CAAC;IAErE,MAAM,cAAc,GAAwB,gCAAkB,CAAC;IAC/D,MAAM,gBAAgB,GAAgC,cAAc,CAAC;IACrE,MAAM,cAAc,GAAG,MAAM,IAAA,aAAK,EAAC,cAAc,EAAE,cAAO,EAAE;QAC1D,SAAS,EAAE,KAAK;KACjB,CAAC,CAAC;IAEH,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,4GAA4G,EAAE,KAAK,IAAI,EAAE;IAC5H,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAe,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG;QACf,SAAS,EACP,kEAAkE;QACpE,SAAS,EACP,kEAAkE;KACrE,CAAC;IACF,MAAM,oBAAoB,GAAsB,IAAA,yBAAI,EAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC5E,MAAM,cAAc,GAAqB;QACvC,GAAG,EAAE,WAAW;QAChB,IAAI,EAAE,oBAAoB,CAAC,IAAI;QAC/B,IAAI,EAAE,CAAC,SAAS,CAAC;KAClB,CAAC;IAEF,MAAM,cAAc,GAAwB;QAC1C,GAAG,gCAAkB;QACrB,iBAAiB,EAAE,EAAE;KACtB,CAAC;IACF,MAAM,gBAAgB,GAAG,cAAc,CAAC;IACxC,MAAM,cAAc,GAAgC,MAAM,IAAA,aAAK,EAC7D,cAAc,EACd,cAAO,EACP;QACE,qBAAqB,EAAE,KAAK;KAC7B,CACF,CAAC;IAEF,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC", "sourcesContent": ["import { sign } from '@kadena/cryptography-utils';\nimport { ensureSignedCommand } from '@kadena/pactjs';\nimport type {\n  ICommand,\n  IUnsignedCommand,\n  SignatureWithHash,\n} from '@kadena/types';\nimport { HttpResponse, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, afterEach, beforeAll, expect, test } from 'vitest';\nimport type {\n  ICommandResult,\n  ILocalCommandResult,\n  LocalResultWithoutPreflight,\n} from '../interfaces/PactAPI';\nimport { local } from '../local';\nimport { pactTestCommand, testURL } from './mockdata/Pact';\nimport { localCommandResult } from './mockdata/execCommand';\n\nconst httpHandlers = [\n  http.post(`${testURL}/api/v1/local`, ({ request }) => {\n    const url = new URL(request.url);\n    const isPreflight = url.searchParams.get('preflight') === 'true';\n    return HttpResponse.json({\n      preflightResult: localCommandResult,\n      ...(isPreflight ? { preflightWarnings: [] } : {}),\n    });\n  }),\n];\n\nconst server = setupServer(...httpHandlers);\n\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\ntest('local should return preflight result of tx queried ', async () => {\n  const commandStr1 = JSON.stringify(pactTestCommand);\n  const keyPair1 = {\n    publicKey:\n      'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n    secretKey:\n      '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',\n  };\n  const cmdWithOneSignature1: SignatureWithHash = sign(commandStr1, keyPair1);\n  const sampleCommand1: IUnsignedCommand = {\n    cmd: commandStr1,\n    hash: cmdWithOneSignature1.hash,\n    sigs: [\n      typeof cmdWithOneSignature1.sig === 'string'\n        ? { sig: cmdWithOneSignature1.sig }\n        : undefined,\n    ],\n  };\n  const signedCommand1: ICommand = ensureSignedCommand(sampleCommand1);\n\n  const commandResult1: ILocalCommandResult = {\n    ...localCommandResult,\n    preflightWarnings: [],\n  };\n  const responseExpected: ILocalCommandResult = commandResult1;\n  const responseActual: ICommandResult | Response = await local(\n    signedCommand1,\n    testURL,\n  );\n\n  expect(responseExpected).toEqual(responseActual);\n});\n\ntest('local with `{preflight: false}` option returns non-preflight result', async () => {\n  const commandStr1 = JSON.stringify(pactTestCommand);\n  const keyPair1 = {\n    publicKey:\n      'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n    secretKey:\n      '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',\n  };\n  const cmdWithOneSignature1: SignatureWithHash = sign(commandStr1, keyPair1);\n  const sampleCommand1: IUnsignedCommand = {\n    cmd: commandStr1,\n    hash: cmdWithOneSignature1.hash,\n    sigs: [\n      typeof cmdWithOneSignature1.sig === 'string'\n        ? { sig: cmdWithOneSignature1.sig }\n        : undefined,\n    ],\n  };\n  const signedCommand1: ICommand = ensureSignedCommand(sampleCommand1);\n\n  const commandResult1: ILocalCommandResult = localCommandResult;\n  const responseExpected: LocalResultWithoutPreflight = commandResult1;\n  const responseActual = await local(signedCommand1, testURL, {\n    preflight: false,\n  });\n\n  expect(responseExpected).toEqual(responseActual);\n});\n\ntest('local with `{signatureVerification: false}` option returns preflight result without signature verification', async () => {\n  const commandStr1 = JSON.stringify(pactTestCommand);\n  const keyPair1 = {\n    publicKey:\n      'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n    secretKey:\n      '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',\n  };\n  const cmdWithOneSignature1: SignatureWithHash = sign(commandStr1, keyPair1);\n  const sampleCommand1: IUnsignedCommand = {\n    cmd: commandStr1,\n    hash: cmdWithOneSignature1.hash,\n    sigs: [undefined],\n  };\n\n  const commandResult1: ILocalCommandResult = {\n    ...localCommandResult,\n    preflightWarnings: [],\n  };\n  const responseExpected = commandResult1;\n  const responseActual: LocalResultWithoutPreflight = await local(\n    sampleCommand1,\n    testURL,\n    {\n      signatureVerification: false,\n    },\n  );\n\n  expect(responseExpected).toEqual(responseActual);\n});\n"]}