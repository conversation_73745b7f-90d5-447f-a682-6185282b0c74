{"version": 3, "file": "parseResponse.test.js", "sourceRoot": "", "sources": ["../../src/tests/parseResponse.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,oDAAiD;AAEjD,IAAA,aAAI,EAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;IAKnE,MAAM,mBAAmB,GAAkB;QACzC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QACvB,GAAG,EAAE,CAAC;KACP,CAAC;IACF,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CACjC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAClD,CAAC;IAEF,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;IAErC,MAAM,cAAc,GAAkB,MAAM,IAAA,6BAAa,EACvD,UAAsB,CACvB,CAAC;IACF,IAAA,eAAM,EAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;IAC9D,MAAM,mBAAmB,GAAG,6BAA6B,CAAC;IAC1D,KAAK,UAAU,mBAAmB;QAChC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAEnE,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;QAErC,OAAO,IAAA,6BAAa,EAAC,UAAsB,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO,IAAA,eAAM,EAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;IACzD,MAAM,mBAAmB,GAAG,yBAAyB,CAAC;IACtD,KAAK,UAAU,mBAAmB;QAChC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CACjC,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CACnD,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;QAErC,OAAO,IAAA,6BAAa,EAAC,UAAsB,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO,IAAA,eAAM,EAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,qEAAqE,EAAE,KAAK,IAAI,EAAE;IACrF,MAAM,eAAe,GAAQ;QAC3B,EAAE,EAAE,KAAK;QACT,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;KACpD,CAAC;IAEF,OAAO,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,eAA2B,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CACrE,eAAe,CAChB,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { parseResponse } from '../parseResponse';\n\ntest('should parse successful Response as expected type', async () => {\n  interface IMockTestType {\n    arr: Array<string>;\n    int: number;\n  }\n  const mockSuccessResponse: IMockTestType = {\n    arr: ['hello', 'world'],\n    int: 2,\n  };\n  const mockPromise = Promise.resolve(\n    new Response(JSON.stringify(mockSuccessResponse)),\n  );\n\n  const mockedData = await mockPromise;\n\n  const parsedResponse: IMockTestType = await parseResponse(\n    mockedData as Response,\n  );\n  expect(mockSuccessResponse).toEqual(parsedResponse);\n});\n\ntest('should fail if Response promise was an error', async () => {\n  const mockFailureResponse = 'Some mock error was thrown.';\n  async function parseFailedResponse(): Promise<unknown> {\n    const mockPromise = Promise.reject(new Error(mockFailureResponse));\n\n    const mockedData = await mockPromise;\n\n    return parseResponse(mockedData as Response);\n  }\n\n  return expect(parseFailedResponse).rejects.toThrowError(mockFailureResponse);\n});\n\ntest('should fail if Response status not `ok`', async () => {\n  const mockFailureResponse = 'Some API error message.';\n  async function parseFailedResponse(): Promise<unknown> {\n    const mockPromise = Promise.resolve(\n      new Response(mockFailureResponse, { status: 404 }),\n    );\n\n    const mockedData = await mockPromise;\n\n    return parseResponse(mockedData as Response);\n  }\n\n  return expect(parseFailedResponse).rejects.toThrowError(mockFailureResponse);\n});\n\ntest('returns response itself if ok is falsy and its not parsable to text', async () => {\n  const invalidResponse: any = {\n    ok: false,\n    text: () => Promise.reject(new Error('Some error')),\n  };\n\n  return expect(parseResponse(invalidResponse as Response)).resolves.toBe(\n    invalidResponse,\n  );\n});\n"]}