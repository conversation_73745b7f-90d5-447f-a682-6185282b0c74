{"version": 3, "file": "stringifyAndMakePOSTRequest.test.js", "sourceRoot": "", "sources": ["../../src/tests/stringifyAndMakePOSTRequest.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,gFAA6E;AAE7E,IAAA,aAAI,EAAC,+CAA+C,EAAE,GAAG,EAAE;IACzD,MAAM,IAAI,GAAW,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;IACvD,MAAM,MAAM,GAAG,IAAA,yDAA2B,EAAS,IAAI,CAAC,CAAC;IACzD,MAAM,QAAQ,GAAG;QACf,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;SACnC;QACD,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,oCAAoC;KAC3C,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,+DAA+D,EAAE,GAAG,EAAE;IACzE,MAAM,IAAI,GAAW,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;IACvD,MAAM,MAAM,GAAG,IAAA,yDAA2B,EAAS,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9E,MAAM,QAAQ,GAAG;QACf,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;SACnC;QACD,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,oCAAoC;KAC3C,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { stringifyAndMakePOSTRequest } from '../stringifyAndMakePOSTRequest';\n\ntest('should stringify body and create POST request', () => {\n  const body: object = { name: 'hello', val: \"'world'\" };\n  const actual = stringifyAndMakePOSTRequest<object>(body);\n  const expected = {\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    method: 'POST',\n    body: '{\"name\":\"hello\",\"val\":\"\\'world\\'\"}',\n  };\n\n  expect(expected).toEqual(actual);\n});\n\ntest('should stringify body and create POST request and add options', () => {\n  const body: object = { name: 'hello', val: \"'world'\" };\n  const actual = stringifyAndMakePOSTRequest<object>(body, { keepalive: true });\n  const expected = {\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    keepalive: true,\n    method: 'POST',\n    body: '{\"name\":\"hello\",\"val\":\"\\'world\\'\"}',\n  };\n\n  expect(expected).toEqual(actual);\n});\n"]}