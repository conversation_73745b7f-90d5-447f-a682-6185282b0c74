"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createPollRequest_1 = require("../createPollRequest");
const createSendRequest_1 = require("../createSendRequest");
const execCommand_1 = require("./mockdata/execCommand");
(0, vitest_1.test)('Takes in command formatted for /send endpoint and outputs request for /poll endpoint', () => {
    const actual = (0, createPollRequest_1.createPollRequest)((0, createSendRequest_1.createSendRequest)(execCommand_1.command));
    const expected = {
        requestKeys: [execCommand_1.command.hash],
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=createPollRequest.test.js.map