"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const parseResponse_1 = require("../parseResponse");
(0, vitest_1.test)('should parse successful Response as expected type', async () => {
    const mockSuccessResponse = {
        arr: ['hello', 'world'],
        int: 2,
    };
    const mockPromise = Promise.resolve(new Response(JSON.stringify(mockSuccessResponse)));
    const mockedData = await mockPromise;
    const parsedResponse = await (0, parseResponse_1.parseResponse)(mockedData);
    (0, vitest_1.expect)(mockSuccessResponse).toEqual(parsedResponse);
});
(0, vitest_1.test)('should fail if Response promise was an error', async () => {
    const mockFailureResponse = 'Some mock error was thrown.';
    async function parseFailedResponse() {
        const mockPromise = Promise.reject(new Error(mockFailureResponse));
        const mockedData = await mockPromise;
        return (0, parseResponse_1.parseResponse)(mockedData);
    }
    return (0, vitest_1.expect)(parseFailedResponse).rejects.toThrowError(mockFailureResponse);
});
(0, vitest_1.test)('should fail if Response status not `ok`', async () => {
    const mockFailureResponse = 'Some API error message.';
    async function parseFailedResponse() {
        const mockPromise = Promise.resolve(new Response(mockFailureResponse, { status: 404 }));
        const mockedData = await mockPromise;
        return (0, parseResponse_1.parseResponse)(mockedData);
    }
    return (0, vitest_1.expect)(parseFailedResponse).rejects.toThrowError(mockFailureResponse);
});
(0, vitest_1.test)('returns response itself if ok is falsy and its not parsable to text', async () => {
    const invalidResponse = {
        ok: false,
        text: () => Promise.reject(new Error('Some error')),
    };
    return (0, vitest_1.expect)((0, parseResponse_1.parseResponse)(invalidResponse)).resolves.toBe(invalidResponse);
});
//# sourceMappingURL=parseResponse.test.js.map