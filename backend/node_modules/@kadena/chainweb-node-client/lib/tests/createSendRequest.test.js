"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createSendRequest_1 = require("../createSendRequest");
const execCommand_1 = require("./mockdata/execCommand");
(0, vitest_1.test)('Takes in Pact command object and outputs command formatted specifically for a send request', () => {
    const actual = (0, createSendRequest_1.createSendRequest)(execCommand_1.command);
    const expected = {
        cmds: [execCommand_1.command],
    };
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=createSendRequest.test.js.map