{"version": 3, "file": "send.test.js", "sourceRoot": "", "sources": ["../../src/tests/send.test.ts"], "names": [], "mappings": ";;AAAA,mEAAmE;AACnE,2CAAqD;AAErD,6BAAyC;AACzC,mCAAuC;AACvC,mCAAsE;AACtE,4DAAyD;AAEzD,kCAA+B;AAC/B,0CAA0C;AAE1C,MAAM,YAAY,GAAG;IACnB,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,cAAc,EACxB,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QACpB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,kBAAY,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;IAC5C,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;IACD,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,yBAAyB,EACnC,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QACpB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,WAAW;aACzB,GAAG,CACF,CAAC,EAAE,EAAE,EAAE,CACL,sCAAsC,EAAE,mFAAmF,CAC9H;aACA,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,IAAI,kBAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;IACrD,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;IACD,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,wBAAwB,EAClC,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QACpB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,WAAW;aACzB,GAAG,CACF,CAAC,EAAE,EAAE,EAAE,CACL,sCAAsC,EAAE,wCAAwC,CACnF;aACA,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,IAAI,kBAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;IACrD,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;CACF,CAAC;AAEF,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,GAAG,YAAY,CAAC,CAAC;AAE5C,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,IAAA,aAAI,EAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;IACnE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,oCAAe,CAAC,CAAC;IACnD,MAAM,OAAO,GAAG;QACd,SAAS,EACP,kEAAkE;QACpE,SAAS,EACP,kEAAkE;KACrE,CAAC;IACF,MAAM,oBAAoB,GAAgB,IAAA,yBAAI,EAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACpE,MAAM,cAAc,GAAqB;QACvC,GAAG,EAAE,UAAU;QACf,IAAI,EAAE,oBAAoB,CAAC,IAAI;QAC/B,IAAI,EAAE;YACJ,OAAO,oBAAoB,CAAC,GAAG,KAAK,QAAQ;gBAC1C,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAoB,CAAC,GAAG,EAAE;gBACnC,CAAC,CAAC,SAAS;SACd;KACF,CAAC;IACF,MAAM,cAAc,GAAa,IAAA,4BAAmB,EAAC,cAAc,CAAC,CAAC;IACrE,MAAM,mBAAmB,GAAG,cAAc,CAAC,IAAI,CAAC;IAEhD,sDAAsD;IACtD,MAAM,cAAc,GAAa;QAC/B,IAAI,EAAE,6CAA6C;QACnD,IAAI,EAAE;YACJ;gBACE,GAAG,EAAE,kIAAkI;aACxI;SACF;QACD,GAAG,EAAE,gYAAgY;KACtY,CAAC;IACF,MAAM,mBAAmB,GAAG,6CAA6C,CAAC;IAC1E,MAAM,OAAO,GAAqB,IAAA,qCAAiB,EAAC;QAClD,cAAc;QACd,cAAc;KACf,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAiB;QACrC,WAAW,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;KACxD,CAAC;IACF,MAAM,cAAc,GAA4B,MAAM,IAAA,WAAI,EAAC,OAAO,EAAE,cAAO,CAAC,CAAC;IAC7E,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;IACrE,sDAAsD;IACtD,MAAM,aAAa,GAAa;QAC9B,IAAI,EAAE,6CAA6C;QACnD,IAAI,EAAE;YACJ;gBACE,GAAG,EAAE,kIAAkI;aACxI;SACF;QACD,GAAG,EAAE,gYAAgY;KACtY,CAAC;IAEF,MAAM,OAAO,GAAqB,IAAA,qCAAiB,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC;IAErE,MAAM,gBAAgB,GACpB,iKAAiK,CAAC;IACpK,MAAM,cAAc,GAAqC,IAAA,WAAI,EAC3D,OAAO,EACP,GAAG,cAAO,aAAa,CACxB,CAAC;IACF,OAAO,IAAA,eAAM,EAAC,cAAc,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACvE,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;IACzE,sDAAsD;IACtD,MAAM,aAAa,GAAa;QAC9B,IAAI,EAAE,6CAA6C;QACnD,IAAI,EAAE;YACJ;gBACE,GAAG,EAAE,kIAAkI;aACxI;SACF;QACD,GAAG,EAAE,gYAAgY;KACtY,CAAC;IACF,MAAM,OAAO,GAAqB,IAAA,qCAAiB,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC;IACrE,MAAM,gBAAgB,GACpB,sHAAsH,CAAC;IACzH,MAAM,cAAc,GAAqC,IAAA,WAAI,EAC3D,OAAO,EACP,GAAG,cAAO,YAAY,CACvB,CAAC;IACF,OAAO,IAAA,eAAM,EAAC,cAAc,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACvE,CAAC,CAAC,CAAC", "sourcesContent": ["import { pactTestCommand, sign } from '@kadena/cryptography-utils';\nimport { ensureSignedCommand } from '@kadena/pactjs';\nimport type { ICommand, IUnsignedCommand, SignCommand } from '@kadena/types';\nimport { HttpResponse, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, afterEach, beforeAll, expect, test } from 'vitest';\nimport { createSendRequest } from '../createSendRequest';\nimport type { ISendRequestBody, SendResponse } from '../interfaces/PactAPI';\nimport { send } from '../send';\nimport { testURL } from './mockdata/Pact';\n\nconst httpHandlers = [\n  http.post<never, ISendRequestBody>(\n    `${testURL}/api/v1/send`,\n    async ({ request }) => {\n      const body = await request.json();\n      const requestKeys = body.cmds.map((cmd) => cmd.hash);\n      return HttpResponse.json({ requestKeys });\n    },\n    { once: true },\n  ),\n  http.post<never, ISendRequestBody>(\n    `${testURL}/wrongChain/api/v1/send`,\n    async ({ request }) => {\n      const body = await request.json();\n      const requestKeys = body.cmds.map((cmd) => cmd.hash);\n      const errorMsg = requestKeys\n        .map(\n          (rk) =>\n            `Error: Validation failed for hash \"${rk}\": Transaction metadata (chain id, chainweb version) conflicts with this endpoint`,\n        )\n        .join('\\n');\n      return new HttpResponse(errorMsg, { status: 403 });\n    },\n    { once: true },\n  ),\n  http.post<never, ISendRequestBody>(\n    `${testURL}/duplicate/api/v1/send`,\n    async ({ request }) => {\n      const body = await request.json();\n      const requestKeys = body.cmds.map((cmd) => cmd.hash);\n      const errorMsg = requestKeys\n        .map(\n          (rk) =>\n            `Error: Validation failed for hash \"${rk}\": Transaction already exists on chain`,\n        )\n        .join('\\n');\n      return new HttpResponse(errorMsg, { status: 403 });\n    },\n    { once: true },\n  ),\n];\n\nconst server = setupServer(...httpHandlers);\n\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\ntest('/send should return request keys of txs submitted', async () => {\n  const commandStr = JSON.stringify(pactTestCommand);\n  const keyPair = {\n    publicKey:\n      'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n    secretKey:\n      '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',\n  };\n  const cmdWithOneSignature1: SignCommand = sign(commandStr, keyPair);\n  const sampleCommand1: IUnsignedCommand = {\n    cmd: commandStr,\n    hash: cmdWithOneSignature1.hash,\n    sigs: [\n      typeof cmdWithOneSignature1.sig === 'string'\n        ? { sig: cmdWithOneSignature1.sig }\n        : undefined,\n    ],\n  };\n  const signedCommand1: ICommand = ensureSignedCommand(sampleCommand1);\n  const expectedRequestKey1 = signedCommand1.hash;\n\n  // A tx created for chain 0 of devnet using `pact -a`.\n  const signedCommand2: ICommand = {\n    hash: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',\n    sigs: [\n      {\n        sig: '0df98906e0c7a6e380f72dac6211b37c321f6555f3eb20ee2736f37784a3edda54da3a15398079b44f474b1fc7f522ffa3ae004a67a0a0266ecc8c82b9a0220b',\n      },\n    ],\n    cmd: '{\"networkId\":\"development\",\"payload\":{\"exec\":{\"data\":null,\"code\":\"(+ 1 2)\"}},\"signers\":[{\"pubKey\":\"f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f\"}],\"meta\":{\"creationTime\":1655142318,\"ttl\":28800,\"gasLimit\":10000,\"chainId\":\"0\",\"gasPrice\":1.0e-5,\"sender\":\"k:f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f\"},\"nonce\":\"2022-06-13 17:45:18.211131 UTC\"}',\n  };\n  const expectedRequestKey2 = 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q';\n  const sendReq: ISendRequestBody = createSendRequest([\n    signedCommand1,\n    signedCommand2,\n  ]);\n\n  const responseExpected: SendResponse = {\n    requestKeys: [expectedRequestKey1, expectedRequestKey2],\n  };\n  const responseActual: Response | SendResponse = await send(sendReq, testURL);\n  expect(responseExpected).toEqual(responseActual);\n});\n\ntest('/send should return error if sent to wrong chain id', async () => {\n  // A tx created for chain 0 of devnet using `pact -a`.\n  const signedCommand: ICommand = {\n    hash: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',\n    sigs: [\n      {\n        sig: '0df98906e0c7a6e380f72dac6211b37c321f6555f3eb20ee2736f37784a3edda54da3a15398079b44f474b1fc7f522ffa3ae004a67a0a0266ecc8c82b9a0220b',\n      },\n    ],\n    cmd: '{\"networkId\":\"development\",\"payload\":{\"exec\":{\"data\":null,\"code\":\"(+ 1 2)\"}},\"signers\":[{\"pubKey\":\"f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f\"}],\"meta\":{\"creationTime\":1655142318,\"ttl\":28800,\"gasLimit\":10000,\"chainId\":\"0\",\"gasPrice\":1.0e-5,\"sender\":\"k:f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f\"},\"nonce\":\"2022-06-13 17:45:18.211131 UTC\"}',\n  };\n\n  const sendReq: ISendRequestBody = createSendRequest([signedCommand]);\n\n  const expectedErrorMsg =\n    'Error: Validation failed for hash \"ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q\": Transaction metadata (chain id, chainweb version) conflicts with this endpoint';\n  const responseActual: Promise<Response | SendResponse> = send(\n    sendReq,\n    `${testURL}/wrongChain`,\n  );\n  return expect(responseActual).rejects.toThrowError(expectedErrorMsg);\n});\n\ntest('/send should return error if tx already exists on chain', async () => {\n  // A tx created for chain 0 of devnet using `pact -a`.\n  const signedCommand: ICommand = {\n    hash: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',\n    sigs: [\n      {\n        sig: '0df98906e0c7a6e380f72dac6211b37c321f6555f3eb20ee2736f37784a3edda54da3a15398079b44f474b1fc7f522ffa3ae004a67a0a0266ecc8c82b9a0220b',\n      },\n    ],\n    cmd: '{\"networkId\":\"development\",\"payload\":{\"exec\":{\"data\":null,\"code\":\"(+ 1 2)\"}},\"signers\":[{\"pubKey\":\"f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f\"}],\"meta\":{\"creationTime\":1655142318,\"ttl\":28800,\"gasLimit\":10000,\"chainId\":\"0\",\"gasPrice\":1.0e-5,\"sender\":\"k:f89ef46927f506c70b6a58fd322450a936311dc6ac91f4ec3d8ef949608dbf1f\"},\"nonce\":\"2022-06-13 17:45:18.211131 UTC\"}',\n  };\n  const sendReq: ISendRequestBody = createSendRequest([signedCommand]);\n  const expectedErrorMsg =\n    'Error: Validation failed for hash \"ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q\": Transaction already exists on chain';\n  const responseActual: Promise<Response | SendResponse> = send(\n    sendReq,\n    `${testURL}/duplicate`,\n  );\n  return expect(responseActual).rejects.toThrowError(expectedErrorMsg);\n});\n"]}