{"version": 3, "file": "poll.test.js", "sourceRoot": "", "sources": ["../../src/tests/poll.test.ts"], "names": [], "mappings": ";;AAAA,6BAAyC;AACzC,mCAAuC;AACvC,mCAA2D;AAE3D,kCAA+B;AAC/B,wDAA4D;AAC5D,0CAA0C;AAE1C,MAAM,MAAM,GAAG,IAAA,kBAAW,GAAE,CAAC;AAE7B,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,IAAA,aAAI,EAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;IACnE,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,cAAc,EACxB,GAAG,EAAE,CACH,kBAAY,CAAC,IAAI,CAAC;QAChB,2CAA2C,EAAE,gCAAkB;KAChE,CAAC,EACJ,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CACF,CAAC;IACF,sDAAsD;IACtD,MAAM,aAAa,GAAqB;QACtC,WAAW,EAAE,CAAC,6CAA6C,CAAC;KAC7D,CAAC;IAEF,MAAM,aAAa,GAAkB;QACnC,2CAA2C,EAAE,gCAAkB;KAChE,CAAC;IACF,MAAM,QAAQ,GAAqB,aAAa,CAAC;IACjD,MAAM,gBAAgB,GAAkB,aAAa,CAAC;IACtD,MAAM,cAAc,GAA6B,MAAM,IAAA,WAAI,EACzD,QAAQ,EACR,cAAO,CACR,CAAC;IAEF,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;IAC9E,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,kCAAkC,EAC5C,GAAG,EAAE,CACH,kBAAY,CAAC,IAAI,CAAC;QAChB,2CAA2C,EAAE,gCAAkB;KAChE,CAAC,EACJ,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CACF,CAAC;IACF,MAAM,aAAa,GAAqB;QACtC,WAAW,EAAE,CAAC,6CAA6C,CAAC;KAC7D,CAAC;IAEF,MAAM,aAAa,GAAkB;QACnC,2CAA2C,EAAE,gCAAkB;KAChE,CAAC;IACF,MAAM,gBAAgB,GAAkB,aAAa,CAAC;IACtD,MAAM,QAAQ,GAAqB,aAAa,CAAC;IAEjD,MAAM,cAAc,GAA6B,MAAM,IAAA,WAAI,EACzD,QAAQ,EACR,cAAO,EACP,CAAC,CACF,CAAC;IAEF,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC", "sourcesContent": ["import { http, HttpResponse } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, beforeAll, expect, test } from 'vitest';\nimport type { IPollRequestBody, IPollResponse } from '../interfaces/PactAPI';\nimport { poll } from '../poll';\nimport { localCommandResult } from './mockdata/execCommand';\nimport { testURL } from './mockdata/Pact';\n\nconst server = setupServer();\n\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterAll(() => server.close());\n\ntest('/poll should return request keys of txs submitted', async () => {\n  server.resetHandlers(\n    http.post(\n      `${testURL}/api/v1/poll`,\n      () =>\n        HttpResponse.json({\n          pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: localCommandResult,\n        }),\n      { once: true },\n    ),\n  );\n  // A tx created for chain 0 of devnet using `pact -a`.\n  const signedCommand: IPollRequestBody = {\n    requestKeys: ['ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q'],\n  };\n\n  const commandResult: IPollResponse = {\n    pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: localCommandResult,\n  };\n  const localReq: IPollRequestBody = signedCommand;\n  const responseExpected: IPollResponse = commandResult;\n  const responseActual: Response | IPollResponse = await poll(\n    localReq,\n    testURL,\n  );\n\n  expect(responseExpected).toEqual(responseActual);\n});\n\ntest(\"confirmationsDepth should be added to the url's searchParams\", async () => {\n  server.resetHandlers(\n    http.post(\n      `${testURL}/api/v1/poll?confirmationDepth=5`,\n      () =>\n        HttpResponse.json({\n          pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: localCommandResult,\n        }),\n      { once: true },\n    ),\n  );\n  const signedCommand: IPollRequestBody = {\n    requestKeys: ['ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q'],\n  };\n\n  const commandResult: IPollResponse = {\n    pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: localCommandResult,\n  };\n  const responseExpected: IPollResponse = commandResult;\n  const localReq: IPollRequestBody = signedCommand;\n\n  const responseActual: Response | IPollResponse = await poll(\n    localReq,\n    testURL,\n    5,\n  );\n\n  expect(responseExpected).toEqual(responseActual);\n});\n"]}