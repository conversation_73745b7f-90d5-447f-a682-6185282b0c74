"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const poll_1 = require("../poll");
const execCommand_1 = require("./mockdata/execCommand");
const Pact_1 = require("./mockdata/Pact");
const server = (0, node_1.setupServer)();
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterAll)(() => server.close());
(0, vitest_1.test)('/poll should return request keys of txs submitted', async () => {
    server.resetHandlers(msw_1.http.post(`${Pact_1.testURL}/api/v1/poll`, () => msw_1.HttpResponse.json({
        pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: execCommand_1.localCommandResult,
    }), { once: true }));
    // A tx created for chain 0 of devnet using `pact -a`.
    const signedCommand = {
        requestKeys: ['ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q'],
    };
    const commandResult = {
        pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: execCommand_1.localCommandResult,
    };
    const localReq = signedCommand;
    const responseExpected = commandResult;
    const responseActual = await (0, poll_1.poll)(localReq, Pact_1.testURL);
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
(0, vitest_1.test)("confirmationsDepth should be added to the url's searchParams", async () => {
    server.resetHandlers(msw_1.http.post(`${Pact_1.testURL}/api/v1/poll?confirmationDepth=5`, () => msw_1.HttpResponse.json({
        pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: execCommand_1.localCommandResult,
    }), { once: true }));
    const signedCommand = {
        requestKeys: ['ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q'],
    };
    const commandResult = {
        pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik: execCommand_1.localCommandResult,
    };
    const responseExpected = commandResult;
    const localReq = signedCommand;
    const responseActual = await (0, poll_1.poll)(localReq, Pact_1.testURL, 5);
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
//# sourceMappingURL=poll.test.js.map