"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const __1 = require("../");
(0, vitest_1.test)('Expects functions to be exposed', async () => {
    (0, vitest_1.expect)(__1.listen).toBeDefined();
    (0, vitest_1.expect)(__1.poll).toBeDefined();
    (0, vitest_1.expect)(__1.send).toBeDefined();
    (0, vitest_1.expect)(__1.spv).toBeDefined();
    (0, vitest_1.expect)(__1.local).toBeDefined();
    (0, vitest_1.expect)(__1.mkCap).toBeDefined();
    (0, vitest_1.expect)(__1.parseResponse).toBeDefined();
    (0, vitest_1.expect)(__1.parseResponseTEXT).toBeDefined();
    (0, vitest_1.expect)(__1.stringifyAndMakePOSTRequest).toBeDefined();
    (0, vitest_1.expect)(__1.createListenRequest).toBeDefined();
    (0, vitest_1.expect)(__1.createPollRequest).toBeDefined();
    (0, vitest_1.expect)(__1.createSendRequest).toBeDefined();
});
//# sourceMappingURL=index.test.js.map