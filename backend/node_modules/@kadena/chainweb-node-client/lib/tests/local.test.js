"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cryptography_utils_1 = require("@kadena/cryptography-utils");
const pactjs_1 = require("@kadena/pactjs");
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const local_1 = require("../local");
const Pact_1 = require("./mockdata/Pact");
const execCommand_1 = require("./mockdata/execCommand");
const httpHandlers = [
    msw_1.http.post(`${Pact_1.testURL}/api/v1/local`, ({ request }) => {
        const url = new URL(request.url);
        const isPreflight = url.searchParams.get('preflight') === 'true';
        return msw_1.HttpResponse.json({
            preflightResult: execCommand_1.localCommandResult,
            ...(isPreflight ? { preflightWarnings: [] } : {}),
        });
    }),
];
const server = (0, node_1.setupServer)(...httpHandlers);
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
(0, vitest_1.test)('local should return preflight result of tx queried ', async () => {
    const commandStr1 = JSON.stringify(Pact_1.pactTestCommand);
    const keyPair1 = {
        publicKey: 'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',
        secretKey: '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',
    };
    const cmdWithOneSignature1 = (0, cryptography_utils_1.sign)(commandStr1, keyPair1);
    const sampleCommand1 = {
        cmd: commandStr1,
        hash: cmdWithOneSignature1.hash,
        sigs: [
            typeof cmdWithOneSignature1.sig === 'string'
                ? { sig: cmdWithOneSignature1.sig }
                : undefined,
        ],
    };
    const signedCommand1 = (0, pactjs_1.ensureSignedCommand)(sampleCommand1);
    const commandResult1 = {
        ...execCommand_1.localCommandResult,
        preflightWarnings: [],
    };
    const responseExpected = commandResult1;
    const responseActual = await (0, local_1.local)(signedCommand1, Pact_1.testURL);
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
(0, vitest_1.test)('local with `{preflight: false}` option returns non-preflight result', async () => {
    const commandStr1 = JSON.stringify(Pact_1.pactTestCommand);
    const keyPair1 = {
        publicKey: 'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',
        secretKey: '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',
    };
    const cmdWithOneSignature1 = (0, cryptography_utils_1.sign)(commandStr1, keyPair1);
    const sampleCommand1 = {
        cmd: commandStr1,
        hash: cmdWithOneSignature1.hash,
        sigs: [
            typeof cmdWithOneSignature1.sig === 'string'
                ? { sig: cmdWithOneSignature1.sig }
                : undefined,
        ],
    };
    const signedCommand1 = (0, pactjs_1.ensureSignedCommand)(sampleCommand1);
    const commandResult1 = execCommand_1.localCommandResult;
    const responseExpected = commandResult1;
    const responseActual = await (0, local_1.local)(signedCommand1, Pact_1.testURL, {
        preflight: false,
    });
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
(0, vitest_1.test)('local with `{signatureVerification: false}` option returns preflight result without signature verification', async () => {
    const commandStr1 = JSON.stringify(Pact_1.pactTestCommand);
    const keyPair1 = {
        publicKey: 'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',
        secretKey: '8693e641ae2bbe9ea802c736f42027b03f86afe63cae315e7169c9c496c17332',
    };
    const cmdWithOneSignature1 = (0, cryptography_utils_1.sign)(commandStr1, keyPair1);
    const sampleCommand1 = {
        cmd: commandStr1,
        hash: cmdWithOneSignature1.hash,
        sigs: [undefined],
    };
    const commandResult1 = {
        ...execCommand_1.localCommandResult,
        preflightWarnings: [],
    };
    const responseExpected = commandResult1;
    const responseActual = await (0, local_1.local)(sampleCommand1, Pact_1.testURL, {
        signatureVerification: false,
    });
    (0, vitest_1.expect)(responseExpected).toEqual(responseActual);
});
//# sourceMappingURL=local.test.js.map