{"version": 3, "file": "listen.test.js", "sourceRoot": "", "sources": ["../../src/tests/listen.test.ts"], "names": [], "mappings": ";;AAAA,6BAAyC;AACzC,mCAAuC;AACvC,mCAAsE;AAMtE,sCAAmC;AACnC,wDAA4D;AAC5D,0CAA0C;AAE1C,MAAM,YAAY,GAAG;IACnB,UAAI,CAAC,IAAI,CACP,GAAG,cAAO,gBAAgB,EAC1B,GAAG,EAAE,CAAC,kBAAY,CAAC,IAAI,CAAC,gCAAkB,CAAC,EAC3C,EAAE,IAAI,EAAE,IAAI,EAAE,CACf;CACF,CAAC;AAEF,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,GAAG,YAAY,CAAC,CAAC;AAE5C,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,IAAA,aAAI,EAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;IAC5D,sDAAsD;IACtD,MAAM,UAAU,GAAuB;QACrC,MAAM,EAAE,6CAA6C;KACtD,CAAC;IAEF,MAAM,cAAc,GAAmB,gCAAkB,CAAC;IAC1D,MAAM,QAAQ,GAAuB,UAAU,CAAC;IAChD,MAAM,gBAAgB,GAAmB,cAAc,CAAC;IACxD,MAAM,cAAc,GAA8B,MAAM,IAAA,eAAM,EAC5D,QAAQ,EACR,cAAO,CACR,CAAC;IAEF,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC", "sourcesContent": ["import { http, HttpResponse } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, afterEach, beforeAll, expect, test } from 'vitest';\nimport type {\n  ICommandResult,\n  IListenRequestBody,\n  ListenResponse,\n} from '../interfaces/PactAPI';\nimport { listen } from '../listen';\nimport { localCommandResult } from './mockdata/execCommand';\nimport { testURL } from './mockdata/Pact';\n\nconst httpHandlers = [\n  http.post(\n    `${testURL}/api/v1/listen`,\n    () => HttpResponse.json(localCommandResult),\n    { once: true },\n  ),\n];\n\nconst server = setupServer(...httpHandlers);\n\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\ntest('/listen should return result of tx queried', async () => {\n  // A tx created for chain 0 of devnet using `pact -a`.\n  const requestKey: IListenRequestBody = {\n    listen: 'ATGCYPMNzdGcFh9Iik73KfMkgURIxaF91Ze4sHFsH8Q',\n  };\n\n  const commandResult1: ListenResponse = localCommandResult;\n  const localReq: IListenRequestBody = requestKey;\n  const responseExpected: ListenResponse = commandResult1;\n  const responseActual: ICommandResult | Response = await listen(\n    localReq,\n    testURL,\n  );\n\n  expect(responseExpected).toEqual(responseActual);\n});\n"]}