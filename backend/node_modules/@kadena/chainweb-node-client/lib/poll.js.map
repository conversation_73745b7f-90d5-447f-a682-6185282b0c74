{"version": 3, "file": "poll.js", "sourceRoot": "", "sources": ["../src/poll.ts"], "names": [], "mappings": ";;;AAEA,mDAAgD;AAChD,+EAA4E;AAC5E,yCAAsC;AACtC;;;;;;;;;;;GAWG;AACI,KAAK,UAAU,IAAI,CACxB,WAA6B,EAC7B,OAAe,EACf,iBAAiB,GAAG,CAAC,EACrB,WAA+B;IAE/B,MAAM,OAAO,GAAG,IAAA,yDAA2B,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtE,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,cAAc,CAAC,CAAC;IAClD,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;QAC1B,OAAO,CAAC,YAAY,CAAC,MAAM,CACzB,mBAAmB,EACnB,iBAAiB,CAAC,QAAQ,EAAE,CAC7B,CAAC;IACJ,CAAC;IACD,MAAM,QAAQ,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1D,OAAO,IAAA,6BAAa,EAAgB,QAAQ,CAAC,CAAC;AAChD,CAAC;AAhBD,oBAgBC", "sourcesContent": ["import type { IPollRequestBody, IPollResponse } from './interfaces/PactAPI';\nimport type { ClientRequestInit } from './local';\nimport { parseResponse } from './parseResponse';\nimport { stringifyAndMakePOSTRequest } from './stringifyAndMakePOSTRequest';\nimport { fetch } from './utils/fetch';\n/**\n * Allows polling for one or more transaction results by request key.\n * Returns an Array of the transaction results we polled for.\n * If a transaction is missing, then it might still be in the mempool, or might have expired.\n *\n * @param requestBody - The request keys of transactions submitted to the server\n *                      that we want to know the results of.\n *                      Must be non-empty list.\n * @param apiHost - API host running a Pact-enabled server.\n *\n * @alpha\n */\nexport async function poll(\n  requestBody: IPollRequestBody,\n  apiHost: string,\n  confirmationDepth = 0,\n  requestInit?: ClientRequestInit,\n): Promise<IPollResponse> {\n  const request = stringifyAndMakePOSTRequest(requestBody, requestInit);\n  const pollUrl = new URL(`${apiHost}/api/v1/poll`);\n  if (confirmationDepth > 0) {\n    pollUrl.searchParams.append(\n      'confirmationDepth',\n      confirmationDepth.toString(),\n    );\n  }\n  const response = await fetch(pollUrl.toString(), request);\n  return parseResponse<IPollResponse>(response);\n}\n"]}