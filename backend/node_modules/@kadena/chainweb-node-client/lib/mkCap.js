"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mkCap = void 0;
/**
 * Helper function for creating a pact capability object.
 * Output can be used with the `mkSignerCList` function.
 * @param name - Qualified name of the capability.
 * @param args - Array of PactValue arguments the capability expects (default: empty array).
 * @alpha
 */
function mkCap(name, args = []) {
    return {
        name,
        args,
    };
}
exports.mkCap = mkCap;
//# sourceMappingURL=mkCap.js.map