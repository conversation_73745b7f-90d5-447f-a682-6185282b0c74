{"version": 3, "file": "createSendRequest.js", "sourceRoot": "", "sources": ["../src/createSendRequest.ts"], "names": [], "mappings": ";;;AAGA;;;;GAIG;AACH,SAAgB,iBAAiB,CAC/B,QAA+B;IAE/B,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;KACtD,CAAC;AACJ,CAAC;AAND,8CAMC", "sourcesContent": ["import type { ICommand } from '@kadena/types';\nimport type { ISendRequestBody } from './interfaces/PactAPI';\n\n/**\n * Makes outer wrapper for a 'send' endpoint.\n * @alpha\n * @param commands - one or an array of commands, see mkSingleCmd\n */\nexport function createSendRequest(\n  commands: ICommand | ICommand[],\n): ISendRequestBody {\n  return {\n    cmds: Array.isArray(commands) ? commands : [commands],\n  };\n}\n"]}