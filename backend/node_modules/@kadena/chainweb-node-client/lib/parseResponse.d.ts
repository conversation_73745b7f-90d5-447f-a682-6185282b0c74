import type { ILocalCommandResult, ILocalResult } from './interfaces/PactAPI';
/**
 * Parses raw `fetch` response into a typed JSON value.
 *
 * Corresponds to `parseRes` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546
 * @alpha
 */
export declare function parseResponse<T>(response: Response): Promise<T>;
/**
 * @alpha
 */
export declare function parsePreflight(commandResult: ILocalResult): ILocalCommandResult;
//# sourceMappingURL=parseResponse.d.ts.map