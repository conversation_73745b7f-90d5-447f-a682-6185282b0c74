"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listen = void 0;
const parseResponse_1 = require("./parseResponse");
const stringifyAndMakePOSTRequest_1 = require("./stringifyAndMakePOSTRequest");
const fetch_1 = require("./utils/fetch");
/**
 * Blocking request for single command result.
 *
 * @param requestBody - The request key of transaction submitted to the server that we want to know the results of.
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
async function listen(requestBody, apiHost, requestInit) {
    const request = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(requestBody, requestInit);
    const listenUrl = new URL(`${apiHost}/api/v1/listen`);
    const response = await (0, fetch_1.fetch)(listenUrl.toString(), request);
    return (0, parseResponse_1.parseResponse)(response);
}
exports.listen = listen;
//# sourceMappingURL=listen.js.map