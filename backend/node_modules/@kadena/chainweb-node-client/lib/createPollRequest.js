"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPollRequest = void 0;
const cryptography_utils_1 = require("@kadena/cryptography-utils");
/**
 * Given an exec 'send' message, prepare a message for 'poll' endpoint.
 * @alpha
 * @param request - JSON with "cmds" field, see 'mkPublicSend'
 * @returns Object with "requestKeys" for polling.
 */
function createPollRequest({ cmds, }) {
    return { requestKeys: (0, cryptography_utils_1.unique)(cmds.map(({ hash }) => hash)) };
}
exports.createPollRequest = createPollRequest;
//# sourceMappingURL=createPollRequest.js.map