{"version": 3, "file": "createPollRequest.js", "sourceRoot": "", "sources": ["../src/createPollRequest.ts"], "names": [], "mappings": ";;;AAAA,mEAAoD;AAGpD;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,EAChC,IAAI,GACa;IACjB,OAAO,EAAE,WAAW,EAAE,IAAA,2BAAM,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;AAC/D,CAAC;AAJD,8CAIC", "sourcesContent": ["import { unique } from '@kadena/cryptography-utils';\nimport type { IPollRequestBody, ISendRequestBody } from './interfaces/PactAPI';\n\n/**\n * Given an exec 'send' message, prepare a message for 'poll' endpoint.\n * @alpha\n * @param request - JSON with \"cmds\" field, see 'mkPublicSend'\n * @returns Object with \"requestKeys\" for polling.\n */\nexport function createPollRequest({\n  cmds,\n}: ISendRequestBody): IPollRequestBody {\n  return { requestKeys: unique(cmds.map(({ hash }) => hash)) };\n}\n"]}