"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createListenRequest = void 0;
const cryptography_utils_1 = require("@kadena/cryptography-utils");
/**
 * Given an exec 'send' message, prepare a message for 'listen' endpoint.
 * @alpha
 * @param request - The JSON request object with "cmds" field, see 'mkPublicSend'. Only takes first element.
 * @returns Object with "requestKey" for polling.
 */
function createListenRequest({ cmds, }) {
    return { listen: (0, cryptography_utils_1.unique)(cmds.map(({ hash }) => hash))[0] };
}
exports.createListenRequest = createListenRequest;
//# sourceMappingURL=createListenRequest.js.map