{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../src/utils/fetch.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAa,QAAA,KAAK,GAA4B,CAAC,CAC7C,GAAG,IAAyC,EAC5C,EAAE;IACF,IACE,OAAO,UAAU,KAAK,WAAW;QACjC,OAAO,UAAU,CAAC,KAAK,KAAK,WAAW,EACvC,CAAC;QACD,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;QACzE,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;QACzE,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO,kDAAO,aAAa,IAAE,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;QAC9C,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC,CAAuC,CAAC", "sourcesContent": ["export const fetch: typeof globalThis.fetch = ((\n  ...args: Parameters<typeof globalThis.fetch>\n) => {\n  if (\n    typeof globalThis !== 'undefined' &&\n    typeof globalThis.fetch !== 'undefined'\n  ) {\n    return globalThis.fetch(...args);\n  }\n\n  if (typeof window !== 'undefined' && typeof window.fetch !== 'undefined') {\n    return window.fetch(...args);\n  }\n\n  if (typeof global !== 'undefined' && typeof global.fetch !== 'undefined') {\n    return global.fetch(...args);\n  }\n\n  return import('cross-fetch').then(({ fetch }) => {\n    return fetch(...args);\n  });\n}) as unknown as typeof globalThis.fetch;\n"]}