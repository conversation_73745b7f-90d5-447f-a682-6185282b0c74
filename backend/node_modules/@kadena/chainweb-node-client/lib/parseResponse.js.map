{"version": 3, "file": "parseResponse.js", "sourceRoot": "", "sources": ["../src/parseResponse.ts"], "names": [], "mappings": ";;;AACA;;;;;;GAMG;AACI,KAAK,UAAU,aAAa,CAAI,QAAkB;IACvD,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QAChB,OAAO,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAM,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,IAAI,CAAC;YACH,oBAAoB;YAEpB,MAAM,YAAY,GAAW,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,QAAwB,CAAC;QAClC,CAAC;IACH,CAAC;AACH,CAAC;AAbD,sCAaC;AAED;;GAEG;AACH,SAAgB,cAAc,CAC5B,aAA2B;IAE3B,IAAI,iBAAiB,IAAI,aAAa,EAAE,CAAC;QACvC,OAAO;YACL,GAAG,aAAa,CAAC,eAAe;YAChC,iBAAiB,EAAE,aAAa,CAAC,iBAAiB;SACnD,CAAC;IACJ,CAAC;;QAAM,OAAO,aAAa,CAAC;AAC9B,CAAC;AATD,wCASC", "sourcesContent": ["import type { ILocalCommandResult, ILocalResult } from './interfaces/PactAPI';\n/**\n * Parses raw `fetch` response into a typed JSON value.\n *\n * Corresponds to `parseRes` function:\n * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546\n * @alpha\n */\nexport async function parseResponse<T>(response: Response): Promise<T> {\n  if (response.ok) {\n    return (await response.json()) as T;\n  } else {\n    try {\n      // Handle API errors\n\n      const textResponse: string = await response.text();\n      return Promise.reject(new Error(textResponse));\n    } catch (error) {\n      return response as unknown as T;\n    }\n  }\n}\n\n/**\n * @alpha\n */\nexport function parsePreflight(\n  commandResult: ILocalResult,\n): ILocalCommandResult {\n  if ('preflightResult' in commandResult) {\n    return {\n      ...commandResult.preflightResult,\n      preflightWarnings: commandResult.preflightWarnings,\n    };\n  } else return commandResult;\n}\n"]}