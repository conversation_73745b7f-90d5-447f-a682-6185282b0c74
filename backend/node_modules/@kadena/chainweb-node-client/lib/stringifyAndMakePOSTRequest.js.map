{"version": 3, "file": "stringifyAndMakePOSTRequest.js", "sourceRoot": "", "sources": ["../src/stringifyAndMakePOSTRequest.ts"], "names": [], "mappings": ";;;AAEA;;;;;;GAMG;AACH,SAAgB,2BAA2B,CACzC,IAAO,EACP,WAA+B;IAE/B,OAAO;QACL,GAAG,WAAW;QACd,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO;SACxB;QACD,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AACJ,CAAC;AAbD,kEAaC", "sourcesContent": ["import type { ClientRequestInit } from './local';\n\n/**\n * Formats API request body to use with `fetch` function.\n *\n * Corresponds to `mkReq` function:\n * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L533\n * @alpha\n */\nexport function stringifyAndMakePOSTRequest<T>(\n  body: T,\n  requestInit?: ClientRequestInit,\n) {\n  return {\n    ...requestInit,\n    headers: {\n      'Content-Type': 'application/json',\n      ...requestInit?.headers,\n    },\n    method: 'POST',\n    body: JSON.stringify(body),\n  };\n}\n"]}