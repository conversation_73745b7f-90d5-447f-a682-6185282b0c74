import type { IPollRequestBody, ISendRequestBody } from './interfaces/PactAPI';
/**
 * Given an exec 'send' message, prepare a message for 'poll' endpoint.
 * @alpha
 * @param request - JSON with "cmds" field, see 'mkPublicSend'
 * @returns Object with "requestKeys" for polling.
 */
export declare function createPollRequest({ cmds, }: ISendRequestBody): IPollRequestBody;
//# sourceMappingURL=createPollRequest.d.ts.map