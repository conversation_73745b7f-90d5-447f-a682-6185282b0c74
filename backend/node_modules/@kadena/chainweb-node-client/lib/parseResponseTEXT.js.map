{"version": 3, "file": "parseResponseTEXT.js", "sourceRoot": "", "sources": ["../src/parseResponseTEXT.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACI,KAAK,UAAU,iBAAiB,CAAC,QAAkB;IACxD,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QAChB,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,oBAAoB;QACpB,MAAM,YAAY,GAAW,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AARD,8CAQC", "sourcesContent": ["/**\n * Parses raw `fetch` response into text.\n *\n * Corresponds to `parseRes` function:\n * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546\n * @alpha\n */\nexport async function parseResponseTEXT(response: Response): Promise<string> {\n  if (response.ok) {\n    return await response.text();\n  } else {\n    // Handle API errors\n    const TEXTResponse: string = await response.text();\n    return Promise.reject(new Error(TEXTResponse));\n  }\n}\n"]}