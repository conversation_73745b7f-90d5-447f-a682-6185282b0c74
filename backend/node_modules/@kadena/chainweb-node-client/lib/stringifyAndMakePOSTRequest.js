"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.stringifyAndMakePOSTRequest = void 0;
/**
 * Formats API request body to use with `fetch` function.
 *
 * Corresponds to `mkReq` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L533
 * @alpha
 */
function stringifyAndMakePOSTRequest(body, requestInit) {
    return {
        ...requestInit,
        headers: {
            'Content-Type': 'application/json',
            ...requestInit === null || requestInit === void 0 ? void 0 : requestInit.headers,
        },
        method: 'POST',
        body: JSON.stringify(body),
    };
}
exports.stringifyAndMakePOSTRequest = stringifyAndMakePOSTRequest;
//# sourceMappingURL=stringifyAndMakePOSTRequest.js.map