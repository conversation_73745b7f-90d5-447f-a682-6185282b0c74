"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.poll = void 0;
const parseResponse_1 = require("./parseResponse");
const stringifyAndMakePOSTRequest_1 = require("./stringifyAndMakePOSTRequest");
const fetch_1 = require("./utils/fetch");
/**
 * Allows polling for one or more transaction results by request key.
 * Returns an Array of the transaction results we polled for.
 * If a transaction is missing, then it might still be in the mempool, or might have expired.
 *
 * @param requestBody - The request keys of transactions submitted to the server
 *                      that we want to know the results of.
 *                      Must be non-empty list.
 * @param apiHost - API host running a Pact-enabled server.
 *
 * @alpha
 */
async function poll(requestBody, apiHost, confirmationDepth = 0, requestInit) {
    const request = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(requestBody, requestInit);
    const pollUrl = new URL(`${apiHost}/api/v1/poll`);
    if (confirmationDepth > 0) {
        pollUrl.searchParams.append('confirmationDepth', confirmationDepth.toString());
    }
    const response = await (0, fetch_1.fetch)(pollUrl.toString(), request);
    return (0, parseResponse_1.parseResponse)(response);
}
exports.poll = poll;
//# sourceMappingURL=poll.js.map