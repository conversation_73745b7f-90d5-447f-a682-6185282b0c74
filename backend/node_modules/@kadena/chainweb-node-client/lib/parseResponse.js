"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parsePreflight = exports.parseResponse = void 0;
/**
 * Parses raw `fetch` response into a typed JSON value.
 *
 * Corresponds to `parseRes` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546
 * @alpha
 */
async function parseResponse(response) {
    if (response.ok) {
        return (await response.json());
    }
    else {
        try {
            // Handle API errors
            const textResponse = await response.text();
            return Promise.reject(new Error(textResponse));
        }
        catch (error) {
            return response;
        }
    }
}
exports.parseResponse = parseResponse;
/**
 * @alpha
 */
function parsePreflight(commandResult) {
    if ('preflightResult' in commandResult) {
        return {
            ...commandResult.preflightResult,
            preflightWarnings: commandResult.preflightWarnings,
        };
    }
    else
        return commandResult;
}
exports.parsePreflight = parsePreflight;
//# sourceMappingURL=parseResponse.js.map