import type { ICommandResult, IListenRequestBody } from './interfaces/PactAPI';
import type { ClientRequestInit } from './local';
/**
 * Blocking request for single command result.
 *
 * @param requestBody - The request key of transaction submitted to the server that we want to know the results of.
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function listen(requestBody: IListenRequestBody, apiHost: string, requestInit?: ClientRequestInit): Promise<ICommandResult>;
//# sourceMappingURL=listen.d.ts.map