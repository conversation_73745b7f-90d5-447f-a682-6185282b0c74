{"version": 3, "file": "listen.js", "sourceRoot": "", "sources": ["../src/listen.ts"], "names": [], "mappings": ";;;AAEA,mDAAgD;AAChD,+EAA4E;AAC5E,yCAAsC;AAEtC;;;;;;GAMG;AACI,KAAK,UAAU,MAAM,CAC1B,WAA+B,EAC/B,OAAe,EACf,WAA+B;IAE/B,MAAM,OAAO,GAAG,IAAA,yDAA2B,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,gBAAgB,CAAC,CAAC;IAEtD,MAAM,QAAQ,GAAG,MAAM,IAAA,aAAK,EAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5D,OAAO,IAAA,6BAAa,EAAiB,QAAQ,CAAC,CAAC;AACjD,CAAC;AAVD,wBAUC", "sourcesContent": ["import type { ICommandResult, IListenRequestBody } from './interfaces/PactAPI';\nimport type { ClientRequestInit } from './local';\nimport { parseResponse } from './parseResponse';\nimport { stringifyAndMakePOSTRequest } from './stringifyAndMakePOSTRequest';\nimport { fetch } from './utils/fetch';\n\n/**\n * Blocking request for single command result.\n *\n * @param requestBody - The request key of transaction submitted to the server that we want to know the results of.\n * @param apiHost - API host running a Pact-enabled server.\n * @alpha\n */\nexport async function listen(\n  requestBody: IListenRequestBody,\n  apiHost: string,\n  requestInit?: ClientRequestInit,\n): Promise<ICommandResult> {\n  const request = stringifyAndMakePOSTRequest(requestBody, requestInit);\n  const listenUrl = new URL(`${apiHost}/api/v1/listen`);\n\n  const response = await fetch(listenUrl.toString(), request);\n  return parseResponse<ICommandResult>(response);\n}\n"]}