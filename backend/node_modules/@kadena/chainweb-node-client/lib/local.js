"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertIUnsignedTransactionToNoSig = exports.localRaw = exports.local = void 0;
const pactjs_1 = require("@kadena/pactjs");
const parseResponse_1 = require("./parseResponse");
const stringifyAndMakePOSTRequest_1 = require("./stringifyAndMakePOSTRequest");
const fetch_1 = require("./utils/fetch");
/**
 * Blocking/sync call to submit a command for non-transactional execution.
 * In a blockchain environment this would be a node-local “dirty read”.
 * Any database writes or changes to the environment are rolled back.
 *
 * @param requestBody - Pact command to submit to server (non-transactional).
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
async function local(requestBody, apiHost, options) {
    const { signatureVerification = true, preflight = true, ...requestInit } = options !== null && options !== void 0 ? options : {};
    if (!signatureVerification) {
        requestBody = convertIUnsignedTransactionToNoSig(requestBody);
    }
    const body = (0, pactjs_1.ensureSignedCommand)(requestBody);
    const result = await localRaw(body, apiHost, {
        preflight,
        signatureVerification,
        ...requestInit,
    });
    return (0, parseResponse_1.parsePreflight)(result);
}
exports.local = local;
/**
 * Blocking/sync call to submit a command for non-transactional execution.
 * In a blockchain environment this would be a node-local “dirty read”.
 * Any database writes or changes to the environment are rolled back.
 *
 * @param requestBody - Pact command to submit to server (non-transactional).
 * @param apiHost - API host running a Pact-enabled server.
 * @param options - option query to enable preflight and signatureVerification
 * @alpha
 */
async function localRaw(requestBody, apiHost, { preflight, signatureVerification, ...requestInit }) {
    const request = (0, stringifyAndMakePOSTRequest_1.stringifyAndMakePOSTRequest)(requestBody, requestInit);
    const localUrlWithQueries = new URL(`${apiHost}/api/v1/local`);
    localUrlWithQueries.searchParams.append('preflight', preflight.toString());
    localUrlWithQueries.searchParams.append('signatureVerification', signatureVerification.toString());
    const response = await (0, fetch_1.fetch)(localUrlWithQueries.toString(), request);
    return (0, parseResponse_1.parseResponse)(response);
}
exports.localRaw = localRaw;
/**
 * @alpha
 */
function convertIUnsignedTransactionToNoSig(transaction) {
    return {
        ...transaction,
        sigs: transaction.sigs.map((s) => s && typeof s.sig === 'string'
            ? s
            : { ...s, sig: '' }),
    };
}
exports.convertIUnsignedTransactionToNoSig = convertIUnsignedTransactionToNoSig;
//# sourceMappingURL=local.js.map