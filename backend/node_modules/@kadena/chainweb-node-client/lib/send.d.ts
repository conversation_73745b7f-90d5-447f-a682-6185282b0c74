import type { ISendRequestBody, SendResponse } from './interfaces/PactAPI';
import type { ClientRequestInit } from './local';
/**
 * Asynchronous submission of one or more public (unencrypted) commands to the blockchain for execution.
 *
 * Corresponds to `fetchSendRaw` and `fetchSend` functions:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L601
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L589
 *
 * @param requestBody - Non-empty array of Pact commands to submit to server.
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function send(requestBody: ISendRequestBody, apiHost: string, requestInit?: ClientRequestInit): Promise<SendResponse>;
//# sourceMappingURL=send.d.ts.map