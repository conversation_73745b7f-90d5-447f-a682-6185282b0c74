"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseResponseTEXT = void 0;
/**
 * Parses raw `fetch` response into text.
 *
 * Corresponds to `parseRes` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546
 * @alpha
 */
async function parseResponseTEXT(response) {
    if (response.ok) {
        return await response.text();
    }
    else {
        // Handle API errors
        const TEXTResponse = await response.text();
        return Promise.reject(new Error(TEXTResponse));
    }
}
exports.parseResponseTEXT = parseResponseTEXT;
//# sourceMappingURL=parseResponseTEXT.js.map