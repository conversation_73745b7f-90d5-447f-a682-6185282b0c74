{"version": 3, "file": "local.js", "sourceRoot": "", "sources": ["../src/local.ts"], "names": [], "mappings": ";;;AAAA,2CAAqD;AAQrD,mDAAgE;AAChE,+EAA4E;AAC5E,yCAAsC;AAwBtC;;;;;;;;GAQG;AACI,KAAK,UAAU,KAAK,CACzB,WAA6B,EAC7B,OAAe,EACf,OAAW;IAEX,MAAM,EACJ,qBAAqB,GAAG,IAAI,EAC5B,SAAS,GAAG,IAAI,EAChB,GAAG,WAAW,EACf,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IAElB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC3B,WAAW,GAAG,kCAAkC,CAAC,WAAW,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,IAAI,GAAG,IAAA,4BAAmB,EAAC,WAAW,CAAC,CAAC;IAC9C,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE;QAC3C,SAAS;QACT,qBAAqB;QACrB,GAAG,WAAW;KACf,CAAC,CAAC;IAEH,OAAO,IAAA,8BAAc,EAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAvBD,sBAuBC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,QAAQ,CAC5B,WAA6B,EAC7B,OAAe,EACf,EACE,SAAS,EACT,qBAAqB,EACrB,GAAG,WAAW,EAIf;IAED,MAAM,OAAO,GAAG,IAAA,yDAA2B,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtE,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,eAAe,CAAC,CAAC;IAE/D,mBAAmB,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC3E,mBAAmB,CAAC,YAAY,CAAC,MAAM,CACrC,uBAAuB,EACvB,qBAAqB,CAAC,QAAQ,EAAE,CACjC,CAAC;IAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,aAAK,EAAC,mBAAmB,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACtE,OAAO,IAAA,6BAAa,EAAoC,QAAQ,CAAC,CAAC;AACpE,CAAC;AAvBD,4BAuBC;AAED;;GAEG;AACH,SAAgB,kCAAkC,CAChD,WAA6B;IAE7B,OAAO;QACL,GAAG,WAAW;QACd,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC/B,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,KAAK,QAAQ;YAC5B,CAAC,CAAE,CAAoB;YACvB,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CACtB;KACF,CAAC;AACJ,CAAC;AAXD,gFAWC", "sourcesContent": ["import { ensureSignedCommand } from '@kadena/pactjs';\nimport type { ICom<PERSON>, ISignatureJson, IUnsignedCommand } from '@kadena/types';\nimport type {\n  ICommandResult,\n  ILocalCommandResult,\n  IPreflightResult,\n  LocalRequestBody,\n} from './interfaces/PactAPI';\nimport { parsePreflight, parseResponse } from './parseResponse';\nimport { stringifyAndMakePOSTRequest } from './stringifyAndMakePOSTRequest';\nimport { fetch } from './utils/fetch';\n\n/**\n * @alpha\n */\nexport type ClientRequestInit = Omit<RequestInit, 'method' | 'body'>;\n\n/**\n * @alpha\n */\nexport interface ILocalOptions extends ClientRequestInit {\n  preflight?: boolean;\n  signatureVerification?: boolean;\n}\n\n/**\n * @alpha\n */\nexport type LocalResponse<Opt extends ILocalOptions> = Opt extends {\n  preflight?: true;\n}\n  ? ILocalCommandResult\n  : ICommandResult;\n\n/**\n * Blocking/sync call to submit a command for non-transactional execution.\n * In a blockchain environment this would be a node-local “dirty read”.\n * Any database writes or changes to the environment are rolled back.\n *\n * @param requestBody - Pact command to submit to server (non-transactional).\n * @param apiHost - API host running a Pact-enabled server.\n * @alpha\n */\nexport async function local<T extends ILocalOptions>(\n  requestBody: LocalRequestBody,\n  apiHost: string,\n  options?: T,\n): Promise<LocalResponse<T>> {\n  const {\n    signatureVerification = true,\n    preflight = true,\n    ...requestInit\n  } = options ?? {};\n\n  if (!signatureVerification) {\n    requestBody = convertIUnsignedTransactionToNoSig(requestBody);\n  }\n\n  const body = ensureSignedCommand(requestBody);\n  const result = await localRaw(body, apiHost, {\n    preflight,\n    signatureVerification,\n    ...requestInit,\n  });\n\n  return parsePreflight(result);\n}\n\n/**\n * Blocking/sync call to submit a command for non-transactional execution.\n * In a blockchain environment this would be a node-local “dirty read”.\n * Any database writes or changes to the environment are rolled back.\n *\n * @param requestBody - Pact command to submit to server (non-transactional).\n * @param apiHost - API host running a Pact-enabled server.\n * @param options - option query to enable preflight and signatureVerification\n * @alpha\n */\nexport async function localRaw(\n  requestBody: LocalRequestBody,\n  apiHost: string,\n  {\n    preflight,\n    signatureVerification,\n    ...requestInit\n  }: ILocalOptions & {\n    signatureVerification: boolean;\n    preflight: boolean;\n  },\n): Promise<IPreflightResult | ICommandResult> {\n  const request = stringifyAndMakePOSTRequest(requestBody, requestInit);\n  const localUrlWithQueries = new URL(`${apiHost}/api/v1/local`);\n\n  localUrlWithQueries.searchParams.append('preflight', preflight.toString());\n  localUrlWithQueries.searchParams.append(\n    'signatureVerification',\n    signatureVerification.toString(),\n  );\n\n  const response = await fetch(localUrlWithQueries.toString(), request);\n  return parseResponse<IPreflightResult | ICommandResult>(response);\n}\n\n/**\n * @alpha\n */\nexport function convertIUnsignedTransactionToNoSig(\n  transaction: IUnsignedCommand,\n): ICommand {\n  return {\n    ...transaction,\n    sigs: transaction.sigs.map((s) =>\n      s && typeof s.sig === 'string'\n        ? (s as ISignatureJson)\n        : { ...s, sig: '' },\n    ),\n  };\n}\n"]}