{"version": 3, "file": "send.js", "sourceRoot": "", "sources": ["../src/send.ts"], "names": [], "mappings": ";;;AAEA,mDAAgD;AAChD,+EAA4E;AAC5E,yCAAsC;AACtC;;;;;;;;;;GAUG;AACI,KAAK,UAAU,IAAI,CACxB,WAA6B,EAC7B,OAAe,EACf,WAA+B;IAE/B,MAAM,OAAO,GAAG,IAAA,yDAA2B,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtE,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,cAAc,CAAC,CAAC;IAElD,MAAM,QAAQ,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1D,OAAO,IAAA,6BAAa,EAAe,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAVD,oBAUC", "sourcesContent": ["import type { ISendRequestBody, SendResponse } from './interfaces/PactAPI';\nimport type { ClientRequestInit } from './local';\nimport { parseResponse } from './parseResponse';\nimport { stringifyAndMakePOSTRequest } from './stringifyAndMakePOSTRequest';\nimport { fetch } from './utils/fetch';\n/**\n * Asynchronous submission of one or more public (unencrypted) commands to the blockchain for execution.\n *\n * Corresponds to `fetchSendRaw` and `fetchSend` functions:\n * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L601\n * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L589\n *\n * @param requestBody - Non-empty array of Pact commands to submit to server.\n * @param apiHost - API host running a Pact-enabled server.\n * @alpha\n */\nexport async function send(\n  requestBody: ISendRequestBody,\n  apiHost: string,\n  requestInit?: ClientRequestInit,\n): Promise<SendResponse> {\n  const request = stringifyAndMakePOSTRequest(requestBody, requestInit);\n  const sendUrl = new URL(`${apiHost}/api/v1/send`);\n\n  const response = await fetch(sendUrl.toString(), request);\n  return parseResponse<SendResponse>(response);\n}\n"]}