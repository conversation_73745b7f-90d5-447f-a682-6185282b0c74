import type { IBase64Url } from '@kadena/types';
import type { ICap } from '@kadena/types';
import type { ICommand } from '@kadena/types';
import type { IMetaData } from '@kadena/types';
import type { IPactEvent } from '@kadena/types';
import type { IPactExec } from '@kadena/types';
import type { IUnsignedCommand } from '@kadena/types';
import type { PactValue } from '@kadena/types';
import type { SPVProof } from '@kadena/types';

/**
 * @alpha
 */
export declare const CHAINS: readonly ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19"];

/**
 * Stringified Chainweb chain numbers.
 *
 * @see https://stackoverflow.com/a/45257357/1463352
 * @alpha
 */
export declare type ChainwebChainId = (typeof CHAINS)[number];

/**
 * Different Chainweb network versions.
 * @alpha
 */
export declare type ChainwebNetworkId = 'mainnet01' | 'testnet04' | 'development';

/**
 * @alpha
 */
export declare type ClientRequestInit = Omit<RequestInit, 'method' | 'body'>;

/**
 * @alpha
 */
export declare function convertIUnsignedTransactionToNoSig(transaction: IUnsignedCommand): ICommand;

/**
 * Given an exec 'send' message, prepare a message for 'listen' endpoint.
 * @alpha
 * @param request - The JSON request object with "cmds" field, see 'mkPublicSend'. Only takes first element.
 * @returns Object with "requestKey" for polling.
 */
export declare function createListenRequest({ cmds, }: ISendRequestBody): IListenRequestBody;

/**
 * Given an exec 'send' message, prepare a message for 'poll' endpoint.
 * @alpha
 * @param request - JSON with "cmds" field, see 'mkPublicSend'
 * @returns Object with "requestKeys" for polling.
 */
export declare function createPollRequest({ cmds, }: ISendRequestBody): IPollRequestBody;

/**
 * Makes outer wrapper for a 'send' endpoint.
 * @alpha
 * @param commands - one or an array of commands, see mkSingleCmd
 */
export declare function createSendRequest(commands: ICommand | ICommand[]): ISendRequestBody;

/**
 * Platform-specific information on the block that executed a transaction.
 *
 * @param blockHash - Block hash of the block containing the transaction.
 * @param blockTime - POSIX time when the block was mined.
 * @param blockHeight - Block height of the block.
 * @param prevBlockHash - Parent Block hash of the containing block.
 * @param publicMeta - Platform-specific data provided by the request.
 *
 *
 */
declare interface IChainwebResponseMetaData {
    blockHash: string;
    blockTime: number;
    blockHeight: number;
    prevBlockHash: string;
    publicMeta?: IMetaData;
}

/**
 * API result of attempting to execute a pact transaction.
 *
 * @param reqKey - Unique ID of a pact transaction, equivalent to the payload hash.
 * @param txId - Database-internal transaction tracking ID.
 *               Absent when transaction was not successful.
 *               Expected to be non-negative 64-bit integers and
 *               are expected to be monotonically increasing.
 * @param result - Pact execution result, either a Pact error or the output (a PactValue) of the last pact expression in the transaction.
 * @param gas - Gas units consummed by the transaction as a 64-bit integer.
 * @param logs - Backend-specific value providing image of database logs.
 * @param continuation - Describes the result of a defpact execution, if one occurred.
 * @param metaData - Platform-specific information on the block that executed the transaction.
 * @param events - Optional list of Pact events emitted during the transaction.
 *
 *
 * @alpha
 */
export declare interface ICommandResult {
    reqKey: IBase64Url;
    txId: number | null;
    result: IPactResultSuccess | IPactResultError;
    gas: number;
    logs: string | null;
    continuation: IPactExec | null;
    metaData: IChainwebResponseMetaData | null;
    events?: Array<IPactEvent>;
}

/**
 * Request type of /listen endpoint.
 *
 * @param listen - Single request key (or command hash) to listen for.
 *
 * @alpha
 */
export declare interface IListenRequestBody {
    listen: IBase64Url;
}

/**
 * API result of attempting to execute a pact transaction.
 *
 * @param reqKey - Unique ID of a pact transaction, equivalent to the payload hash.
 * @param txId - Database-internal transaction tracking ID.
 *               Absent when transaction was not successful.
 *               Expected to be non-negative 64-bit integers and
 *               are expected to be monotonically increasing.
 * @param result - Pact execution result, either a Pact error or the output (a PactValue) of the last pact expression in the transaction.
 * @param gas - Gas units consummed by the transaction as a 64-bit integer.
 * @param logs - Backend-specific value providing image of database logs.
 * @param continuation - Describes the result of a defpact execution, if one occurred.
 * @param metaData - Platform-specific information on the block that executed the transaction.
 * @param events - Optional list of Pact events emitted during the transaction.
 * @param preflightWarnings - Optional list of Preflight warnings on /local result.
 *
 *
 * @alpha
 */
export declare interface ILocalCommandResult extends ICommandResult {
    preflightWarnings?: Array<string>;
}

/**
 * @alpha
 */
export declare interface ILocalOptions extends ClientRequestInit {
    preflight?: boolean;
    signatureVerification?: boolean;
}

/**
 * @alpha
 */
export declare type ILocalResult = IPreflightResult | ICommandResult;

/**
 * The result of a pact execution when an error occurs.
 * If the result is from a committed transaction from the /send endpoint, the error will only list the type of error that occured.
 * If the result is from the /local endpoint, the error will be an object.
 *
 * @alpha
 */
export declare interface IPactResultError {
    status: 'failure';
    error: {
        callStack: any;
        type: string;
        message: string;
        info: string;
    };
}

/**
 * The result of a pact execution when no errors occur.
 * The data will be the value returned or generated by the pact code.
 *
 * @alpha
 */
export declare interface IPactResultSuccess {
    status: 'success';
    data: PactValue;
}

/**
 * Request type of /poll endpoint.
 *
 * @param requestKeys - List of request keys (or command hashes) to poll for.
 *
 * @alpha
 */
export declare interface IPollRequestBody {
    requestKeys: Array<IBase64Url>;
}

/**
 * @alpha
 */
export declare interface IPollResponse {
    [key: IBase64Url]: ICommandResult;
}

/**
 * @alpha
 */
export declare interface IPreflightResult {
    preflightResult: ICommandResult;
    preflightWarnings: [];
}

/**
 * A Request Key is the blake2b-256 bit hash of a Pact command.
 * They might also be called Transaction IDs.
 * A list of them are returned by the /send endpoint.
 * @alpha
 */
export declare interface IRequestKeys {
    requestKeys: Array<IBase64Url>;
}

/**
 * Request type of /send endpoint.
 *
 * @param cmds - Non-empty array of Pact commands (or transactions) to submit to server.
 * @alpha
 */
export declare interface ISendRequestBody {
    cmds: Array<ICommand>;
}

/**
 * Request type of /spv endpoint.
 *
 * @param requestKey - Request Key of an initiated cross chain transaction at the source chain.
 * @param targetChainId - Target chain id of the cross chain transaction.
 *
 * @alpha
 */
export declare interface ISPVRequestBody {
    requestKey: IBase64Url;
    targetChainId: ChainwebChainId;
}

/**
 * Blocking request for single command result.
 *
 * @param requestBody - The request key of transaction submitted to the server that we want to know the results of.
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function listen(requestBody: IListenRequestBody, apiHost: string, requestInit?: ClientRequestInit): Promise<ICommandResult>;

/**
 * @alpha
 */
export declare type ListenResponse = ICommandResult;

/**
 * Blocking/sync call to submit a command for non-transactional execution.
 * In a blockchain environment this would be a node-local “dirty read”.
 * Any database writes or changes to the environment are rolled back.
 *
 * @param requestBody - Pact command to submit to server (non-transactional).
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function local<T extends ILocalOptions>(requestBody: LocalRequestBody, apiHost: string, options?: T): Promise<LocalResponse<T>>;

/**
 * Blocking/sync call to submit a command for non-transactional execution.
 * In a blockchain environment this would be a node-local “dirty read”.
 * Any database writes or changes to the environment are rolled back.
 *
 * @param requestBody - Pact command to submit to server (non-transactional).
 * @param apiHost - API host running a Pact-enabled server.
 * @param options - option query to enable preflight and signatureVerification
 * @alpha
 */
export declare function localRaw(requestBody: LocalRequestBody, apiHost: string, { preflight, signatureVerification, ...requestInit }: ILocalOptions & {
    signatureVerification: boolean;
    preflight: boolean;
}): Promise<IPreflightResult | ICommandResult>;

/**
 * @alpha
 */
export declare type LocalRequestBody = ICommand | IUnsignedCommand;

/**
 * @alpha
 */
export declare type LocalResponse<Opt extends ILocalOptions> = Opt extends {
    preflight?: true;
} ? ILocalCommandResult : ICommandResult;

/**
 * @alpha
 */
export declare type LocalResultWithoutPreflight = Omit<ILocalCommandResult, 'preflightWarnings'>;

/**
 * Helper function for creating a pact capability object.
 * Output can be used with the `mkSignerCList` function.
 * @param name - Qualified name of the capability.
 * @param args - Array of PactValue arguments the capability expects (default: empty array).
 * @alpha
 */
export declare function mkCap(name: string, args?: Array<PactValue>): ICap;

/**
 * @alpha
 */
export declare function parsePreflight(commandResult: ILocalResult): ILocalCommandResult;

/**
 * Parses raw `fetch` response into a typed JSON value.
 *
 * Corresponds to `parseRes` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546
 * @alpha
 */
export declare function parseResponse<T>(response: Response): Promise<T>;

/**
 * Parses raw `fetch` response into text.
 *
 * Corresponds to `parseRes` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L546
 * @alpha
 */
export declare function parseResponseTEXT(response: Response): Promise<string>;

/**
 * Allows polling for one or more transaction results by request key.
 * Returns an Array of the transaction results we polled for.
 * If a transaction is missing, then it might still be in the mempool, or might have expired.
 *
 * @param requestBody - The request keys of transactions submitted to the server
 *                      that we want to know the results of.
 *                      Must be non-empty list.
 * @param apiHost - API host running a Pact-enabled server.
 *
 * @alpha
 */
export declare function poll(requestBody: IPollRequestBody, apiHost: string, confirmationDepth?: number, requestInit?: ClientRequestInit): Promise<IPollResponse>;

/**
 * Asynchronous submission of one or more public (unencrypted) commands to the blockchain for execution.
 *
 * Corresponds to `fetchSendRaw` and `fetchSend` functions:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L601
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L589
 *
 * @param requestBody - Non-empty array of Pact commands to submit to server.
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function send(requestBody: ISendRequestBody, apiHost: string, requestInit?: ClientRequestInit): Promise<SendResponse>;

/**
 * Response type of /send endpoint.
 *
 * @param requestKeys - List of request keys (or command hashes) of the transactions submitted.
 *                      Can be sent to /poll and /listen to retrieve transaction results.
 *
 * @alpha
 */
export declare type SendResponse = IRequestKeys;

/**
 * Blocking request to fetch spv proof of a cross chain transaction.
 * Request must be sent to the chain where the transaction is initiated.
 *
 * @param requestBody -
 * @param apiHost - API host running a Pact-enabled server.
 * @alpha
 */
export declare function spv(requestBody: ISPVRequestBody, apiHost: string, requestInit?: ClientRequestInit): Promise<SPVResponse | Response>;

/**
 * Response type of /spv endpoint.
 *
 * Returns backend-specific data for continuing a cross-chain proof.
 *
 * @alpha
 */
export declare type SPVResponse = SPVProof;

/**
 * Formats API request body to use with `fetch` function.
 *
 * Corresponds to `mkReq` function:
 * https://github.com/kadena-io/pact-lang-api/blob/master/pact-lang-api.js#L533
 * @alpha
 */
export declare function stringifyAndMakePOSTRequest<T>(body: T, requestInit?: ClientRequestInit): {
    headers: {
        'Content-Type': string;
    } | {
        length: number;
        toString(): string;
        toLocaleString(): string;
        pop(): [string, string] | undefined;
        push(...items: [string, string][]): number;
        concat(...items: ConcatArray<[string, string]>[]): [string, string][];
        concat(...items: ([string, string] | ConcatArray<[string, string]>)[]): [string, string][];
        join(separator?: string | undefined): string;
        reverse(): [string, string][];
        shift(): [string, string] | undefined;
        slice(start?: number | undefined, end?: number | undefined): [string, string][];
        sort(compareFn?: ((a: [string, string], b: [string, string]) => number) | undefined): [string, string][];
        splice(start: number, deleteCount?: number | undefined): [string, string][];
        splice(start: number, deleteCount: number, ...items: [string, string][]): [string, string][];
        unshift(...items: [string, string][]): number;
        indexOf(searchElement: [string, string], fromIndex?: number | undefined): number;
        lastIndexOf(searchElement: [string, string], fromIndex?: number | undefined): number;
        every<S extends [string, string]>(predicate: (value: [string, string], index: number, array: [string, string][]) => value is S, thisArg?: any): this is S[];
        every(predicate: (value: [string, string], index: number, array: [string, string][]) => unknown, thisArg?: any): boolean;
        some(predicate: (value: [string, string], index: number, array: [string, string][]) => unknown, thisArg?: any): boolean;
        forEach(callbackfn: (value: [string, string], index: number, array: [string, string][]) => void, thisArg?: any): void;
        map<U>(callbackfn: (value: [string, string], index: number, array: [string, string][]) => U, thisArg?: any): U[];
        filter<S_1 extends [string, string]>(predicate: (value: [string, string], index: number, array: [string, string][]) => value is S_1, thisArg?: any): S_1[];
        filter(predicate: (value: [string, string], index: number, array: [string, string][]) => unknown, thisArg?: any): [string, string][];
        reduce(callbackfn: (previousValue: [string, string], currentValue: [string, string], currentIndex: number, array: [string, string][]) => [string, string]): [string, string];
        reduce(callbackfn: (previousValue: [string, string], currentValue: [string, string], currentIndex: number, array: [string, string][]) => [string, string], initialValue: [string, string]): [string, string];
        reduce<U_1>(callbackfn: (previousValue: U_1, currentValue: [string, string], currentIndex: number, array: [string, string][]) => U_1, initialValue: U_1): U_1;
        reduceRight(callbackfn: (previousValue: [string, string], currentValue: [string, string], currentIndex: number, array: [string, string][]) => [string, string]): [string, string];
        reduceRight(callbackfn: (previousValue: [string, string], currentValue: [string, string], currentIndex: number, array: [string, string][]) => [string, string], initialValue: [string, string]): [string, string];
        reduceRight<U_2>(callbackfn: (previousValue: U_2, currentValue: [string, string], currentIndex: number, array: [string, string][]) => U_2, initialValue: U_2): U_2;
        find<S_2 extends [string, string]>(predicate: (value: [string, string], index: number, obj: [string, string][]) => value is S_2, thisArg?: any): S_2 | undefined;
        find(predicate: (value: [string, string], index: number, obj: [string, string][]) => unknown, thisArg?: any): [string, string] | undefined;
        findIndex(predicate: (value: [string, string], index: number, obj: [string, string][]) => unknown, thisArg?: any): number;
        fill(value: [string, string], start?: number | undefined, end?: number | undefined): [string, string][];
        copyWithin(target: number, start: number, end?: number | undefined): [string, string][];
        entries(): IterableIterator<[number, [string, string]]>;
        keys(): IterableIterator<number>;
        values(): IterableIterator<[string, string]>;
        includes(searchElement: [string, string], fromIndex?: number | undefined): boolean;
        flatMap<U_3, This = undefined>(callback: (this: This, value: [string, string], index: number, array: [string, string][]) => U_3 | readonly U_3[], thisArg?: This | undefined): U_3[];
        flat<A, D extends number = 1>(this: A, depth?: D | undefined): FlatArray<A, D>[];
        [Symbol.iterator](): IterableIterator<[string, string]>;
        [Symbol.unscopables]: {
            [x: number]: boolean | undefined;
            length?: boolean | undefined;
            toString?: boolean | undefined;
            toLocaleString?: boolean | undefined;
            pop?: boolean | undefined;
            push?: boolean | undefined;
            concat?: boolean | undefined;
            join?: boolean | undefined;
            reverse?: boolean | undefined;
            shift?: boolean | undefined;
            slice?: boolean | undefined;
            sort?: boolean | undefined;
            splice?: boolean | undefined;
            unshift?: boolean | undefined;
            indexOf?: boolean | undefined;
            lastIndexOf?: boolean | undefined;
            every?: boolean | undefined;
            some?: boolean | undefined;
            forEach?: boolean | undefined;
            map?: boolean | undefined;
            filter?: boolean | undefined;
            reduce?: boolean | undefined;
            reduceRight?: boolean | undefined;
            find?: boolean | undefined;
            findIndex?: boolean | undefined;
            fill?: boolean | undefined;
            copyWithin?: boolean | undefined;
            entries?: boolean | undefined;
            keys?: boolean | undefined;
            values?: boolean | undefined;
            includes?: boolean | undefined;
            flatMap?: boolean | undefined;
            flat?: boolean | undefined;
            [Symbol.iterator]?: boolean | undefined;
            readonly [Symbol.unscopables]?: boolean | undefined;
            at?: boolean | undefined;
        };
        at(index: number): [string, string] | undefined;
        'Content-Type': string;
    } | {
        'Content-Type': string;
    } | {
        append(name: string, value: string): void;
        delete(name: string): void;
        get(name: string): string | null;
        getSetCookie(): string[];
        has(name: string): boolean;
        set(name: string, value: string): void;
        forEach(callbackfn: (value: string, key: string, parent: Headers) => void, thisArg?: any): void;
        'Content-Type': string;
    };
    method: string;
    body: string;
    cache?: RequestCache | undefined;
    credentials?: RequestCredentials | undefined;
    integrity?: string | undefined;
    keepalive?: boolean | undefined;
    mode?: RequestMode | undefined;
    priority?: RequestPriority | undefined;
    redirect?: RequestRedirect | undefined;
    referrer?: string | undefined;
    referrerPolicy?: ReferrerPolicy | undefined;
    signal?: AbortSignal | null | undefined;
    window?: null | undefined;
};

export { }
