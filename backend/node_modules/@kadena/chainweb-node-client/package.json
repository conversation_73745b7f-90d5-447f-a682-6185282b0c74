{"name": "@kadena/chainweb-node-client", "version": "0.9.1", "description": "Typed JavaScript wrapper with fetch to call chainweb-node API endpoints", "keywords": [], "repository": {"type": "git", "url": "https://github.com/kadena-community/kadena.js.git", "directory": "packages/libs/chainweb-node-client"}, "license": "BSD-3-<PERSON><PERSON>", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "main": "lib/index.js", "types": "dist/chainweb-node-client.d.ts", "files": ["dist", "lib"], "dependencies": {"cross-fetch": "~3.1.5", "@kadena/cryptography-utils": "0.4.4", "@kadena/pactjs": "0.4.3"}, "devDependencies": {"@microsoft/api-extractor": "^7.43.1", "@rushstack/eslint-config": "~3.6.9", "@types/node": "^20.12.7", "@vitest/coverage-v8": "^1.6.0", "eslint": "^8.45.0", "msw": "^2.2.14", "prettier": "~3.2.5", "typescript": "5.4.5", "vitest": "^1.6.0", "@kadena-dev/lint-package": "0.2.0", "@kadena-dev/shared-config": "1.0.1", "@kadena/types": "0.7.0", "@kadena-dev/eslint-config": "1.1.0", "@kadena-dev/markdown": "1.0.3"}, "publishConfig": {"provenance": true}, "scripts": {"_postinstall": "pnpm run generate:openapi-types", "build": "tsc && api-extractor run --verbose", "format": "pnpm run --sequential /^format:.*/", "format:lint": "pnpm run lint:src --fix", "format:md": "remark README.md -o --use @kadena-dev/markdown", "format:src": "prettier . --cache --write", "generate:openapi-types": "echo 'openapi specs needs fixes' # openapi-typescript \"./openapi/*.json\" --output ./src/openapi", "lint": "pnpm run /^lint:.*/", "lint:fmt": "prettier . --cache --check", "lint:pkg": "lint-package", "lint:src": "eslint src --ext .js,.ts", "test": "vitest run", "test:watch": "vitest"}}