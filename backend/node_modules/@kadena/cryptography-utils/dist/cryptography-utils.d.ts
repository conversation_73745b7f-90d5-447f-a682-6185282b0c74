import type { IBase64Url } from '@kadena/types';
import type { <PERSON><PERSON>eyPair } from '@kadena/types';
import type { IUnsignedCommand } from '@kadena/types';
import type { SignCommand } from '@kadena/types';

/**
 * Takes in Base64 Url encoded string and outputs decoded string
 * code from [https://gist.github.com/1020396] by [https://github.com/atk]
 *
 * @alpha
 */
export declare function base64UrlDecode(str: IBase64Url): string;

/**
 * Takes in a hex string and outputs a Uint8Array binary object.
 *
 * @alpha
 */
export declare function base64UrlDecodeArr(input: IBase64Url): Uint8Array;

/**
 * Takes in string and outputs Base64 Url encoded string
 * code from [https://gist.github.com/999166] by [https://github.com/nignag]
 *
 * @alpha
 */
export declare function base64UrlEncode(str: string): IBase64Url;

/**
 * Takes in Uint8Array binary object and outputs hex string.
 *
 * @alpha
 */
export declare function base64UrlEncodeArr(input: Uint8Array): IBase64Url;

/**
 * Takes in Uint8Array binary object and outputs hex string.
 *
 * @alpha
 */
export declare function binToHex(array: Uint8Array): string;

/**
 * Generate a random ED25519 keypair.
 *
 * @alpha
 */
export declare function genKeyPair(): IKeyPair;

/**
 * Takes in string, outputs blake2b256 hash encoded as unescaped base64url.
 *
 * @alpha
 */
export declare function hash(str: string): string;

/**
 * Takes in string and outputs blake2b256 hash binary as a Uint8Array.
 *
 * @alpha
 */
export declare function hashBin(str: string): Uint8Array;

/**
 * Takes in hex string and outputs Uint8Array binary object.
 *
 * @alpha
 */
export declare function hexToBin(hexString: string): Uint8Array;

/**
 * @alpha
 */
export declare const pactTestCommand: {
    networkId: undefined | unknown;
    payload: {
        exec: {
            data: {
                'accounts-admin-keyset': string[];
            };
            code: string;
        };
    };
    signers: {
        pubKey: string;
    }[];
    meta: {
        creationTime: number;
        ttl: number;
        gasLimit: number;
        chainId: string;
        gasPrice: number;
        sender: string;
    };
    nonce: string;
};

/**
 * @alpha
 */
export declare const pactTestCommand1: IUnsignedCommand;

/**
 * @alpha
 */
export declare const pactTestCommand2: IUnsignedCommand;

/**
 * Generate a deterministic ED25519 keypair from a given Kadena secretKey
 *
 * @alpha
 */
export declare function restoreKeyPairFromSecretKey(seed: string): IKeyPair;

/**
 Perform blake2b256 hashing on a message, and sign using keyPair.

 * @alpha
 */
export declare function sign(msg: string, { secretKey, publicKey }: IKeyPair): SignCommand;

/**
 Sign a hash using key pair

 * @alpha
 */
export declare function signHash(hash: string, { secretKey, publicKey }: IKeyPair): SignCommand;

/**
 * Takes in string and outputs Uint8Array
 *
 * @alpha
 */
export declare function strToUint8Array(str: string): Uint8Array;

/**
 * Converts a keypair into Uint8Array binary object, public key attached to secret key
 * @alpha
 */
export declare function toTweetNaclSecretKey({ secretKey, publicKey, }: IKeyPair): Uint8Array;

/**
 * Convert Uint8Array to string
 *
 * @alpha
 */
export declare function uint8ArrayToStr(array: Uint8Array): string;

/**
 * Takes in Array with IBase64Url values and outputs an Array with unique IBase64Url values
 * @example
 * Here's some example code to use unique:
 *
 * ```ts
 *   const cmdHashes = [
 *     'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',
 *     'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
 *     'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
 *   ];
 *
 *   const uniqueHashesArray = unique(cmdHashes);
 *
 *   // output [
 *   //  'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',
 *   //  'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
 *   // ];
 *
 * ```
 * @alpha
 */
export declare function unique(array: Array<string>): Array<string>;

/**
 * Verifies the signature for the message and returns true if verification succeeded or false if it failed.
 *
 * @alpha
 */
export declare function verifySig(msg: Uint8Array, sig: Uint8Array, pubKey: Uint8Array): boolean;

export { }
