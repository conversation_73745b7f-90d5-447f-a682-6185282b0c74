"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const unique_1 = require("../unique");
(0, vitest_1.test)('Takes in an array of hashes and remove duplicate', () => {
    const cmdHashes = [
        'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',
        'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
        'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
    ];
    const actual = (0, unique_1.unique)(cmdHashes);
    const expected = [
        'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',
        'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
    ];
    (0, vitest_1.expect)(expected).toEqual(actual);
});
(0, vitest_1.test)('Takes in an array of unique hashes and return unique hashes', () => {
    const cmdHashes = [
        'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',
        'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
    ];
    const actual = (0, unique_1.unique)(cmdHashes);
    const expected = [
        'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',
        'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',
    ];
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=unique.test.js.map