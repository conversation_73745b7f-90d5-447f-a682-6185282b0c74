{"version": 3, "file": "verifySig.test.js", "sourceRoot": "", "sources": ["../../src/tests/verifySig.test.ts"], "names": [], "mappings": ";;AAAA,mCAAgD;AAChD,8DAA2D;AAC3D,0CAAuC;AACvC,4CAAyC;AAEzC,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,aAAI,EAAC,oFAAoF,EAAE,GAAG,EAAE;QAC9F,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,6CAA6C;YACnD,GAAG,EAAE,kIAAkI;YACvI,MAAM,EACJ,kEAAkE;SACrE,CAAC;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEtC,MAAM,UAAU,GAAG,IAAA,qBAAS,EAC1B,IAAA,uCAAkB,EAAC,IAAI,CAAC,EACxB,IAAA,mBAAQ,EAAC,GAAG,CAAC,EACb,IAAA,mBAAQ,EAAC,MAAM,CAAC,CACjB,CAAC;QAEF,IAAA,eAAM,EAAC,UAAU,CAAC,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,IAAA,aAAI,EAAC,uFAAuF,EAAE,GAAG,EAAE;QACjG,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,6CAA6C;YACnD,GAAG,EAAE,kIAAkI;YACvI,MAAM,EACJ,kEAAkE;SACrE,CAAC;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEtC,MAAM,UAAU,GAAG,IAAA,qBAAS,EAC1B,IAAA,uCAAkB,EAAC,IAAI,CAAC,EACxB,IAAA,mBAAQ,EAAC,GAAG,CAAC,EACb,IAAA,mBAAQ,EAAC,MAAM,CAAC,CACjB,CAAC;QAEF,IAAA,eAAM,EAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, test } from 'vitest';\nimport { base64UrlDecodeArr } from '../base64UrlDecodeArr';\nimport { hexToBin } from '../hexToBin';\nimport { verifySig } from '../verifySig';\n\ndescribe('verifySig', () => {\n  test('Takes in message, public key and signature in binary object, returns true if valid', () => {\n    const signCmd = {\n      hash: 'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n      sig: 'b7abf9fef533bbdb306a406db75f93fc6367ae1dc3ecbd18d118f5a6000610d064abab646256c856f8e6f0290b675808d9e4f9090fa035e7fb8f8962cc4d8202',\n      pubKey:\n        '1442f89e7311e68568abe039f1edd162f0da021fdae2c5fcf821a9b7d25d3769',\n    };\n    const { hash, sig, pubKey } = signCmd;\n\n    const isValidSig = verifySig(\n      base64UrlDecodeArr(hash),\n      hexToBin(sig),\n      hexToBin(pubKey),\n    );\n\n    expect(isValidSig).toBeTruthy();\n  });\n\n  test('takes in message, public key and signature in binary object, returns false if invalid', () => {\n    const signCmd = {\n      hash: 'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n      sig: '26d765e3b812d59d80ffbd034d4fc4a1a24f8d0c3929586575617089e5098d967955d348608b515ae9ff7871b46726ffc71252d53b9e562d5bcf3bfe66292906',\n      pubKey:\n        'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n    };\n    const { hash, sig, pubKey } = signCmd;\n\n    const isValidSig = verifySig(\n      base64UrlDecodeArr(hash),\n      hexToBin(sig),\n      hexToBin(pubKey),\n    );\n\n    expect(isValidSig).toBeFalsy();\n  });\n});\n"]}