{"version": 3, "file": "unique.test.js", "sourceRoot": "", "sources": ["../../src/tests/unique.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,sCAAmC;AAEnC,IAAA,aAAI,EAAC,kDAAkD,EAAE,GAAG,EAAE;IAC5D,MAAM,SAAS,GAAG;QAChB,6CAA6C;QAC7C,6CAA6C;QAC7C,6CAA6C;KAC9C,CAAC;IAEF,MAAM,MAAM,GAAG,IAAA,eAAM,EAAC,SAAS,CAAC,CAAC;IACjC,MAAM,QAAQ,GAAG;QACf,6CAA6C;QAC7C,6CAA6C;KAC9C,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,IAAA,aAAI,EAAC,6DAA6D,EAAE,GAAG,EAAE;IACvE,MAAM,SAAS,GAAG;QAChB,6CAA6C;QAC7C,6CAA6C;KAC9C,CAAC;IAEF,MAAM,MAAM,GAAG,IAAA,eAAM,EAAC,SAAS,CAAC,CAAC;IACjC,MAAM,QAAQ,GAAG;QACf,6CAA6C;QAC7C,6CAA6C;KAC9C,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { unique } from '../unique';\n\ntest('Takes in an array of hashes and remove duplicate', () => {\n  const cmdHashes = [\n    'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',\n    'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n    'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  ];\n\n  const actual = unique(cmdHashes);\n  const expected = [\n    'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',\n    'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  ];\n\n  expect(expected).toEqual(actual);\n});\n\ntest('Takes in an array of unique hashes and return unique hashes', () => {\n  const cmdHashes = [\n    'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',\n    'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  ];\n\n  const actual = unique(cmdHashes);\n  const expected = [\n    'NjduEShgzrjEmAVhprS85hst7mvCqOo6qjGH5j5WHro',\n    'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  ];\n\n  expect(expected).toEqual(actual);\n});\n"]}