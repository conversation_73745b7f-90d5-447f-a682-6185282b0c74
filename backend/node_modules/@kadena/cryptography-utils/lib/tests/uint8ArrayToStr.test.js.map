{"version": 3, "file": "uint8ArrayToStr.test.js", "sourceRoot": "", "sources": ["../../src/tests/uint8ArrayToStr.test.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AACtC,wDAAqD;AAErD,IAAA,aAAI,EAAC,wCAAwC,EAAE,GAAG,EAAE;IAClD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC;QACzB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;QAC3E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;KACtE,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,IAAA,iCAAe,EAAC,GAAG,CAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,IAAA,iCAAe,EAC9B,IAAI,UAAU,CAAC;QACb,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;QACtE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QACrE,GAAG;KACJ,CAAC,CACH,CAAC;IAEF,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC", "sourcesContent": ["import { expect, test } from 'vitest';\nimport { uint8ArrayToStr } from '../uint8ArrayToStr';\n\ntest('should convert Uint8 array to a string', () => {\n  const arr = new Uint8Array([\n    205, 170, 167, 69, 13, 17, 99, 60, 83, 113, 200, 237, 98, 128, 111, 66, 192,\n    232, 228, 175, 102, 198, 190, 19, 16, 95, 135, 33, 132, 226, 228, 154,\n  ]);\n\n  const actual = uint8ArrayToStr(arr);\n  const expected = uint8ArrayToStr(\n    new Uint8Array([\n      205, 170, 167, 69, 13, 17, 99, 60, 83, 113, 200, 237, 98, 128, 111, 66,\n      192, 232, 228, 175, 102, 198, 190, 19, 16, 95, 135, 33, 132, 226, 228,\n      154,\n    ]),\n  );\n\n  expect(expected).toEqual(actual);\n});\n"]}