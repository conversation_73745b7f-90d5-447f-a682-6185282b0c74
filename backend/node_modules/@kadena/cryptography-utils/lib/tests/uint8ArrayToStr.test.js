"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const uint8ArrayToStr_1 = require("../uint8ArrayToStr");
(0, vitest_1.test)('should convert Uint8 array to a string', () => {
    const arr = new Uint8Array([
        205, 170, 167, 69, 13, 17, 99, 60, 83, 113, 200, 237, 98, 128, 111, 66, 192,
        232, 228, 175, 102, 198, 190, 19, 16, 95, 135, 33, 132, 226, 228, 154,
    ]);
    const actual = (0, uint8ArrayToStr_1.uint8ArrayToStr)(arr);
    const expected = (0, uint8ArrayToStr_1.uint8ArrayToStr)(new Uint8Array([
        205, 170, 167, 69, 13, 17, 99, 60, 83, 113, 200, 237, 98, 128, 111, 66,
        192, 232, 228, 175, 102, 198, 190, 19, 16, 95, 135, 33, 132, 226, 228,
        154,
    ]));
    (0, vitest_1.expect)(expected).toEqual(actual);
});
//# sourceMappingURL=uint8ArrayToStr.test.js.map