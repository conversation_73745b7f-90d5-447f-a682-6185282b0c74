{"version": 3, "file": "Pact.js", "sourceRoot": "", "sources": ["../../../src/tests/mockdata/Pact.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACU,QAAA,eAAe,GAexB;IACF,SAAS,EAAE,IAAI;IACf,OAAO,EAAE;QACP,IAAI,EAAE;YACJ,IAAI,EAAE;gBACJ,uBAAuB,EAAE;oBACvB,kEAAkE;iBACnE;aACF;YACD,IAAI,EAAE,oKAAoK;SAC3K;KACF;IACD,OAAO,EAAE;QACP;YACE,MAAM,EACJ,kEAAkE;SACrE;KACF;IACD,IAAI,EAAE;QACJ,YAAY,EAAE,CAAC;QACf,GAAG,EAAE,CAAC;QACN,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,GAAG;QACZ,QAAQ,EAAE,CAAC;QACX,MAAM,EAAE,EAAE;KACX;IACD,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;CAChC,CAAC;AAEF;;GAEG;AACU,QAAA,gBAAgB,GAAqB;IAChD,IAAI,EAAE,6CAA6C;IACnD,IAAI,EAAE;QACJ;YACE,GAAG,EAAE,kIAAkI;SACxI;KACF;IACD,GAAG,EAAE,6gBAA6gB;CACnhB,CAAC;AAEF;;GAEG;AACU,QAAA,gBAAgB,GAAqB;IAChD,IAAI,EAAE,6CAA6C;IACnD,IAAI,EAAE,CAAC,SAAS,CAAC;IACjB,GAAG,EAAE,6gBAA6gB;CACnhB,CAAC", "sourcesContent": ["import type { IUnsignedCommand } from '@kadena/types';\n\n/**\n * @alpha\n */\nexport const pactTestCommand: {\n  networkId: undefined | unknown;\n  payload: {\n    exec: { data: { 'accounts-admin-keyset': string[] }; code: string };\n  };\n  signers: { pubKey: string }[];\n  meta: {\n    creationTime: number;\n    ttl: number;\n    gasLimit: number;\n    chainId: string;\n    gasPrice: number;\n    sender: string;\n  };\n  nonce: string;\n} = {\n  networkId: null,\n  payload: {\n    exec: {\n      data: {\n        'accounts-admin-keyset': [\n          'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n        ],\n      },\n      code: '(define-keyset \\'k (read-keyset \"accounts-admin-keyset\"))\\n(module system \\'k\\n  (defun get-system-time ()\\n    (time \"2017-10-31T12:00:00Z\")))\\n(get-system-time)',\n    },\n  },\n  signers: [\n    {\n      pubKey:\n        'ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d',\n    },\n  ],\n  meta: {\n    creationTime: 0,\n    ttl: 0,\n    gasLimit: 0,\n    chainId: '0',\n    gasPrice: 0,\n    sender: '',\n  },\n  nonce: JSON.stringify('step01'),\n};\n\n/**\n * @alpha\n */\nexport const pactTestCommand1: IUnsignedCommand = {\n  hash: 'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  sigs: [\n    {\n      sig: '26d765e3b812d59d80ffbd034d4fc4a1a24f8d0c3929586575617089e5098d967955d348608b515ae9ff7871b46726ffc71252d53b9e562d5bcf3bfe66292906',\n    },\n  ],\n  cmd: '{\"networkId\":null,\"payload\":{\"exec\":{\"data\":{\"accounts-admin-keyset\":[\"ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d\"]},\"code\":\"(define-keyset \\'k (read-keyset \\\\\"accounts-admin-keyset\\\\\"))\\\\n(module system \\'k\\\\n  (defun get-system-time ()\\\\n    (time \\\\\"2017-10-31T12:00:00Z\\\\\")))\\\\n(get-system-time)\"}},\"signers\":[{\"pubKey\":\"ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d\"}],\"meta\":{\"creationTime\":0,\"ttl\":0,\"gasLimit\":0,\"chainId\":\"\",\"gasPrice\":0,\"sender\":\"\"},\"nonce\":\"\\\\\"step01\\\\\"\"}',\n};\n\n/**\n * @alpha\n */\nexport const pactTestCommand2: IUnsignedCommand = {\n  hash: 'pMohh9G2NT1jQn4byK1iwvoLopbnU86NeNPSUq8I0ik',\n  sigs: [undefined],\n  cmd: '{\"networkId\":null,\"payload\":{\"exec\":{\"data\":{\"accounts-admin-keyset\":[\"ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d\"]},\"code\":\"(define-keyset \\'k (read-keyset \\\\\"accounts-admin-keyset\\\\\"))\\\\n(module system \\'k\\\\n  (defun get-system-time ()\\\\n    (time \\\\\"2017-10-31T12:00:00Z\\\\\")))\\\\n(get-system-time)\"}},\"signers\":[{\"pubKey\":\"ba54b224d1924dd98403f5c751abdd10de6cd81b0121800bf7bdbdcfaec7388d\"}],\"meta\":{\"creationTime\":0,\"ttl\":0,\"gasLimit\":0,\"chainId\":\"\",\"gasPrice\":0,\"sender\":\"\"},\"nonce\":\"\\\\\"step01\\\\\"\"}',\n};\n"]}