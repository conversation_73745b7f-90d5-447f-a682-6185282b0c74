{"name": "@kadena/client", "version": "1.17.1", "description": "Core library for building Pact expressions to send to the blockchain in js. Makes use of .kadena/pactjs-generated", "repository": {"type": "git", "url": "https://github.com/kadena-community/kadena.js.git", "directory": "packages/libs/client"}, "license": "BSD-3-<PERSON><PERSON>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "exports": {".": "./lib/index.js", "./fp": "./lib/fp.js"}, "main": "lib/index.js", "types": "dist/client.d.ts", "files": ["dist", "lib", "fp"], "dependencies": {"@walletconnect/sign-client": "~2.8.1", "cross-fetch": "~3.1.5", "debug": "4.3.4", "@kadena/chainweb-node-client": "0.9.1", "@kadena/pactjs": "0.4.3", "@kadena/cryptography-utils": "0.4.4"}, "devDependencies": {"@microsoft/api-extractor": "^7.43.1", "@rushstack/eslint-config": "~3.6.9", "@types/debug": "~4.1.12", "@types/node": "^20.12.7", "@vitest/coverage-v8": "^1.6.0", "@walletconnect/types": "~2.8.1", "eslint": "^8.45.0", "jsdom": "^22.1.0", "msw": "^2.2.14", "prettier": "~3.2.5", "ts-node": "~10.9.2", "typescript": "5.4.5", "vitest": "^1.6.0", "@kadena-dev/eslint-config": "1.1.0", "@kadena-dev/lint-package": "0.2.0", "@kadena-dev/shared-config": "1.0.1", "@kadena/types": "0.7.0", "@kadena-dev/markdown": "1.0.3"}, "publishConfig": {"provenance": true}, "scripts": {"build": "tsc && api-extractor run --verbose", "format": "pnpm run --sequential /^format:.*/", "format:lint": "pnpm run lint:src --fix", "format:md": "remark README.md -o --use @kadena-dev/markdown", "format:src": "prettier . --cache --write", "lint": "pnpm run /^lint:.*/", "lint:fmt": "prettier . --cache --check", "lint:pkg": "lint-package", "lint:src": "eslint src --ext .js,.ts", "start": "ts-node --transpile-only src/index.ts", "test": "vitest run", "test:integration": "vitest run -c ./vitest.integration.config.ts", "test:watch": "vitest"}}