import { ChainId } from '@kadena/types';
import type Client from '@walletconnect/sign-client';
import { ClientRequestInit } from '@kadena/chainweb-node-client';
import { ICap } from '@kadena/types';
import { ICommand } from '@kadena/types';
import { ICommandResult } from '@kadena/chainweb-node-client';
import type { IExecPayload } from '@kadena/types';
import { IKeyPair } from '@kadena/types';
import type { ILocalCommandResult } from '@kadena/chainweb-node-client';
import type { ILocalOptions } from '@kadena/chainweb-node-client';
import { IPollResponse } from '@kadena/chainweb-node-client';
import { IPreflightResult } from '@kadena/chainweb-node-client';
import type { ISigningCap } from '@kadena/types';
import { IUnsignedCommand } from '@kadena/types';
import type { LocalRequestBody } from '@kadena/chainweb-node-client';
import type { LocalResponse } from '@kadena/chainweb-node-client';
import type { PactValue } from '@kadena/types';
import type { SessionTypes } from '@walletconnect/types';

declare type AddCapabilities<T> = {
    [K in keyof T]: T[K] extends {
        capability: any;
    } ? T[K] : ExtractPactModule<T[K]>;
};

/**
 * adds signatures to an {@link @kadena/types#IUnsignedCommand | unsigned command}
 *
 * @public
 */
export declare const addSignatures: (transaction: IUnsignedCommand, ...signatures: {
    sig: string;
    pubKey?: string;
}[]) => IUnsignedCommand | ICommand;

declare type AllPartial<T> = {
    [P in keyof T]?: T[P] extends {} ? AllPartial<T[P]> : T[P];
} & {};

/**
 * @public
 */
export declare type BuiltInPredicate = 'keys-all' | 'keys-any' | 'keys-2';

export { ChainId }

export { ClientRequestInit }

/**
 * Creates Chainweb client
 * @public
 */
export declare const createClient: ICreateClient;

/**
 * Creates the quicksignWithEckoWallet function with interface {@link ISingleSignFunction}
 *
 * @deprecated Use {@link createQuicksignWithEckoWallet} instead
 * @public
 */
export declare const createEckoWalletQuicksign: typeof createQuicksignWithEckoWallet;

/**
 * Creates the signWithEckoWallet function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithEckoWallet} function
 *
 * @deprecated Use {@link createSignWithEckoWallet} instead
 * @public
 */
export declare const createEckoWalletSign: typeof createSignWithEckoWallet;

/**
 * Creates the quicksignWithEckoWallet function with interface {@link ISingleSignFunction}
 *
 * @public
 */
export declare function createQuicksignWithEckoWallet(): IEckoSignFunction;

/**
 * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @public
 */
export declare function createQuicksignWithWalletConnect(client: Client, session: SessionTypes.Struct, walletConnectChainId: TWalletConnectChainId): ISignFunction;

/**
 * Creates the signWithChainweaver function with interface {@link ISignFunction}
 * Lets you sign with Chainweaver according to {@link https://github.com/kadena-io/KIPs/blob/master/kip-0015.md | sign-v1 API}
 *
 * @param options - object to customize behaviour.
 *   - `host: string` - the host of the chainweaver instance to use. Defaults to `http://127.0.0.1:9467`
 * @returns - {@link ISignFunction}
 * @public
 */
export declare function createSignWithChainweaver(options?: {
    host: string;
}): ISignFunction;

/**
 * Creates the signWithEckoWallet function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createEckoWalletQuicksign} function
 *
 * @public
 */
export declare function createSignWithEckoWallet(): IEckoSignSingleFunction;

/**
 * function to create a `signWithKeypair` function
 * This allows you to sign subsequent transactions with the same keypair(s)
 *
 * @param keyOrKeys - provide the key or multiple keys to sign with
 * @returns a function to sign with
 *
 * @example
 * ```ts
 * const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);
 * const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);
 * const signedTx3 = await signWithKeystore(tx3);
 * ```
 *
 * @public
 */
export declare const createSignWithKeypair: ICreateSignWithKeypair;

/**
 * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithWalletConnect} function
 *
 * @public
 */
export declare function createSignWithWalletConnect(client: Client, session: SessionTypes.Struct, walletConnectChainId: TWalletConnectChainId): ISingleSignFunction;

/**
 * Prepare a transaction object. Creates an object with hash, cmd and sigs ({@link @kadena/types#IUnsignedCommand})
 * @public
 */
export declare const createTransaction: (pactCommand: IPartialPactCommand) => IUnsignedCommand;

/**
 * returns a new instance of command builder
 * @param initial - the initial command
 *
 * @public
 */
export declare const createTransactionBuilder: (initial?: IPartialPactCommand) => ITransactionBuilder;

/**
 * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 *
 * @deprecated Use {@link createQuicksignWithWalletConnect} instead
 * @public
 */
export declare const createWalletConnectQuicksign: typeof createQuicksignWithWalletConnect;

/**
 * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithWalletConnect} function
 *
 * @deprecated Use {@link createSignWithWalletConnect} instead
 * @public
 */
export declare const createWalletConnectSign: typeof createSignWithWalletConnect;

/**
 * The response status of the Ecko Wallet request
 * @public
 */
export declare type EckoStatus = 'success' | 'fail';

/**
 * @internal
 */
declare type ExtractCapabilityType<TCommand> = TCommand extends {
    payload: infer TPayload;
} ? TPayload extends {
    funs: infer TFunctions;
} ? TFunctions extends Array<infer TFunction> ? UnionToIntersection<TFunction> extends {
    capability: infer TCapability;
} ? TCapability : IGeneralCapability : IGeneralCapability : IGeneralCapability : IGeneralCapability;

declare type ExtractPactModule<T> = RootModule<T> | ModuleWithNamespace<T> extends never ? string : RootModule<T> | ModuleWithNamespace<T>;

declare type FnRest = '' | ` ${string}`;

declare type GetFuncReturnType<M, F> = M extends keyof IPactModules ? F extends keyof IPactModules[M] ? IPactModules[M][F] extends (...args: any[]) => infer R ? R : never : never : never;

/**
 *
 * @public
 * Creates endpoint url based on the baseUrl, networkId and chainId
 *
 * @example
 * const getLocalHostUrl = getHostUrl('http://localhost:8080')
 * const client = createClient(getLocalHostUrl)
 */
export declare const getHostUrl: (hostBaseUrl: string) => ({ networkId, chainId }: INetworkOptions) => string;

/**
 * Parses an error message to extract the Pact error code.
 *
 * This function is compatible with both Pact 4 and Pact 5 error formats.
 *
 * @param  error - The error returned by Pact.
 * @returns {@link PactErrorCode} - The extracted Pact error ('ERROR' if the error code could not be extracted).
     *
     * @example
     * ```ts
     * const client = createClient();
     * const response = await client.local(tx);
     * if (response.result.status === 'failure') {
     *   if (getPactErrorCode(response.result.error) === 'RECORD_NOT_FOUND') {
     *     // handle record not found error
     *   }
     * }
     * ```
     * @public
     */
 export declare function getPactErrorCode(error: {
     message: string | undefined;
 } | undefined): PactErrorCode;

 declare interface IAddKeyset<TCommand> {
     <TKey extends string, PRED extends BuiltInPredicate>(key: TKey, pred: PRED, ...publicKeys: string[]): IBuilder<TCommand>;
     <TKey extends string, PRED extends string>(key: TKey, pred: PRED, ...publicKeys: string[]): IBuilder<TCommand>;
 }

 declare interface IAddSigner<TCommand> {
     /**
      * Add signer without capability
      */
     (first: ISigner | ISigner[]): IBuilder<TCommand>;
     /**
      * Add a signer including capabilities. The withCapability function is obtained from
      * the function you call in the execution part.
      * @example
      * Pact.builder.execute(
      *   Pact.coin.transfer("alice", "bob", \{ decimal:"1" \})
      * ).addSigner("public_key", (withCapability) =\> [
      *   withCapability("coin.GAS"),
      *   withCapability("coin.TRANSFER", "alice", "bob", \{ decimal:"1" \})
      * ])
      */
     (first: ISigner | ISigner[], capability: (withCapability: ExtractCapabilityType<TCommand>) => ICap[]): IBuilder<TCommand>;
 }

 declare interface IAddVerifier<TCommand> {
     /**
      * Add verifier without capability
      */
     (verifier: IVerifier): IBuilder<TCommand>;
     /**
      * Add a signer including capabilities. The withCapability function is obtained from
      * the function you call in the execution part.
      * @example
      * Pact.builder.execute(
      *   Pact.coin.transfer("alice", "bob", \{ decimal:"1" \})
      * ).addVerifier(\{ name:"name", proof:"proof" \}, (withCapability) =\> [
      *   withCapability("coin.GAS"),
      *   withCapability("coin.TRANSFER", "alice", "bob", \{ decimal:"1" \})
      * ])
      */
     (verifier: IVerifier, capability: (withCapability: ExtractCapabilityType<TCommand>) => ICap[]): IBuilder<TCommand>;
 }

 /**
  * @public
  */
 export declare interface IBaseClient {
     /**
      * Sends a command for non-transactional execution.
      * In a blockchain environment, this would be a node-local "dirty read".
      * Any database writes or changes to the environment are rolled back.
      * Gas payment is not required for this function.
      *
      * Calls the '/local' endpoint with optional options.
      *
      * @param transaction - The transaction to be executed.
      * @param options - Optional settings for preflight and signatureVerification.
      * @returns A promise that resolves to the local response.
      */
     local: <T extends ILocalOptions>(transaction: LocalRequestBody, options?: T) => Promise<LocalResponse<T>>;
     /**
      * Submits one or more public (unencrypted) signed commands to the blockchain for execution.
      *
      * Calls the '/send' endpoint.
      * This is the only function that requires gas payment.
      *
      * @param transactionList - The list of transactions to be submitted.
      * @returns A promise that resolves the transactionDescriptor {@link ITransactionDescriptor}
      */
     submit: ISubmit;
     /**
      * Polls the result of one or more submitted requests.
      * Calls the '/poll' endpoint multiple times to get the status of all requests.
      *
      * @param transactionDescriptors - transaction descriptors to status polling.
      * @param options - options to adjust polling (onPoll, timeout, interval, and confirmationDepth).
      * @returns A promise that resolves to the poll request promise with the command result.
      */
     pollStatus: (transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor, options?: IPollOptions) => IPollRequestPromise<ICommandResult>;
     /**
      * Gets the result of one or more submitted requests.
      * If the result is not ready, it returns an empty object.
      * Calls the '/poll' endpoint only once.
      *
      * @param transactionDescriptors - transaction descriptors to get the status.
      * @returns  A promise that resolves to the poll response with the command result.
      */
     getStatus: (transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor, options?: ClientRequestInit) => Promise<IPollResponse>;
     /**
      * Listens for the result of the request. This is a long-polling process that eventually returns the result.
      * Calls the '/listen' endpoint.
      *
      *
      * @param transactionDescriptors - transaction descriptors to listen for.
      * @returns A promise that resolves to the command result.
      */
     listen: (transactionDescriptor: ITransactionDescriptor, options?: ClientRequestInit) => Promise<ICommandResult>;
     /**
      * Creates an SPV proof for a request. This is required for multi-step tasks.
      * Calls the '/spv' endpoint several times to retrieve the SPV proof.
      *
      *
      * @param transactionDescriptor - The request key for which the SPV proof is generated.
      * @param targetChainId - The target chain ID for the SPV proof.
      * @param options - options to adjust polling (onPoll, timeout, and interval).
      * @returns A promise that resolves to the generated SPV proof.
      */
     pollCreateSpv: (transactionDescriptor: ITransactionDescriptor, targetChainId: ChainId, options?: IPollOptions) => Promise<string>;
     /**
      * Creates an SPV proof for a request. This is required for multi-step tasks.
      * Calls the '/spv' endpoint only once.
      *
      *
      * @param transactionDescriptor - The transaction descriptor for which the SPV proof is generated.
      * @param targetChainId - The target chain ID for the SPV proof.
      * @returns A promise that resolves to the generated SPV proof.
      */
     createSpv: (transactionDescriptor: ITransactionDescriptor, targetChainId: ChainId, options?: ClientRequestInit) => Promise<string>;
 }

 /**
  * The interface of the return value `Pact.builder.execution` or `Pact.builder.continuation`
  *
  * @see {@link IPact}
  * @public
  */
 export declare interface IBuilder<TCommand> {
     /**
      * Add verifier with theirs capabilities
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   // add verifier without scoping to any capabilities
      *   .addVerifier(\{ name:"bridge", proof:"proof" \})
      *   // add verifier without scoping to the capabilities
      *   .addVerifier(\{ name:"zk", proof:"proof" \},()=>[
      *       withCapability("coin.GAS"),
      *       withCapability("myModule.CAP","arg1",{ decimal: 2 })
      *    ])
      * ```
      */
     addVerifier: IAddVerifier<TCommand>;
     /**
      * Add signer with theirs capabilities
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   // add signer without scoping to any capabilities
      *   .addSigner("public_key")
      *   // add signer without scoping to the capabilities
      *   .addSigner("gas_payer_public_key",()=>[
      *       withCapability("coin.GAS"),
      *       withCapability("myModule.CAP","arg1",{ decimal: 2 })
      *    ])
      * ```
      */
     addSigner: IAddSigner<TCommand>;
     /**
      * Set meta data
      *
      * @param meta - includes sender parameter which is the account address of the gas payer
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   // select the chain and gas_payer_account
      *   .setMeta({
      *     chainId: '0',
      *     sender: 'gas_payer_account',
      *   })
      * ```
      */
     setMeta: (meta: Partial<Omit<IPactCommand['meta'], 'sender'>> & {
         senderAccount?: string;
     }) => IBuilder<TCommand>;
     /**
      * Set nonce
      *
      * if its not presented the commandBuilder uses the default nonce generator. `kjs:nonce:timestamp`
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   .setNonce('my-custom-nonce')
      * ```
      */
     setNonce: ISetNonce<TCommand>;
     /**
      * Set network id
      *
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   .setNetworkId('mainnet01')
      * ```
      */
     setNetworkId: (id: string) => IBuilder<TCommand>;
     /**
      * add data
      *
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   .addData('theKey','theValue')
      * ```
      */
     addData: (key: string, data: ValidDataTypes) => IBuilder<TCommand>;
     /**
      * add keyset to the data part
      *
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   .addKeyset('keysetName', 'keys-all', 'fist-public-key', 'second-public-key')
      * ```
      */
     addKeyset: IAddKeyset<TCommand>;
     /**
      * finalizing the command and create the transaction object in `IUnsignedCommand` format
      *
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("albert"))
      *   // select the chain and gas_payer_account
      *   .setMeta({
      *     chainId: '0',
      *     sender: 'gas_payer_account',
      *   })
      *   // the sigs array has the same length as the signers array in the command but it fills all
      *   // by undefined which then will be replaced by the final signatures by using the wallet helpers
      *   .createTransaction(); // {cmd:"stringified command", hash:"string-hash", sigs:[undefined]}
      * ```
      */
     createTransaction: () => IUnsignedCommand;
     /**
      * finalizing the command by adding all default values.
      *
      */
     getCommand: () => Partial<IPactCommand>;
 }

 export { ICap }

 /**
  * @beta
  * @deprecated Use {@link @kadena/types#ICap} instead
  */
 export declare type ICapabilityItem = ICap;

 /**
  * Interface for the {@link createClient | createClient()} return value
  * @public
  */
 export declare interface IClient extends IBaseClient {
     /**
      * An alias for `local` when both preflight and signatureVerification are `true`.
      * @see local
      */
     preflight: (transaction: ICommand | IUnsignedCommand, options?: ClientRequestInit) => Promise<ILocalCommandResult>;
     /**
      * An alias for `local` when preflight is `false` and signatureVerification is `true`.
      *
      * @remarks
      * @see {@link IBaseClient.local | local() function}
      */
     signatureVerification: (transaction: ICommand, options?: ClientRequestInit) => Promise<ICommandResult>;
     /**
      * An alias for `local` when both preflight and signatureVerification are `false`.
      * This call has minimum restrictions and can be used to read data from the node.
      *
      * @remarks
      * @see {@link IBaseClient.local | local() function}
      */
     dirtyRead: (transaction: IUnsignedCommand, options?: ClientRequestInit) => Promise<ICommandResult>;
     /**
      * Generates a command from the code and data, then sends it to the '/local' endpoint.
      *
      * @see {@link IBaseClient.local | local() function}
      */
     runPact: (code: string, data: Record<string, unknown>, option: ClientRequestInit & INetworkOptions) => Promise<ICommandResult>;
     /**
      * Alias for `submit`.
      * Use {@link IBaseClient.submit | submit() function}
      *
      * @deprecated Use `submit` instead.
      */
     send: ISubmit;
     /**
      * Alias for `submit` that accepts only one transaction. useful when you want more precise type checking.
      * {@link IBaseClient.submit | submit() function}
      */
     submitOne: (transaction: ICommand, options?: ClientRequestInit) => Promise<ITransactionDescriptor>;
     /**
      * Use {@link IBaseClient.getStatus | getStatus() function}
      * Alias for `getStatus`.
      *
      * @deprecated Use `getStatus` instead.
      */
     getPoll: (transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor, options?: ClientRequestInit) => Promise<IPollResponse>;
     /**
      * Polls the result of one request.
      * Calls the '/poll' endpoint.
      *
      *
      * @param transactionDescriptors - transaction descriptors to listen for.
      * @param options - options to adjust polling (onPoll, timeout, interval, and confirmationDepth).
      * @returns A promise that resolves to the command result.
      */
     pollOne: (transactionDescriptor: ITransactionDescriptor, options?: IPollOptions) => Promise<ICommandResult>;
 }

 export { ICommand }

 export { ICommandResult }

 /**
  * Interface that describes the common functions to be used with Ecko Wallet
  * @public
  */
 export declare interface ICommonEckoFunctions {
     isInstalled: () => boolean;
     isConnected: (networkId: string) => Promise<boolean>;
     connect: (networkId: string) => Promise<boolean>;
     checkStatus: (networkId: string) => Promise<IEckoConnectOrStatusResponse | undefined>;
 }

 /**
  * @internal
  */
 declare interface IContinuation {
     (options: WithRequired<IContinuationPayloadObject['cont'], 'pactId' | 'rollback' | 'step'>): IBuilder<{
         payload: IContinuationPayloadObject;
     }>;
 }

 /**
  * The payload of a Continuation transaction
  * @public
  */
 export declare interface IContinuationPayloadObject {
     cont: {
         pactId: string;
         step: number;
         rollback: boolean;
         data?: Record<string, unknown>;
         proof?: string | null;
     };
 }

 /**
  * @public
  */
 export declare interface ICreateClient {
     /**
      * Generates a client instance by passing the URL of the host.
      *
      * Useful when you are working with a single network and chainId.
      * @param hostUrl - the URL to use in the client
      * @param defaults - default options for the client it includes confirmationDepth that is used for polling
      */
     (hostUrl: string, defaults?: {
         confirmationDepth?: number;
     }): IClient;
     /**
      * Generates a client instance by passing a hostUrlGenerator function.
      *
      * Note: The default hostUrlGenerator creates a Kadena testnet or mainnet URL based on networkId.
      * @param hostAddressGenerator - the function that generates the URL based on `chainId` and `networkId` from the transaction
      * @param defaults - default options for the client it includes confirmationDepth that is used for polling
      */
     (hostAddressGenerator?: (options: {
         chainId: ChainId;
         networkId: string;
         type?: 'local' | 'send' | 'poll' | 'listen' | 'spv';
     }) => string | {
         hostUrl: string;
         requestInit: ClientRequestInit;
     }, defaults?: {
         confirmationDepth?: number;
     }): IClient;
 }

 /**
  * interface for the `createSignWithKeypair` function {@link createSignWithKeypair}
  *
  * @public
  */
 export declare interface ICreateSignWithKeypair {
     /**
      * @param key - provide the key to sign with
      * @returns a function to sign with
      *
      * @example
      * ```ts
      * const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);
      * const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);
      * const signedTx3 = await signWithKeystore(tx3);
      * ```
      *
      * @public
      */
     (key: IKeyPair): ISignFunction;
     /**
      * @param keys - provide the keys to sign with
      * @returns a function to sign with
      *
      *
      * @example
      * ```ts
      * const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);
      * const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);
      * const signedTx3 = await signWithKeystore(tx3);
      * ```
      *
      * @public
      */
     (keys: IKeyPair[]): ISignFunction;
 }

 /**
  * Interface that describes the response from Ecko Wallet when checking status or connecting
  * @public
  */
 export declare interface IEckoConnectOrStatusResponse {
     status: EckoStatus;
     message?: string;
     account?: {
         account: string;
         publicKey: string;
         connectedSites: string[];
     };
 }

 /**
  * Interface to use when writing a signing function for Ecko Wallet that accepts multiple transactions
  * @public
  */
 export declare interface IEckoSignFunction extends ISignFunction, ICommonEckoFunctions {
 }

 /**
  * Interface to use when writing a signing function for Ecko Wallet that accepts a single transaction
  * @public
  */
 export declare interface IEckoSignSingleFunction extends ISingleSignFunction, ICommonEckoFunctions {
 }

 /**
  * @internal
  */
 declare interface IExecution {
     <TCodes extends Array<(string & {
         capability(name: string, ...args: unknown[]): ICap;
     }) | string>>(...codes: [...TCodes]): IBuilder<{
         payload: IExecPayload & {
             funs: AddCapabilities<[...TCodes]>;
         };
     }>;
 }

 /**
  * The payload of a Execution transaction
  * @public
  */
 export declare interface IExecutionPayloadObject {
     exec: {
         code: string;
         data: Record<string, unknown>;
     };
 }

 /**
  * @internal
  */
 declare interface IGeneralCapability {
     (name: string, ...args: unknown[]): ICap;
     (name: 'coin.GAS'): ICap;
 }

 export { IKeyPair }

 /**
  * @public
  */
 export declare interface INetworkOptions {
     networkId: string;
     chainId: ChainId;
 }

 /**
  * Interface that represents the Pact object
  * @public
  */
 export declare interface IPact {
     modules: IPactModules;
     builder: ITransactionBuilder;
 }

 /**
  * The non-serialized transaction payload
  * @public
  */
 export declare interface IPactCommand {
     payload: IExecutionPayloadObject | IContinuationPayloadObject;
     meta: {
         chainId: ChainId;
         sender?: string;
         gasLimit?: number;
         gasPrice?: number;
         ttl?: number;
         creationTime?: number;
     };
     signers: Array<{
         pubKey: string;
         address?: string;
         scheme?: SignerScheme;
         clist?: ICap[];
     }>;
     verifiers?: Array<{
         name: string;
         proof: PactValue;
         clist?: ICap[];
     }>;
     networkId: string;
     nonce: string;
 }

 /**
  * Interface that represents the generated Pact modules
  * @public
  */
 export declare interface IPactModules {
 }

 /**
  * The the Partial type of {@link IPactCommand}
  * @public
  */
 export declare interface IPartialPactCommand extends AllPartial<IPactCommand> {
     payload?: {
         exec: Partial<IExecutionPayloadObject['exec']>;
     } | {
         cont: Partial<IContinuationPayloadObject['cont']>;
     };
 }

 /**
  * Options for any polling action on {@link IClient}
  * @public
  */
 export declare interface IPollOptions extends ClientRequestInit {
     onPoll?: (id: string | undefined, error: any) => void;
     onResult?: (requestKey: string, result: ICommandResult) => void;
     timeout?: Milliseconds;
     interval?: Milliseconds;
     confirmationDepth?: number;
 }

 /**
  * @public
  */
 export declare type IPollRequestPromise<T> = Promise<Record<string, T>> & {
     /**
      * @deprecated pass callback to {@link IPollOptions.onResult} instead
      */
     requests: Record<string, Promise<T>>;
 };

 export { IPollResponse }

 export { IPreflightResult }

 /**
  * Interface for the {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare interface IQuickSignRequestBody {
     cmdSigDatas: IUnsignedQuicksignTransaction[];
 }

 /**
  * Response from {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare type IQuicksignResponse = IQuicksignResponseError | IQuicksignResponseOutcomes;

 /**
  * response `commandSigData` in {@link IQuicksignResponseOutcomes}
  * @public
  */
 export declare interface IQuicksignResponseCommand {
     sigs: IQuicksignSigner[];
     cmd: string;
 }

 /**
  * Error response from {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare interface IQuicksignResponseError {
     error: {
         type: 'reject';
     } | {
         type: 'emptyList';
     } | {
         type: 'other';
         msg: string;
     };
 }

 /**
  * Succesful result from {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare interface IQuicksignResponseOutcomes {
     responses: {
         commandSigData: IQuicksignResponseCommand;
         outcome: {
             hash: string;
             result: 'success';
         } | {
             msg: string;
             result: 'failure';
         } | {
             result: 'noSig';
         };
     }[];
 }

 /**
  * `sig` in {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare type IQuicksignSig = string | null;

 /**
  * `sigs` in {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare interface IQuicksignSigner {
     pubKey: string;
     sig: IQuicksignSig;
 }

 declare interface ISetNonce<TCommand> {
     /**
      * Overriding the default nonce by calling this function
      */
     (nonce: string): IBuilder<TCommand>;
     /**
      * Overriding the default nonce by calling this function. The `nonceGenerator` function will receive the command object
      * and should return the nonce as a string.
      */
     (nonceGenerator: (cmd: IPartialPactCommand) => string): IBuilder<TCommand>;
 }

 /**
  * Interface for the {@link https://github.com/kadena-io/KIPs/blob/master/kip-0015.md | `sign v1` API}
  * @public
  */
 export declare interface ISignBody {
     code: string;
     caps: {
         role: string;
         description: string;
         cap: ICap;
     }[];
     data: Record<string, unknown>;
     sender: string;
     chainId: string;
     gasLimit: number;
     gasPrice: number;
     ttl: number;
     signingPubKey: string;
     networkId: string;
 }

 /**
  * @public
  */
 export declare type ISigner = string | {
     pubKey: string;
     scheme?: SignerScheme;
     address?: string;
 };

 /**
  * Interface to use when writing a signing function that accepts multiple transactions
  * @public
  */
 export declare interface ISignFunction extends ISingleSignFunction {
     (transactionList: IUnsignedCommand[]): Promise<(ICommand | IUnsignedCommand)[]>;
 }

 /**
  * @alpha
  */
 export declare interface ISigningRequest {
     code: string;
     data?: Record<string, unknown>;
     caps: ISigningCap[];
     nonce?: string;
     chainId?: IPactCommand['meta']['chainId'];
     gasLimit?: number;
     gasPrice?: number;
     ttl?: number;
     sender?: string;
     extraSigners?: string[];
 }

 /**
  * Interface to use when writing a signing function that accepts a single transaction
  * @public
  */
 export declare interface ISingleSignFunction {
     (transaction: IUnsignedCommand): Promise<ICommand | IUnsignedCommand>;
 }

 /**
  * Determines if a command is fully signed.
  *
  * @param command - The command to check.
  * @returns True if the command is signed, false otherwise.

  * @public
  */
 export declare function isSignedTransaction(command: IUnsignedCommand | ICommand): command is ICommand;

 /**
  * @public
  */
 export declare interface ISubmit {
     /**
      * Submits one public (unencrypted) signed command to the blockchain for execution.
      *
      * Calls the '/send' endpoint.
      * This is the only function that requires gas payment.
      *
      * @param transaction - The transaction to be submitted.
      * @returns A promise that resolves the transactionDescriptor {@link ITransactionDescriptor}
      */
     (transaction: ICommand, options?: ClientRequestInit): Promise<ITransactionDescriptor>;
     /**
      * Submits one or more public (unencrypted) signed commands to the blockchain for execution.
      *
      * Calls the '/send' endpoint.
      * This is the only function that requires gas payment.
      *
      * @param transactionList - The list of transactions to be submitted.
      * @returns A promise that resolves the transactionDescriptor {@link ITransactionDescriptor}
      */
     (transactionList: ICommand[], options?: ClientRequestInit): Promise<ITransactionDescriptor[]>;
 }

 /**
  * @public
  */
 export declare interface ITransactionBuilder {
     /**
      * create execution command
      *
      * @example
      * ```
      * Pact.builder
      *   .execution(Pact.modules.coin.transfer("bob","alice", {decimal:"10"}))
      * ```
      */
     execution: IExecution;
     /**
      * create continuation command
      * @example
      * ```
      * Pact.builder
      *   .continuation({ pactId:"id", proof:"spv_proof", rollback: false , step:1 , data:{} })
      * ```
      */
     continuation: IContinuation;
 }

 /**
  * Represents the object type that the `submit` or `send` function returns,
  * which other helper functions accept as the first input.
  * This ensures that we always have enough data to fetch the request from the chain.
  * @public
  */
 export declare interface ITransactionDescriptor {
     requestKey: string;
     chainId: ChainId;
     networkId: string;
 }

 export { IUnsignedCommand }

 /**
  * `cmdSigData` in {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}
  * @public
  */
 export declare interface IUnsignedQuicksignTransaction {
     sigs: IQuicksignSigner[];
     cmd: string;
 }

 declare interface IVerifier {
     name: string;
     proof: PactValue;
 }

 /**
  * the class for adding values to the final pact object without adding quotes to strings
  * @public
  */
 export declare class Literal {
     private _value;
     constructor(value: string);
     getValue(): string;
     toJSON(): string;
     toString(): string;
 }

 /**
  * Will create a literal pact expression without surrounding quotes `"`
  * @example
  * ```
  * // use literal as function input
  * Pact.module.["free.crowdfund"]["create-project"](
  *   "project_id",
  *   "project_name",
  *   // this is a reference to another module and should not have quotes in the created expression
  *   literal("coin"),
  *   ...
  * )
  *
  * // use literal as a property of a json in the input
  * Pact.module.["my-contract"]["set-details"](
  *   "name",
  *   "data" : {
  *      age : 35,
  *      tokens : [literal("coin"), literal("kdx")]
  *   }
  * )
  * ```
  * @public
  */
 export declare const literal: (value: string) => Literal;

 /**
  * @alpha
  */
 export declare type Milliseconds = number & {
     _brand?: 'milliseconds';
 };

 declare type ModuleWithNamespace<T> = T extends `(${infer namespaceType}.${infer moduleType}.${infer func}${FnRest})` ? func extends `${string} ` | `${string} ${string}` ? never : GetFuncReturnType<`${namespaceType}.${moduleType}`, func> : never;

 /**
  * The wrapper object that provides the Transaction builder and Contract interface
  * @public
  */
 export declare const Pact: IPact;

 /**
  * Pact error codes
  * @public
  */
 export declare type PactErrorCode = 'RECORD_NOT_FOUND' | 'DEFPACT_COMPLETED' | 'CANNOT_RESOLVE_MODULE' | 'EMPTY_CODE' | 'ERROR';

 /**
  * General type for reference values
  * @public
  */
 export declare type PactReference = Literal | (() => string);

 /**
  * @public
  */
 export declare type PactReturnType<T extends (...args: any[]) => any> = T extends (...args: any[]) => infer R ? R extends {
     returnType: infer RR;
 } ? RR : any : any;

 /**
  * @public
  */
 export declare function parseAsPactValue(input: PactValue | (() => string) | Literal): string;

 declare type Prettify<T extends Record<string, unknown>> = {
     [K in keyof T]: T[K];
 } & {};

 /**
  * Helper function that returns `(read-keyset "key")` Pact expression
  * @public
  */
 export declare const readKeyset: (key: string) => () => string;

 declare type RootModule<T> = T extends `(${infer moduleType}.${infer func}${FnRest})` ? func extends `${string} ` | `${string} ${string}` ? never : GetFuncReturnType<moduleType, func> : never;

 /**
  * @public
  */
 export declare type SignerScheme = 'ED25519' | 'ETH' | 'WebAuthn';

 /**
  *
  * @internal
  *
  */
 export declare const signTransactions: (chainweaverUrl: string) => ISignFunction;

 /**
  * * Lets you sign with chainweaver according to {@link https://github.com/kadena-io/KIPs/blob/master/kip-0015.md | sign-v1 API}
  *
  * @deprecated Use {@link createSignWithChainweaver} instead
  * @public
  */
 export declare const signWithChainweaver: ISignFunction;

 /**
  * The Blockchain that's used in a WalletConnect context
  *
  * @remarks
  * For kadena it is `kadena:<networkId>`
  *
  * @see Reference {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md#pairing-with-walletconnect | KIP-0017 WalletConnect Specification }
  * @public
  */
 export declare type TWalletConnectChainId = `kadena:${IPactCommand['networkId']}`;

 /**
  * @internal
  */
 declare type UnionToIntersection<T> = (T extends unknown ? (k: T) => void : never) extends (k: infer I) => void ? I : never;

 /**
  * unpack all of the Literal(string) to string
  * @internal
  */
 export declare function unpackLiterals(value: string): string;

 declare type ValidDataTypes = Record<string, unknown> | string | number | boolean | Array<ValidDataTypes>;

 /**
  * create withCapability type from a Pact.modules function
  *
  * @public
  */
 export declare type WithCapability<TCode extends string & {
     capability: unknown;
 }> = ExtractCapabilityType<{
     payload: {
         funs: [TCode];
     };
 }>;

 declare type WithRequired<T, K extends keyof T> = Prettify<T & {
     [P in K]-?: T[P];
 }>;

 export { }
