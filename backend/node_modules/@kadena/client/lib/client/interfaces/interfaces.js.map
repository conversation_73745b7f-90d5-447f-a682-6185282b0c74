{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../../../src/client/interfaces/interfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["import type {\n  ClientRequestInit,\n  ICommandResult,\n} from '@kadena/chainweb-node-client';\nimport type { ChainId } from '@kadena/types';\n\n/**\n * @public\n */\nexport interface INetworkOptions {\n  networkId: string;\n  chainId: ChainId;\n}\n\n/**\n * @alpha\n */\nexport type Milliseconds = number & { _brand?: 'milliseconds' };\n\n/**\n * Options for any polling action on {@link IClient}\n * @public\n */\nexport interface IPollOptions extends ClientRequestInit {\n  onPoll?: (id: string | undefined, error: any) => void;\n  onResult?: (requestKey: string, result: ICommandResult) => void;\n  timeout?: Milliseconds;\n  interval?: Milliseconds;\n  confirmationDepth?: number;\n}\n\n/**\n * @public\n */\nexport type IPollRequestPromise<T> = Promise<Record<string, T>> & {\n  /**\n   * @deprecated pass callback to {@link IPollOptions.onResult} instead\n   */\n  requests: Record<string, Promise<T>>;\n};\n"]}