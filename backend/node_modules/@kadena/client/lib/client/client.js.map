{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/client/client.ts"], "names": [], "mappings": ";;;AASA,uEAAyE;AAGzE,2CAAwC;AACxC,mCAA4C;AAC5C,yCAA0C;AAM1C,uDAAoD;AACpD,yCAKuB;AA+QvB,MAAM,WAAW,GAAG,CAClB,UAAwE,EACxE,EAAE;IACF,MAAM,OAAO,GACX,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC;IACnE,MAAM,WAAW,GACf,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/D,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;AAClC,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,YAAY,GAAkB,CACzC,IAAI,GAAG,2BAAmB,EAC1B,QAAQ,GAAG,EAAE,iBAAiB,EAAE,CAAC,EAAE,EAC1B,EAAE;IACX,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;IACrD,MAAM,OAAO,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAE7D,MAAM,MAAM,GAAgB;QAC1B,KAAK,CAAC,IAAI,EAAE,OAAO;YACjB,MAAM,GAAG,GAAiB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,UAAU,GAAG,OAAO,CAAC;gBACzB,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO;gBACzB,SAAS,EAAE,GAAG,CAAC,SAAS;aACzB,CAAC,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,IAAA,4BAAK,EAAC,IAAI,EAAE,OAAO,EAAE,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;YAC/B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;YACzB,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACxC,CAAC;YACD,MAAM,GAAG,GAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,OAAO,CAAC;gBACzB,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO;gBACzB,SAAS,EAAE,GAAG,CAAC,SAAS;aACzB,CAAC,CAAC;YAEH,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YAEzD,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,IAAA,2BAAI,EAChC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAClB,OAAO,EACP,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CACnC,CAAC;YAEF,MAAM,sBAAsB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACvD,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO;gBACzB,SAAS,EAAE,GAAG,CAAC,SAAS;aACzB,CAAC,CAAC,CAAC;YAEJ,OAAO,MAAM,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAY;QACb,UAAU,CACR,sBAAyE,EACzE,OAAsB;YAEtB,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;gBACxD,CAAC,CAAC,sBAAsB;gBACxB,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC;YAC7B,MAAM,OAAO,GAAG,IAAA,mBAAW,EACzB,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE;gBACtD,MAAM,UAAU,GAAG,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBACjE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;gBACzD,OAAO;oBACL,UAAU;oBACV,IAAI,EAAE,OAAO;oBACb,WAAW;iBACZ,CAAC;YACJ,CAAC,CAAC,CACH,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE;gBAC5B,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBAC/C,OAAO,IAAA,mBAAU,EACf,IAAI,EACJ,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,EACpC;oBACE,iBAAiB;oBACjB,GAAG,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC;iBACtC,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,wCAAwC;YACxC,MAAM,yBAAyB,GAAG,IAAA,mCAA2B,EAAC,OAAO,CAAC,CAAC;YAEvE,OAAO,yBAAyB,CAAC;QACnC,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,sBAAsB,EAAE,OAA2B;YACjE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;gBACxD,CAAC,CAAC,sBAAsB;gBACxB,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC;YAE7B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,IAAA,mBAAW,EACT,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE;gBACtD,MAAM,UAAU,GAAG,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBACjE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;gBACzD,OAAO;oBACL,UAAU;oBACV,IAAI,EAAE,OAAO;oBACb,WAAW;iBACZ,CAAC;YACJ,CAAC,CAAC,CACH,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,EAAE;gBAC/B,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBAE/C,OAAO,IAAA,2BAAI,EACT,EAAE,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,EACrD,OAAO,EACP,SAAS,EACT,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CACnC,CAAC;YACJ,CAAC,CAAC,CACH,CAAC;YAEF,wCAAwC;YACxC,MAAM,aAAa,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,CAAC;YAExC,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO;YACtD,MAAM,UAAU,GAAG,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YACnE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,MAAM,IAAA,6BAAM,EACzB,EAAE,MAAM,EAAE,UAAU,EAAE,EACtB,OAAO,EACP,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CACnC,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,aAAa,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,aAAa,EAAE,OAAO;YACtE,MAAM,UAAU,GAAG,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,IAAA,aAAO,EACZ,OAAO,EACP,UAAU,EACV,aAAa,EACb,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CACnC,CAAC;QACJ,CAAC;QAED,KAAK,CAAC,SAAS,CACb,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAClC,aAAa,EACb,OAAO;YAEP,MAAM,UAAU,GAAG,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,IAAA,YAAM,EACX,OAAO,EACP,UAAU,EACV,aAAa,EACb,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CACnC,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,OAAO;QACL,GAAG,MAAM;QACT,SAAS,EAAE,MAAM,CAAC,MAAM;QACxB,SAAS,CAAC,IAAI,EAAE,OAAO;YACrB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;gBACxB,GAAG,OAAO;gBACV,SAAS,EAAE,IAAI;gBACf,qBAAqB,EAAE,IAAI;aAC5B,CAAC,CAAC;QACL,CAAC;QACD,qBAAqB,CAAC,IAAI,EAAE,OAAO;YACjC,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;gBACxB,GAAG,OAAO;gBACV,SAAS,EAAE,KAAK;gBAChB,qBAAqB,EAAE,IAAI;aAC5B,CAAC,CAAC;QACL,CAAC;QACD,SAAS,CAAC,IAAI,EAAE,OAAO;YACrB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;gBACxB,GAAG,OAAO;gBACV,SAAS,EAAE,KAAK;gBAChB,qBAAqB,EAAE,KAAK;aAC7B,CAAC,CAAC;QACL,CAAC;QACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;YAC/B,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;YACzD,IAAI,OAAO,KAAK,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;YAEnD,OAAO,IAAA,iBAAO,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAA,2BAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;QAC1E,CAAC;QACD,IAAI,EAAE,MAAM,CAAC,MAAM;QACnB,OAAO,EAAE,MAAM,CAAC,SAAS;QACzB,OAAO,EAAE,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE;YAC1C,OAAO,MAAM;iBACV,UAAU,CAAC,qBAAqB,EAAE,OAAO,CAAC;iBAC1C,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1D,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAhMW,QAAA,YAAY,gBAgMvB", "sourcesContent": ["import type {\n  ClientRequestInit,\n  ICommandResult,\n  ILocalCommandResult,\n  ILocalOptions,\n  IPollResponse,\n  LocalRequestBody,\n  LocalResponse,\n} from '@kadena/chainweb-node-client';\nimport { listen, local, poll, send } from '@kadena/chainweb-node-client';\nimport type { ChainId, ICommand, IUnsignedCommand } from '@kadena/types';\nimport type { IPactCommand } from '../interfaces/IPactCommand';\nimport { runPact } from './api/runPact';\nimport { getSpv, pollSpv } from './api/spv';\nimport { pollStatus } from './api/status';\nimport type {\n  INetworkOptions,\n  IPollOptions,\n  IPollRequestPromise,\n} from './interfaces/interfaces';\nimport { mergeOptions } from './utils/mergeOptions';\nimport {\n  groupByHost,\n  kadenaHostGenerator,\n  mergeAll,\n  mergeAllPollRequestPromises,\n} from './utils/utils';\n\n/**\n * Represents the object type that the `submit` or `send` function returns,\n * which other helper functions accept as the first input.\n * This ensures that we always have enough data to fetch the request from the chain.\n * @public\n */\nexport interface ITransactionDescriptor {\n  requestKey: string;\n  chainId: ChainId;\n  networkId: string;\n}\n\n/**\n * @public\n */\nexport interface ISubmit {\n  /**\n   * Submits one public (unencrypted) signed command to the blockchain for execution.\n   *\n   * Calls the '/send' endpoint.\n   * This is the only function that requires gas payment.\n   *\n   * @param transaction - The transaction to be submitted.\n   * @returns A promise that resolves the transactionDescriptor {@link ITransactionDescriptor}\n   */\n  (\n    transaction: ICommand,\n    options?: ClientRequestInit,\n  ): Promise<ITransactionDescriptor>;\n\n  /**\n   * Submits one or more public (unencrypted) signed commands to the blockchain for execution.\n   *\n   * Calls the '/send' endpoint.\n   * This is the only function that requires gas payment.\n   *\n   * @param transactionList - The list of transactions to be submitted.\n   * @returns A promise that resolves the transactionDescriptor {@link ITransactionDescriptor}\n   */\n  (\n    transactionList: ICommand[],\n    options?: ClientRequestInit,\n  ): Promise<ITransactionDescriptor[]>;\n}\n\n/**\n * @public\n */\nexport interface IBaseClient {\n  /**\n   * Sends a command for non-transactional execution.\n   * In a blockchain environment, this would be a node-local \"dirty read\".\n   * Any database writes or changes to the environment are rolled back.\n   * Gas payment is not required for this function.\n   *\n   * Calls the '/local' endpoint with optional options.\n   *\n   * @param transaction - The transaction to be executed.\n   * @param options - Optional settings for preflight and signatureVerification.\n   * @returns A promise that resolves to the local response.\n   */\n  local: <T extends ILocalOptions>(\n    transaction: LocalRequestBody,\n    options?: T,\n  ) => Promise<LocalResponse<T>>;\n\n  /**\n   * Submits one or more public (unencrypted) signed commands to the blockchain for execution.\n   *\n   * Calls the '/send' endpoint.\n   * This is the only function that requires gas payment.\n   *\n   * @param transactionList - The list of transactions to be submitted.\n   * @returns A promise that resolves the transactionDescriptor {@link ITransactionDescriptor}\n   */\n  submit: ISubmit;\n\n  /**\n   * Polls the result of one or more submitted requests.\n   * Calls the '/poll' endpoint multiple times to get the status of all requests.\n   *\n   * @param transactionDescriptors - transaction descriptors to status polling.\n   * @param options - options to adjust polling (onPoll, timeout, interval, and confirmationDepth).\n   * @returns A promise that resolves to the poll request promise with the command result.\n   */\n  pollStatus: (\n    transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor,\n    options?: IPollOptions,\n  ) => IPollRequestPromise<ICommandResult>;\n\n  /**\n   * Gets the result of one or more submitted requests.\n   * If the result is not ready, it returns an empty object.\n   * Calls the '/poll' endpoint only once.\n   *\n   * @param transactionDescriptors - transaction descriptors to get the status.\n   * @returns  A promise that resolves to the poll response with the command result.\n   */\n  getStatus: (\n    transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor,\n    options?: ClientRequestInit,\n  ) => Promise<IPollResponse>;\n\n  /**\n   * Listens for the result of the request. This is a long-polling process that eventually returns the result.\n   * Calls the '/listen' endpoint.\n   *\n   *\n   * @param transactionDescriptors - transaction descriptors to listen for.\n   * @returns A promise that resolves to the command result.\n   */\n  listen: (\n    transactionDescriptor: ITransactionDescriptor,\n    options?: ClientRequestInit,\n  ) => Promise<ICommandResult>;\n\n  /**\n   * Creates an SPV proof for a request. This is required for multi-step tasks.\n   * Calls the '/spv' endpoint several times to retrieve the SPV proof.\n   *\n   *\n   * @param transactionDescriptor - The request key for which the SPV proof is generated.\n   * @param targetChainId - The target chain ID for the SPV proof.\n   * @param options - options to adjust polling (onPoll, timeout, and interval).\n   * @returns A promise that resolves to the generated SPV proof.\n   */\n  pollCreateSpv: (\n    transactionDescriptor: ITransactionDescriptor,\n    targetChainId: ChainId,\n    options?: IPollOptions,\n  ) => Promise<string>;\n\n  /**\n   * Creates an SPV proof for a request. This is required for multi-step tasks.\n   * Calls the '/spv' endpoint only once.\n   *\n   *\n   * @param transactionDescriptor - The transaction descriptor for which the SPV proof is generated.\n   * @param targetChainId - The target chain ID for the SPV proof.\n   * @returns A promise that resolves to the generated SPV proof.\n   */\n  createSpv: (\n    transactionDescriptor: ITransactionDescriptor,\n    targetChainId: ChainId,\n    options?: ClientRequestInit,\n  ) => Promise<string>;\n}\n\n/**\n * Interface for the {@link createClient | createClient()} return value\n * @public\n */\nexport interface IClient extends IBaseClient {\n  /**\n   * An alias for `local` when both preflight and signatureVerification are `true`.\n   * @see local\n   */\n  preflight: (\n    transaction: ICommand | IUnsignedCommand,\n    options?: ClientRequestInit,\n  ) => Promise<ILocalCommandResult>;\n\n  /**\n   * An alias for `local` when preflight is `false` and signatureVerification is `true`.\n   *\n   * @remarks\n   * @see {@link IBaseClient.local | local() function}\n   */\n  signatureVerification: (\n    transaction: ICommand,\n    options?: ClientRequestInit,\n  ) => Promise<ICommandResult>;\n\n  /**\n   * An alias for `local` when both preflight and signatureVerification are `false`.\n   * This call has minimum restrictions and can be used to read data from the node.\n   *\n   * @remarks\n   * @see {@link IBaseClient.local | local() function}\n   */\n  dirtyRead: (\n    transaction: IUnsignedCommand,\n    options?: ClientRequestInit,\n  ) => Promise<ICommandResult>;\n\n  /**\n   * Generates a command from the code and data, then sends it to the '/local' endpoint.\n   *\n   * @see {@link IBaseClient.local | local() function}\n   */\n  runPact: (\n    code: string,\n    data: Record<string, unknown>,\n    option: ClientRequestInit & INetworkOptions,\n  ) => Promise<ICommandResult>;\n\n  /**\n   * Alias for `submit`.\n   * Use {@link IBaseClient.submit | submit() function}\n   *\n   * @deprecated Use `submit` instead.\n   */\n  send: ISubmit;\n\n  /**\n   * Alias for `submit` that accepts only one transaction. useful when you want more precise type checking.\n   * {@link IBaseClient.submit | submit() function}\n   */\n  submitOne: (\n    transaction: ICommand,\n    options?: ClientRequestInit,\n  ) => Promise<ITransactionDescriptor>;\n\n  /**\n   * Use {@link IBaseClient.getStatus | getStatus() function}\n   * Alias for `getStatus`.\n   *\n   * @deprecated Use `getStatus` instead.\n   */\n  getPoll: (\n    transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor,\n    options?: ClientRequestInit,\n  ) => Promise<IPollResponse>;\n\n  /**\n   * Polls the result of one request.\n   * Calls the '/poll' endpoint.\n   *\n   *\n   * @param transactionDescriptors - transaction descriptors to listen for.\n   * @param options - options to adjust polling (onPoll, timeout, interval, and confirmationDepth).\n   * @returns A promise that resolves to the command result.\n   */\n  pollOne: (\n    transactionDescriptor: ITransactionDescriptor,\n    options?: IPollOptions,\n  ) => Promise<ICommandResult>;\n}\n\n/**\n * @public\n */\nexport interface ICreateClient {\n  /**\n   * Generates a client instance by passing the URL of the host.\n   *\n   * Useful when you are working with a single network and chainId.\n   * @param hostUrl - the URL to use in the client\n   * @param defaults - default options for the client it includes confirmationDepth that is used for polling\n   */\n  (hostUrl: string, defaults?: { confirmationDepth?: number }): IClient;\n\n  /**\n   * Generates a client instance by passing a hostUrlGenerator function.\n   *\n   * Note: The default hostUrlGenerator creates a Kadena testnet or mainnet URL based on networkId.\n   * @param hostAddressGenerator - the function that generates the URL based on `chainId` and `networkId` from the transaction\n   * @param defaults - default options for the client it includes confirmationDepth that is used for polling\n   */\n  (\n    hostAddressGenerator?: (options: {\n      chainId: ChainId;\n      networkId: string;\n      type?: 'local' | 'send' | 'poll' | 'listen' | 'spv';\n    }) => string | { hostUrl: string; requestInit: ClientRequestInit },\n    defaults?: { confirmationDepth?: number },\n  ): IClient;\n}\n\nconst getHostData = (\n  hostObject: string | { hostUrl: string; requestInit: ClientRequestInit },\n) => {\n  const hostUrl =\n    typeof hostObject === 'string' ? hostObject : hostObject.hostUrl;\n  const requestInit =\n    typeof hostObject === 'object' ? hostObject.requestInit : {};\n  return { hostUrl, requestInit };\n};\n\n/**\n * Creates Chainweb client\n * @public\n */\nexport const createClient: ICreateClient = (\n  host = kadenaHostGenerator,\n  defaults = { confirmationDepth: 0 },\n): IClient => {\n  const confirmationDepth = defaults.confirmationDepth;\n  const getHost = typeof host === 'string' ? () => host : host;\n\n  const client: IBaseClient = {\n    local(body, options) {\n      const cmd: IPactCommand = JSON.parse(body.cmd);\n      const hostObject = getHost({\n        chainId: cmd.meta.chainId,\n        networkId: cmd.networkId,\n      });\n      const { hostUrl, requestInit } = getHostData(hostObject);\n      return local(body, hostUrl, mergeOptions(requestInit, options));\n    },\n    submit: (async (body, options) => {\n      const isList = Array.isArray(body);\n      const commands = isList ? body : [body];\n      const [first] = commands;\n      if (first === undefined) {\n        throw new Error('EMPTY_COMMAND_LIST');\n      }\n      const cmd: IPactCommand = JSON.parse(first.cmd);\n      const hostObject = getHost({\n        chainId: cmd.meta.chainId,\n        networkId: cmd.networkId,\n      });\n\n      const { hostUrl, requestInit } = getHostData(hostObject);\n\n      const { requestKeys } = await send(\n        { cmds: commands },\n        hostUrl,\n        mergeOptions(requestInit, options),\n      );\n\n      const transactionDescriptors = requestKeys.map((key) => ({\n        requestKey: key,\n        chainId: cmd.meta.chainId,\n        networkId: cmd.networkId,\n      }));\n\n      return isList ? transactionDescriptors : transactionDescriptors[0];\n    }) as ISubmit,\n    pollStatus(\n      transactionDescriptors: ITransactionDescriptor[] | ITransactionDescriptor,\n      options?: IPollOptions,\n    ): IPollRequestPromise<ICommandResult> {\n      const requestsList = Array.isArray(transactionDescriptors)\n        ? transactionDescriptors\n        : [transactionDescriptors];\n      const results = groupByHost(\n        requestsList.map(({ requestKey, chainId, networkId }) => {\n          const hostObject = getHost({ chainId, networkId, type: 'poll' });\n          const { hostUrl, requestInit } = getHostData(hostObject);\n          return {\n            requestKey,\n            host: hostUrl,\n            requestInit,\n          };\n        }),\n      ).map(([host, requestKeys]) => {\n        const requestInit = requestKeys[0].requestInit;\n        return pollStatus(\n          host,\n          requestKeys.map((r) => r.requestKey),\n          {\n            confirmationDepth,\n            ...mergeOptions(requestInit, options),\n          },\n        );\n      });\n\n      // merge all of the result in one object\n      const mergedPollRequestPromises = mergeAllPollRequestPromises(results);\n\n      return mergedPollRequestPromises;\n    },\n    async getStatus(transactionDescriptors, options?: ClientRequestInit) {\n      const requestsList = Array.isArray(transactionDescriptors)\n        ? transactionDescriptors\n        : [transactionDescriptors];\n\n      const results = await Promise.all(\n        groupByHost(\n          requestsList.map(({ requestKey, chainId, networkId }) => {\n            const hostObject = getHost({ chainId, networkId, type: 'poll' });\n            const { hostUrl, requestInit } = getHostData(hostObject);\n            return {\n              requestKey,\n              host: hostUrl,\n              requestInit,\n            };\n          }),\n        ).map(([hostUrl, requestKeys]) => {\n          const requestInit = requestKeys[0].requestInit;\n\n          return poll(\n            { requestKeys: requestKeys.map((r) => r.requestKey) },\n            hostUrl,\n            undefined,\n            mergeOptions(requestInit, options),\n          );\n        }),\n      );\n\n      // merge all of the result in one object\n      const mergedResults = mergeAll(results);\n\n      return mergedResults;\n    },\n\n    async listen({ requestKey, chainId, networkId }, options) {\n      const hostObject = getHost({ chainId, networkId, type: 'listen' });\n      const { hostUrl, requestInit } = getHostData(hostObject);\n      const result = await listen(\n        { listen: requestKey },\n        hostUrl,\n        mergeOptions(requestInit, options),\n      );\n\n      return result;\n    },\n\n    pollCreateSpv({ requestKey, chainId, networkId }, targetChainId, options) {\n      const hostObject = getHost({ chainId, networkId, type: 'spv' });\n      const { hostUrl, requestInit } = getHostData(hostObject);\n      return pollSpv(\n        hostUrl,\n        requestKey,\n        targetChainId,\n        mergeOptions(requestInit, options),\n      );\n    },\n\n    async createSpv(\n      { requestKey, chainId, networkId },\n      targetChainId,\n      options,\n    ) {\n      const hostObject = getHost({ chainId, networkId, type: 'spv' });\n      const { hostUrl, requestInit } = getHostData(hostObject);\n      return getSpv(\n        hostUrl,\n        requestKey,\n        targetChainId,\n        mergeOptions(requestInit, options),\n      );\n    },\n  };\n\n  return {\n    ...client,\n    submitOne: client.submit,\n    preflight(body, options) {\n      return client.local(body, {\n        ...options,\n        preflight: true,\n        signatureVerification: true,\n      });\n    },\n    signatureVerification(body, options) {\n      return client.local(body, {\n        ...options,\n        preflight: false,\n        signatureVerification: true,\n      });\n    },\n    dirtyRead(body, options) {\n      return client.local(body, {\n        ...options,\n        preflight: false,\n        signatureVerification: false,\n      });\n    },\n    runPact: (code, data, options) => {\n      const hostObject = getHost(options);\n      const { hostUrl, requestInit } = getHostData(hostObject);\n      if (hostUrl === '') throw new Error('NO_HOST_URL');\n\n      return runPact(hostUrl, code, data, mergeOptions(requestInit, options));\n    },\n    send: client.submit,\n    getPoll: client.getStatus,\n    pollOne: (transactionDescriptor, options) => {\n      return client\n        .pollStatus(transactionDescriptor, options)\n        .then((res) => res[transactionDescriptor.requestKey]);\n    },\n  };\n};\n"]}