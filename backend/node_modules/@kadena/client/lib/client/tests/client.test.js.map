{"version": 3, "file": "client.test.js", "sourceRoot": "", "sources": ["../../../src/client/tests/client.test.ts"], "names": [], "mappings": ";;AACA,uEAAqD;AAErD,6BAAyC;AACzC,mCAAuC;AACvC,mCAQgB;AAChB,sCAAyC;AAEzC,mCAAmC;AACnC,WAAE,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IAC/D,MAAM,GAAG,GAA8B,MAAM,cAAc,EAAE,CAAC;IAC9D,MAAM,KAAK,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpD,OAAO,EAAE,GAAG,GAAG,EAAE,KAAK,EAAE,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,IAAA,kBAAW,GAAE,CAAC;AAC7B,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,MAAM,IAAI,GAAG,CACX,IAAY,EACZ,QAA0C,EAC1C,MAAM,GAAG,GAAG,EACkB,EAAE,CAChC,UAAI,CAAC,IAAI,CACP,IAAI,EACJ,GAAG,EAAE,CACH,OAAO,QAAQ,KAAK,QAAQ;IAC1B,CAAC,CAAC,IAAI,kBAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC;IACxC,CAAC,CAAC,kBAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,EAC7C,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;AAEJ,MAAM,gBAAgB,GAAG,CAAC,EACxB,SAAS,EACT,OAAO,GAIR,EAAU,EAAE,CAAC,sBAAsB,SAAS,IAAI,OAAO,EAAE,CAAC;AAE3D,IAAA,iBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,IAAA,WAAE,EAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;QAClE,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;QAExC,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,8CAA8C,EAAE,QAAQ,CAAC,CAC/D,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,qBAAY,EAAC,OAAO,CAAC,CAAC;QAExC,MAAM,IAAI,GAAG;YACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC;YACvE,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;SAC5B,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;QACnE,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,2EAA2E,EAC3E,EAAE,MAAM,EAAE,UAAU,EAAE,CACvB,CACF,CAAC;QAEF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,qBAAY,GAAE,CAAC;QAEjC,MAAM,SAAS,GAAG,WAAW,CAAC;QAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;QAEpB,MAAM,IAAI,GAAG;YACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;YACrD,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;SAC5B,CAAC;QAEF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,yGAAyG,EAAE,KAAK,IAAI,EAAE;QACvH,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CAAC,4CAA4C,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;YACtE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,kBAAY,CAAC,IAAI,CAAC;gBACvB,UAAU,EAAE;oBACV,MAAM,EAAE,UAAU;oBAClB,iBAAiB,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC;iBAC7D;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CACH,CAAC;QAEF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,EAAE;YACjD,iBAAiB,EAAE,CAAC;SACrB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;YAC3B,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,WAAW;YACtB,OAAO,EAAE,GAAG;SACb,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,WAAE,EAAC,oFAAoF,EAAE,KAAK,IAAI,EAAE;YAClG,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CACP,4CAA4C,EAC5C,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;gBACd,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACjC,OAAO,kBAAY,CAAC,IAAI,CAAC;oBACvB,UAAU,EAAE;wBACV,MAAM,EAAE,UAAU;wBAClB,iBAAiB,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC;qBAC7D;iBACF,CAAC,CAAC;YACL,CAAC,CACF,CACF,CAAC;YAEF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,EAAE;gBACjD,iBAAiB,EAAE,CAAC;aACrB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAC1B;gBACE,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,WAAW;gBACtB,OAAO,EAAE,GAAG;aACb,EACD;gBACE,iBAAiB,EAAE,CAAC;aACrB,CACF,CAAC;YACF,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,OAAO,EAAE,GAAG,EAAE;QACrB,IAAA,WAAE,EAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;YACxF,MAAM,gBAAgB,GAAG;gBACvB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,aAAa;aACtB,CAAC;YACF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,gBAAgB,CAAC,CACtE,CAAC;YAEF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAEjD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAA,eAAM,EAAC,GAAG,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,WAAE,EAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;YACzF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,4CAA4C,EAAE;gBACjD,WAAW,EAAE,CAAC,UAAU,CAAC;aAC1B,CAAC,CACH,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAA,eAAM,EAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC;gBACpC,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,4CAA4C,EAAE;gBACjD,WAAW,EAAE,CAAC,UAAU,CAAC;aAC1B,CAAC,CACH,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAA,eAAM,EAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC;gBACpC,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,4CAA4C,EAAE;gBACjD,WAAW,EAAE,CAAC,UAAU,CAAC;aAC1B,CAAC,CACH,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,MAAM,sBAAsB,GAAG,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAEpD,IAAA,eAAM,EAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC;gBACrC,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE;aACjE,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,qBAAY,EAAC,GAAG,EAAE,CAAC,sBAAsB,CAAC,CAAC;YAC9D,MAAM,IAAA,eAAM,EAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAC3C,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAChC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,IAAA,WAAE,EAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;YACzF,MAAM,QAAQ,GAAG;gBACf,cAAc;gBACd,EAAE;gBACF,eAAe;gBACf,EAAE;gBACF,cAAc;gBACd,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE;aACvC,CAAC;YAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,4CAA4C,EAAE;gBACjD,WAAW,EAAE,CAAC,UAAU,CAAC;aAC1B,CAAC,EACF,IAAI,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC/D,IAAI,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC/D,IAAI,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChE,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAE9D,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAA,eAAM,EAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAE7D,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,qBAAqB,EAAE;gBACrD,QAAQ,EAAE,EAAE;aACb,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,QAAQ,GAAG;gBACf,cAAc;gBACd,EAAE;gBACF,eAAe;gBACf,EAAE;gBACF,cAAc;gBACd;oBACE,YAAY,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE;oBACtC,YAAY,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE;iBACvC;aACF,CAAC;YAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,4CAA4C,EAAE;gBACjD,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;aAC1C,CAAC,EACF,IAAI,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC/D,IAAI,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC/D,IAAI,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChE,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAE9D,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX;oBACE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;oBACrD,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;iBAC5B;gBACD;oBACE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;oBACrD,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;iBAC5B;aACF,CAAC;YAEF,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAA,eAAM,EAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,qBAAqB,EAAE;gBACrD,QAAQ,EAAE,EAAE;aACb,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAA,WAAE,EAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,kFAAkF,EAClF,EAAE,CACH,CACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,qBAAY,GAAE,CAAC;YAErC,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC;gBAC7B,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;YACpF,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,kFAAkF,EAClF,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,CACrD,CACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,qBAAY,GAAE,CAAC;YAErC,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC;gBAC7B;oBACE,UAAU,EAAE,YAAY;oBACxB,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE,WAAW;iBACvB;gBACD,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE;aACnE,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,YAAY,EAAE,SAAS;gBACvB,YAAY,EAAE,SAAS;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,WAAE,EAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,oFAAoF,EACpF,EAAE,MAAM,EAAE,UAAU,EAAE,CACvB,CACF,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,qBAAY,GAAE,CAAC;YAElC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC;gBAC1B,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,0EAA0E,EAC1E,OAAO,CACR,CACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAA,qBAAY,GAAE,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CACzB;gBACE,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,EACD,GAAG,CACJ,CAAC;YAEF,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,0BAA0B,EAAE,WAAW,EAAE,GAAG,CAAC,EAClD,IAAI,CAAC,0BAA0B,EAAE,WAAW,EAAE,GAAG,CAAC,EAClD,IAAI,CAAC,0BAA0B,EAAE,WAAW,EAAE,GAAG,CAAC,EAClD,IAAI,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAC1C,CAAC;YAEF,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,IAAA,qBAAY,EAAC,sBAAsB,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,MAAM,OAAO,CAC1B;gBACE,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,EACD,GAAG,EACH,EAAE,QAAQ,EAAE,EAAE,EAAE,CACjB,CAAC;YAEF,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAA,WAAE,EAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;YACxF,MAAM,gBAAgB,GAAG;gBACvB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,aAAa;aACtB,CAAC;YACF,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,uFAAuF,EACvF,gBAAgB,CACjB,CACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAErD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,IAAA,eAAM,EAAC,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAA,iBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAA,WAAE,EAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;YACzF,MAAM,gBAAgB,GAAG;gBACvB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,aAAa;aACtB,CAAC;YACF,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,wFAAwF,EACxF,gBAAgB,CACjB,CACF,CAAC;YAEF,MAAM,EAAE,qBAAqB,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAEjE,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,IAAA,eAAM,EAAC,MAAM,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAA,WAAE,EAAC,4EAA4E,EAAE,KAAK,IAAI,EAAE;YAC1F,MAAM,gBAAgB,GAAG;gBACvB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,aAAa;aACtB,CAAC;YACF,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,yFAAyF,EACzF,gBAAgB,CACjB,CACF,CAAC;YAEF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YAErD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC;YAEpB,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;gBACrD,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5B,CAAC;YAEF,IAAA,eAAM,EAAC,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,kBAAS,EAAC,GAAG,EAAE;YACb,WAAE,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,iBAAQ,EAAC,GAAG,EAAE;YACZ,WAAE,CAAC,aAAa,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;YACtF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,qBAAY,EAAC,gBAAgB,CAAC,CAAC;YACnD,MAAM,YAAY,GAAG,EAAE,CAAC;YAExB,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CACP,yFAAyF,EACzF,GAAG,EAAE,CAAC,kBAAY,CAAC,IAAI,CAAC,YAAY,CAAC,EACrC,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAC1B,SAAS,EACT,EAAE,QAAQ,EAAE,UAAU,EAAE,EACxB;gBACE,SAAS,EAAE,WAAW;gBACtB,OAAO,EAAE,GAAG;aACb,CACF,CAAC;YAEF,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAE3C,IAAA,eAAM,EAAC,4BAAK,CAAC,CAAC,cAAc,CAC1B;gBACE,GAAG,EAAE,uHAAuH;gBAC5H,IAAI,EAAE,6CAA6C;gBACnD,IAAI,EAAE,EAAE;aACT,EACD,gCAAgC,EAChC;gBACE,SAAS,EAAE,KAAK;gBAChB,qBAAqB,EAAE,KAAK;gBAC5B,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,WAAW;aACvB,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import type * as ChainWebNodeClient from '@kadena/chainweb-node-client';\nimport { local } from '@kadena/chainweb-node-client';\nimport type { ChainId } from '@kadena/types';\nimport { http, HttpResponse } from 'msw';\nimport { setupServer } from 'msw/node';\nimport {\n  afterAll,\n  afterEach,\n  beforeAll,\n  describe,\n  expect,\n  it,\n  vi,\n} from 'vitest';\nimport { createClient } from '../client';\n\n// Hack to spy on exported function\nvi.mock('@kadena/chainweb-node-client', async (importOriginal) => {\n  const mod: typeof ChainWebNodeClient = await importOriginal();\n  const local = vi.fn().mockImplementation(mod.local);\n  return { ...mod, local };\n});\n\nconst server = setupServer();\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\nconst post = (\n  path: string,\n  response: string | Record<string, unknown>,\n  status = 200,\n): ReturnType<typeof http.post> =>\n  http.post(\n    path,\n    () =>\n      typeof response === 'string'\n        ? new HttpResponse(response, { status })\n        : HttpResponse.json(response, { status }),\n    { once: true },\n  );\n\nconst hostApiGenerator = ({\n  networkId,\n  chainId,\n}: {\n  networkId: string;\n  chainId: ChainId;\n}): string => `http://example.org/${networkId}/${chainId}`;\n\ndescribe('client', () => {\n  it('uses the string input as the host for all requests', async () => {\n    const response = { reqKey: 'test-key' };\n\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/local', response),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const { local } = createClient(hostUrl);\n\n    const body = {\n      cmd: JSON.stringify({ networkId: 'mainnet01', meta: { chainId: '1' } }),\n      hash: 'hash',\n      sigs: [{ sig: 'test-sig' }],\n    };\n\n    const result = await local(body);\n\n    expect(result).toEqual(response);\n  });\n\n  it('uses kadenaHostGenerator if called without argument', async () => {\n    server.resetHandlers(\n      post(\n        'https://api.chainweb.com/chainweb/0.0/mainnet01/chain/1/pact/api/v1/local',\n        { reqKey: 'test-key' },\n      ),\n    );\n\n    const { local } = createClient();\n\n    const networkId = 'mainnet01';\n    const chainId = '1';\n\n    const body = {\n      cmd: JSON.stringify({ networkId, meta: { chainId } }),\n      hash: 'hash',\n      sigs: [{ sig: 'test-sig' }],\n    };\n\n    await local(body);\n  });\n\n  it(\"uses confirmationDepth that is passed as an argument to the 'client' function for the applicable places\", async () => {\n    server.resetHandlers(\n      http.post('http://example.org/mainnet01/1/api/v1/poll', ({ request }) => {\n        const url = new URL(request.url);\n        return HttpResponse.json({\n          'test-key': {\n            reqKey: 'test-key',\n            confirmationDepth: url.searchParams.get('confirmationDepth'),\n          },\n        });\n      }),\n    );\n\n    const { pollOne } = createClient(hostApiGenerator, {\n      confirmationDepth: 4,\n    });\n\n    const result = await pollOne({\n      requestKey: 'test-key',\n      networkId: 'mainnet01',\n      chainId: '1',\n    });\n\n    expect(result).toEqual({ reqKey: 'test-key', confirmationDepth: '4' });\n  });\n\n  describe('pollOne', () => {\n    it(\"used the confirmationDepth that is passed as an argument to the 'pollOne' function\", async () => {\n      server.resetHandlers(\n        http.post(\n          'http://example.org/mainnet01/1/api/v1/poll',\n          ({ request }) => {\n            const url = new URL(request.url);\n            return HttpResponse.json({\n              'test-key': {\n                reqKey: 'test-key',\n                confirmationDepth: url.searchParams.get('confirmationDepth'),\n              },\n            });\n          },\n        ),\n      );\n\n      const { pollOne } = createClient(hostApiGenerator, {\n        confirmationDepth: 4,\n      });\n\n      const result = await pollOne(\n        {\n          requestKey: 'test-key',\n          networkId: 'mainnet01',\n          chainId: '1',\n        },\n        {\n          confirmationDepth: 5,\n        },\n      );\n      expect(result).toEqual({ reqKey: 'test-key', confirmationDepth: '5' });\n    });\n  });\n\n  describe('local', () => {\n    it('uses the hostApiGenerator function to generate hostUrl for local request', async () => {\n      const expectedResponse = {\n        reqKey: 'test-key',\n        result: 'test-result',\n      };\n      server.resetHandlers(\n        post('http://example.org/mainnet01/1/api/v1/local', expectedResponse),\n      );\n\n      const { local } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      const res = await local(body);\n      expect(res).toEqual(expectedResponse);\n    });\n  });\n\n  describe('submit', () => {\n    it('uses the hostApiGenerator function to generate hostUrl for submit request', async () => {\n      server.resetHandlers(\n        post('http://example.org/mainnet01/1/api/v1/send', {\n          requestKeys: ['test-key'],\n        }),\n      );\n\n      const { submit } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      const transactionDescriptor = await submit(body);\n\n      expect(transactionDescriptor).toEqual({\n        requestKey: 'test-key',\n        chainId: '1',\n        networkId: 'mainnet01',\n      });\n    });\n\n    it('returns requestKey if input is a single command', async () => {\n      server.resetHandlers(\n        post('http://example.org/mainnet01/1/api/v1/send', {\n          requestKeys: ['test-key'],\n        }),\n      );\n\n      const { submit } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      const transactionDescriptor = await submit(body);\n\n      expect(transactionDescriptor).toEqual({\n        requestKey: 'test-key',\n        chainId: '1',\n        networkId: 'mainnet01',\n      });\n    });\n\n    it('returns requestKeys if input is an array', async () => {\n      server.resetHandlers(\n        post('http://example.org/mainnet01/1/api/v1/send', {\n          requestKeys: ['test-key'],\n        }),\n      );\n\n      const { submit } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      const transactionDescriptors = await submit([body]);\n\n      expect(transactionDescriptors).toEqual([\n        { requestKey: 'test-key', chainId: '1', networkId: 'mainnet01' },\n      ]);\n    });\n\n    it('throes an error if the command list is empty', async () => {\n      const { submit } = createClient(() => 'http://test-host.com');\n      await expect(submit([])).rejects.toThrowError(\n        new Error('EMPTY_COMMAND_LIST'),\n      );\n    });\n  });\n\n  describe('pollStatus', () => {\n    it('calls /poll endpoint several times to get the final status of the request', async () => {\n      const response = [\n        // first /poll\n        {},\n        // second /poll\n        {},\n        // third /poll\n        { 'test-key': { reqKey: 'test-key' } },\n      ];\n\n      server.resetHandlers(\n        post('http://example.org/mainnet01/1/api/v1/send', {\n          requestKeys: ['test-key'],\n        }),\n        post('http://example.org/mainnet01/1/api/v1/poll', response[0]),\n        post('http://example.org/mainnet01/1/api/v1/poll', response[1]),\n        post('http://example.org/mainnet01/1/api/v1/poll', response[2]),\n      );\n\n      const { submit, pollStatus } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      const transactionDescriptor = await submit(body);\n\n      expect(transactionDescriptor.requestKey).toEqual('test-key');\n\n      const result = await pollStatus(transactionDescriptor, {\n        interval: 10,\n      });\n\n      expect(result).toEqual(response[2]);\n    });\n\n    it('returns a list if input is list of requests', async () => {\n      const response = [\n        // first /poll\n        {},\n        // second /poll\n        {},\n        // third /poll\n        {\n          'test-key-1': { reqKey: 'test-key-1' },\n          'test-key-2': { reqKey: 'test-key-2' },\n        },\n      ];\n\n      server.resetHandlers(\n        post('http://example.org/mainnet01/1/api/v1/send', {\n          requestKeys: ['test-key-1', 'test-key-2'],\n        }),\n        post('http://example.org/mainnet01/1/api/v1/poll', response[0]),\n        post('http://example.org/mainnet01/1/api/v1/poll', response[1]),\n        post('http://example.org/mainnet01/1/api/v1/poll', response[2]),\n      );\n\n      const { submit, pollStatus } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = [\n        {\n          cmd: JSON.stringify({ networkId, meta: { chainId } }),\n          hash: 'hash',\n          sigs: [{ sig: 'test-sig' }],\n        },\n        {\n          cmd: JSON.stringify({ networkId, meta: { chainId } }),\n          hash: 'hash',\n          sigs: [{ sig: 'test-sig' }],\n        },\n      ];\n\n      const transactionDescriptor = await submit(body);\n\n      expect(transactionDescriptor.length).toEqual(2);\n\n      const result = await pollStatus(transactionDescriptor, {\n        interval: 10,\n      });\n\n      expect(result).toEqual(response[2]);\n    });\n  });\n\n  describe('getStatus', () => {\n    it('calls /poll endpoint once to get the status of the request', async () => {\n      server.resetHandlers(\n        post(\n          'https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/api/v1/poll',\n          {},\n        ),\n      );\n\n      const { getStatus } = createClient();\n\n      const result = await getStatus({\n        requestKey: 'test-key',\n        chainId: '0',\n        networkId: 'testnet04',\n      });\n\n      expect(result).toEqual({});\n    });\n\n    it('calls /poll endpoint once to get the status of the list of requests ', async () => {\n      server.resetHandlers(\n        post(\n          'https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/api/v1/poll',\n          { 'test-key-1': 'result1', 'test-key-2': 'result2' },\n        ),\n      );\n\n      const { getStatus } = createClient();\n\n      const result = await getStatus([\n        {\n          requestKey: 'test-key-1',\n          chainId: '0',\n          networkId: 'testnet04',\n        },\n        { requestKey: 'test-key-2', chainId: '0', networkId: 'testnet04' },\n      ]);\n\n      expect(result).toEqual({\n        'test-key-1': 'result1',\n        'test-key-2': 'result2',\n      });\n    });\n  });\n\n  describe('listen', () => {\n    it('calls /listen endpoint get the status of the request', async () => {\n      server.resetHandlers(\n        post(\n          'https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/api/v1/listen',\n          { reqKey: 'test-key' },\n        ),\n      );\n\n      const { listen } = createClient();\n\n      const result = await listen({\n        requestKey: 'test-key',\n        chainId: '0',\n        networkId: 'testnet04',\n      });\n\n      expect(result).toEqual({ reqKey: 'test-key' });\n    });\n  });\n\n  describe('getSpv', () => {\n    it('calls /spv endpoint once to get spv proof', async () => {\n      server.resetHandlers(\n        post(\n          'https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/spv',\n          'proof',\n        ),\n      );\n\n      const { createSpv: getSpv } = createClient();\n\n      const result = await getSpv(\n        {\n          requestKey: 'test-key',\n          chainId: '0',\n          networkId: 'testnet04',\n        },\n        '2',\n      );\n\n      expect(result).toEqual('proof');\n    });\n  });\n\n  describe('pollSpv', () => {\n    it('calls /spv endpoint once to get spv proof', async () => {\n      server.resetHandlers(\n        post('http://test-host.com/spv', 'not found', 404),\n        post('http://test-host.com/spv', 'not found', 404),\n        post('http://test-host.com/spv', 'not found', 404),\n        post('http://test-host.com/spv', 'proof'),\n      );\n\n      const { pollCreateSpv: pollSpv } = createClient('http://test-host.com');\n\n      const result = await pollSpv(\n        {\n          requestKey: 'test-key',\n          chainId: '0',\n          networkId: 'testnet04',\n        },\n        '2',\n        { interval: 10 },\n      );\n\n      expect(result).toEqual('proof');\n    });\n  });\n\n  describe('preflight', () => {\n    it('uses local request and add preflight=true and signatureVerification=true', async () => {\n      const expectedResponse = {\n        reqKey: 'test-key',\n        result: 'test-result',\n      };\n      server.resetHandlers(\n        post(\n          'http://example.org/mainnet01/1/api/v1/local?preflight=true&signatureVerification=true',\n          expectedResponse,\n        ),\n      );\n\n      const { preflight } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      expect(await preflight(body)).toEqual(expectedResponse);\n    });\n  });\n  describe('signatureVerification', () => {\n    it('uses local request and add preflight=false and signatureVerification=true', async () => {\n      const expectedResponse = {\n        reqKey: 'test-key',\n        result: 'test-result',\n      };\n      server.resetHandlers(\n        post(\n          'http://example.org/mainnet01/1/api/v1/local?preflight=false&signatureVerification=true',\n          expectedResponse,\n        ),\n      );\n\n      const { signatureVerification } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      expect(await signatureVerification(body)).toEqual(expectedResponse);\n    });\n  });\n\n  describe('dirtyRead', () => {\n    it('uses local request and add preflight=false and signatureVerification=false', async () => {\n      const expectedResponse = {\n        reqKey: 'test-key',\n        result: 'test-result',\n      };\n      server.resetHandlers(\n        post(\n          'http://example.org/mainnet01/1/api/v1/local?preflight=false&signatureVerification=false',\n          expectedResponse,\n        ),\n      );\n\n      const { dirtyRead } = createClient(hostApiGenerator);\n\n      const networkId = 'mainnet01';\n      const chainId = '1';\n\n      const body = {\n        cmd: JSON.stringify({ networkId, meta: { chainId } }),\n        hash: 'hash',\n        sigs: [{ sig: 'test-sig' }],\n      };\n\n      expect(await dirtyRead(body)).toEqual(expectedResponse);\n    });\n  });\n\n  describe('runPact', () => {\n    beforeAll(() => {\n      vi.useFakeTimers().setSystemTime(new Date('2023-07-31'));\n    });\n\n    afterAll(() => {\n      vi.useRealTimers();\n    });\n\n    it('create a complete pact command from the input and send it to the chain', async () => {\n      const { runPact } = createClient(hostApiGenerator);\n      const mockResponse = {};\n\n      server.resetHandlers(\n        http.post(\n          'http://example.org/mainnet01/1/api/v1/local?preflight=false&signatureVerification=false',\n          () => HttpResponse.json(mockResponse),\n          { once: true },\n        ),\n      );\n\n      const result = await runPact(\n        '(+ 1 1)',\n        { testData: 'testData' },\n        {\n          networkId: 'mainnet01',\n          chainId: '1',\n        },\n      );\n\n      expect(result).toStrictEqual(mockResponse);\n\n      expect(local).toBeCalledWith(\n        {\n          cmd: '{\"payload\":{\"exec\":{\"code\":\"(+ 1 1)\",\"data\":{\"testData\":\"testData\"}}},\"nonce\":\"kjs:nonce:1690761600000\",\"signers\":[]}',\n          hash: '4KHg5lsf4zxOXsaqbvNIJlVPKXDtuzi3xiSlRUnqBJQ',\n          sigs: [],\n        },\n        'http://example.org/mainnet01/1',\n        {\n          preflight: false,\n          signatureVerification: false,\n          chainId: '1',\n          networkId: 'mainnet01',\n        },\n      );\n    });\n  });\n});\n"]}