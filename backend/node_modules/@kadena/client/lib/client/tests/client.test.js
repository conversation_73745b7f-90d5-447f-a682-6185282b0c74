"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const chainweb_node_client_1 = require("@kadena/chainweb-node-client");
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const client_1 = require("../client");
// Hack to spy on exported function
vitest_1.vi.mock('@kadena/chainweb-node-client', async (importOriginal) => {
    const mod = await importOriginal();
    const local = vitest_1.vi.fn().mockImplementation(mod.local);
    return { ...mod, local };
});
const server = (0, node_1.setupServer)();
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
const post = (path, response, status = 200) => msw_1.http.post(path, () => typeof response === 'string'
    ? new msw_1.HttpResponse(response, { status })
    : msw_1.HttpResponse.json(response, { status }), { once: true });
const hostApiGenerator = ({ networkId, chainId, }) => `http://example.org/${networkId}/${chainId}`;
(0, vitest_1.describe)('client', () => {
    (0, vitest_1.it)('uses the string input as the host for all requests', async () => {
        const response = { reqKey: 'test-key' };
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/local', response));
        const hostUrl = 'http://test-blockchain-host.com';
        const { local } = (0, client_1.createClient)(hostUrl);
        const body = {
            cmd: JSON.stringify({ networkId: 'mainnet01', meta: { chainId: '1' } }),
            hash: 'hash',
            sigs: [{ sig: 'test-sig' }],
        };
        const result = await local(body);
        (0, vitest_1.expect)(result).toEqual(response);
    });
    (0, vitest_1.it)('uses kadenaHostGenerator if called without argument', async () => {
        server.resetHandlers(post('https://api.chainweb.com/chainweb/0.0/mainnet01/chain/1/pact/api/v1/local', { reqKey: 'test-key' }));
        const { local } = (0, client_1.createClient)();
        const networkId = 'mainnet01';
        const chainId = '1';
        const body = {
            cmd: JSON.stringify({ networkId, meta: { chainId } }),
            hash: 'hash',
            sigs: [{ sig: 'test-sig' }],
        };
        await local(body);
    });
    (0, vitest_1.it)("uses confirmationDepth that is passed as an argument to the 'client' function for the applicable places", async () => {
        server.resetHandlers(msw_1.http.post('http://example.org/mainnet01/1/api/v1/poll', ({ request }) => {
            const url = new URL(request.url);
            return msw_1.HttpResponse.json({
                'test-key': {
                    reqKey: 'test-key',
                    confirmationDepth: url.searchParams.get('confirmationDepth'),
                },
            });
        }));
        const { pollOne } = (0, client_1.createClient)(hostApiGenerator, {
            confirmationDepth: 4,
        });
        const result = await pollOne({
            requestKey: 'test-key',
            networkId: 'mainnet01',
            chainId: '1',
        });
        (0, vitest_1.expect)(result).toEqual({ reqKey: 'test-key', confirmationDepth: '4' });
    });
    (0, vitest_1.describe)('pollOne', () => {
        (0, vitest_1.it)("used the confirmationDepth that is passed as an argument to the 'pollOne' function", async () => {
            server.resetHandlers(msw_1.http.post('http://example.org/mainnet01/1/api/v1/poll', ({ request }) => {
                const url = new URL(request.url);
                return msw_1.HttpResponse.json({
                    'test-key': {
                        reqKey: 'test-key',
                        confirmationDepth: url.searchParams.get('confirmationDepth'),
                    },
                });
            }));
            const { pollOne } = (0, client_1.createClient)(hostApiGenerator, {
                confirmationDepth: 4,
            });
            const result = await pollOne({
                requestKey: 'test-key',
                networkId: 'mainnet01',
                chainId: '1',
            }, {
                confirmationDepth: 5,
            });
            (0, vitest_1.expect)(result).toEqual({ reqKey: 'test-key', confirmationDepth: '5' });
        });
    });
    (0, vitest_1.describe)('local', () => {
        (0, vitest_1.it)('uses the hostApiGenerator function to generate hostUrl for local request', async () => {
            const expectedResponse = {
                reqKey: 'test-key',
                result: 'test-result',
            };
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/local', expectedResponse));
            const { local } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            const res = await local(body);
            (0, vitest_1.expect)(res).toEqual(expectedResponse);
        });
    });
    (0, vitest_1.describe)('submit', () => {
        (0, vitest_1.it)('uses the hostApiGenerator function to generate hostUrl for submit request', async () => {
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/send', {
                requestKeys: ['test-key'],
            }));
            const { submit } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            const transactionDescriptor = await submit(body);
            (0, vitest_1.expect)(transactionDescriptor).toEqual({
                requestKey: 'test-key',
                chainId: '1',
                networkId: 'mainnet01',
            });
        });
        (0, vitest_1.it)('returns requestKey if input is a single command', async () => {
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/send', {
                requestKeys: ['test-key'],
            }));
            const { submit } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            const transactionDescriptor = await submit(body);
            (0, vitest_1.expect)(transactionDescriptor).toEqual({
                requestKey: 'test-key',
                chainId: '1',
                networkId: 'mainnet01',
            });
        });
        (0, vitest_1.it)('returns requestKeys if input is an array', async () => {
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/send', {
                requestKeys: ['test-key'],
            }));
            const { submit } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            const transactionDescriptors = await submit([body]);
            (0, vitest_1.expect)(transactionDescriptors).toEqual([
                { requestKey: 'test-key', chainId: '1', networkId: 'mainnet01' },
            ]);
        });
        (0, vitest_1.it)('throes an error if the command list is empty', async () => {
            const { submit } = (0, client_1.createClient)(() => 'http://test-host.com');
            await (0, vitest_1.expect)(submit([])).rejects.toThrowError(new Error('EMPTY_COMMAND_LIST'));
        });
    });
    (0, vitest_1.describe)('pollStatus', () => {
        (0, vitest_1.it)('calls /poll endpoint several times to get the final status of the request', async () => {
            const response = [
                // first /poll
                {},
                // second /poll
                {},
                // third /poll
                { 'test-key': { reqKey: 'test-key' } },
            ];
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/send', {
                requestKeys: ['test-key'],
            }), post('http://example.org/mainnet01/1/api/v1/poll', response[0]), post('http://example.org/mainnet01/1/api/v1/poll', response[1]), post('http://example.org/mainnet01/1/api/v1/poll', response[2]));
            const { submit, pollStatus } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            const transactionDescriptor = await submit(body);
            (0, vitest_1.expect)(transactionDescriptor.requestKey).toEqual('test-key');
            const result = await pollStatus(transactionDescriptor, {
                interval: 10,
            });
            (0, vitest_1.expect)(result).toEqual(response[2]);
        });
        (0, vitest_1.it)('returns a list if input is list of requests', async () => {
            const response = [
                // first /poll
                {},
                // second /poll
                {},
                // third /poll
                {
                    'test-key-1': { reqKey: 'test-key-1' },
                    'test-key-2': { reqKey: 'test-key-2' },
                },
            ];
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/send', {
                requestKeys: ['test-key-1', 'test-key-2'],
            }), post('http://example.org/mainnet01/1/api/v1/poll', response[0]), post('http://example.org/mainnet01/1/api/v1/poll', response[1]), post('http://example.org/mainnet01/1/api/v1/poll', response[2]));
            const { submit, pollStatus } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = [
                {
                    cmd: JSON.stringify({ networkId, meta: { chainId } }),
                    hash: 'hash',
                    sigs: [{ sig: 'test-sig' }],
                },
                {
                    cmd: JSON.stringify({ networkId, meta: { chainId } }),
                    hash: 'hash',
                    sigs: [{ sig: 'test-sig' }],
                },
            ];
            const transactionDescriptor = await submit(body);
            (0, vitest_1.expect)(transactionDescriptor.length).toEqual(2);
            const result = await pollStatus(transactionDescriptor, {
                interval: 10,
            });
            (0, vitest_1.expect)(result).toEqual(response[2]);
        });
    });
    (0, vitest_1.describe)('getStatus', () => {
        (0, vitest_1.it)('calls /poll endpoint once to get the status of the request', async () => {
            server.resetHandlers(post('https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/api/v1/poll', {}));
            const { getStatus } = (0, client_1.createClient)();
            const result = await getStatus({
                requestKey: 'test-key',
                chainId: '0',
                networkId: 'testnet04',
            });
            (0, vitest_1.expect)(result).toEqual({});
        });
        (0, vitest_1.it)('calls /poll endpoint once to get the status of the list of requests ', async () => {
            server.resetHandlers(post('https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/api/v1/poll', { 'test-key-1': 'result1', 'test-key-2': 'result2' }));
            const { getStatus } = (0, client_1.createClient)();
            const result = await getStatus([
                {
                    requestKey: 'test-key-1',
                    chainId: '0',
                    networkId: 'testnet04',
                },
                { requestKey: 'test-key-2', chainId: '0', networkId: 'testnet04' },
            ]);
            (0, vitest_1.expect)(result).toEqual({
                'test-key-1': 'result1',
                'test-key-2': 'result2',
            });
        });
    });
    (0, vitest_1.describe)('listen', () => {
        (0, vitest_1.it)('calls /listen endpoint get the status of the request', async () => {
            server.resetHandlers(post('https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/api/v1/listen', { reqKey: 'test-key' }));
            const { listen } = (0, client_1.createClient)();
            const result = await listen({
                requestKey: 'test-key',
                chainId: '0',
                networkId: 'testnet04',
            });
            (0, vitest_1.expect)(result).toEqual({ reqKey: 'test-key' });
        });
    });
    (0, vitest_1.describe)('getSpv', () => {
        (0, vitest_1.it)('calls /spv endpoint once to get spv proof', async () => {
            server.resetHandlers(post('https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/0/pact/spv', 'proof'));
            const { createSpv: getSpv } = (0, client_1.createClient)();
            const result = await getSpv({
                requestKey: 'test-key',
                chainId: '0',
                networkId: 'testnet04',
            }, '2');
            (0, vitest_1.expect)(result).toEqual('proof');
        });
    });
    (0, vitest_1.describe)('pollSpv', () => {
        (0, vitest_1.it)('calls /spv endpoint once to get spv proof', async () => {
            server.resetHandlers(post('http://test-host.com/spv', 'not found', 404), post('http://test-host.com/spv', 'not found', 404), post('http://test-host.com/spv', 'not found', 404), post('http://test-host.com/spv', 'proof'));
            const { pollCreateSpv: pollSpv } = (0, client_1.createClient)('http://test-host.com');
            const result = await pollSpv({
                requestKey: 'test-key',
                chainId: '0',
                networkId: 'testnet04',
            }, '2', { interval: 10 });
            (0, vitest_1.expect)(result).toEqual('proof');
        });
    });
    (0, vitest_1.describe)('preflight', () => {
        (0, vitest_1.it)('uses local request and add preflight=true and signatureVerification=true', async () => {
            const expectedResponse = {
                reqKey: 'test-key',
                result: 'test-result',
            };
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/local?preflight=true&signatureVerification=true', expectedResponse));
            const { preflight } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            (0, vitest_1.expect)(await preflight(body)).toEqual(expectedResponse);
        });
    });
    (0, vitest_1.describe)('signatureVerification', () => {
        (0, vitest_1.it)('uses local request and add preflight=false and signatureVerification=true', async () => {
            const expectedResponse = {
                reqKey: 'test-key',
                result: 'test-result',
            };
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/local?preflight=false&signatureVerification=true', expectedResponse));
            const { signatureVerification } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            (0, vitest_1.expect)(await signatureVerification(body)).toEqual(expectedResponse);
        });
    });
    (0, vitest_1.describe)('dirtyRead', () => {
        (0, vitest_1.it)('uses local request and add preflight=false and signatureVerification=false', async () => {
            const expectedResponse = {
                reqKey: 'test-key',
                result: 'test-result',
            };
            server.resetHandlers(post('http://example.org/mainnet01/1/api/v1/local?preflight=false&signatureVerification=false', expectedResponse));
            const { dirtyRead } = (0, client_1.createClient)(hostApiGenerator);
            const networkId = 'mainnet01';
            const chainId = '1';
            const body = {
                cmd: JSON.stringify({ networkId, meta: { chainId } }),
                hash: 'hash',
                sigs: [{ sig: 'test-sig' }],
            };
            (0, vitest_1.expect)(await dirtyRead(body)).toEqual(expectedResponse);
        });
    });
    (0, vitest_1.describe)('runPact', () => {
        (0, vitest_1.beforeAll)(() => {
            vitest_1.vi.useFakeTimers().setSystemTime(new Date('2023-07-31'));
        });
        (0, vitest_1.afterAll)(() => {
            vitest_1.vi.useRealTimers();
        });
        (0, vitest_1.it)('create a complete pact command from the input and send it to the chain', async () => {
            const { runPact } = (0, client_1.createClient)(hostApiGenerator);
            const mockResponse = {};
            server.resetHandlers(msw_1.http.post('http://example.org/mainnet01/1/api/v1/local?preflight=false&signatureVerification=false', () => msw_1.HttpResponse.json(mockResponse), { once: true }));
            const result = await runPact('(+ 1 1)', { testData: 'testData' }, {
                networkId: 'mainnet01',
                chainId: '1',
            });
            (0, vitest_1.expect)(result).toStrictEqual(mockResponse);
            (0, vitest_1.expect)(chainweb_node_client_1.local).toBeCalledWith({
                cmd: '{"payload":{"exec":{"code":"(+ 1 1)","data":{"testData":"testData"}}},"nonce":"kjs:nonce:1690761600000","signers":[]}',
                hash: '4KHg5lsf4zxOXsaqbvNIJlVPKXDtuzi3xiSlRUnqBJQ',
                sigs: [],
            }, 'http://example.org/mainnet01/1', {
                preflight: false,
                signatureVerification: false,
                chainId: '1',
                networkId: 'mainnet01',
            });
        });
    });
});
//# sourceMappingURL=client.test.js.map