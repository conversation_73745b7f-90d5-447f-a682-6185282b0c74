{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../src/client/client.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,aAAa,EACd,MAAM,8BAA8B,CAAC;AAEtC,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAKzE,OAAO,KAAK,EACV,eAAe,EACf,YAAY,EACZ,mBAAmB,EACpB,MAAM,yBAAyB,CAAC;AASjC;;;;;GAKG;AACH,MAAM,WAAW,sBAAsB;IACrC,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,OAAO;IACtB;;;;;;;;OAQG;IACH,CACE,WAAW,EAAE,QAAQ,EACrB,OAAO,CAAC,EAAE,iBAAiB,GAC1B,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAEnC;;;;;;;;OAQG;IACH,CACE,eAAe,EAAE,QAAQ,EAAE,EAC3B,OAAO,CAAC,EAAE,iBAAiB,GAC1B,OAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;;;;;;;;;OAWG;IACH,KAAK,EAAE,CAAC,CAAC,SAAS,aAAa,EAC7B,WAAW,EAAE,gBAAgB,EAC7B,OAAO,CAAC,EAAE,CAAC,KACR,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAE/B;;;;;;;;OAQG;IACH,MAAM,EAAE,OAAO,CAAC;IAEhB;;;;;;;OAOG;IACH,UAAU,EAAE,CACV,sBAAsB,EAAE,sBAAsB,EAAE,GAAG,sBAAsB,EACzE,OAAO,CAAC,EAAE,YAAY,KACnB,mBAAmB,CAAC,cAAc,CAAC,CAAC;IAEzC;;;;;;;OAOG;IACH,SAAS,EAAE,CACT,sBAAsB,EAAE,sBAAsB,EAAE,GAAG,sBAAsB,EACzE,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,aAAa,CAAC,CAAC;IAE5B;;;;;;;OAOG;IACH,MAAM,EAAE,CACN,qBAAqB,EAAE,sBAAsB,EAC7C,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,cAAc,CAAC,CAAC;IAE7B;;;;;;;;;OASG;IACH,aAAa,EAAE,CACb,qBAAqB,EAAE,sBAAsB,EAC7C,aAAa,EAAE,OAAO,EACtB,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC,MAAM,CAAC,CAAC;IAErB;;;;;;;;OAQG;IACH,SAAS,EAAE,CACT,qBAAqB,EAAE,sBAAsB,EAC7C,aAAa,EAAE,OAAO,EACtB,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,MAAM,CAAC,CAAC;CACtB;AAED;;;GAGG;AACH,MAAM,WAAW,OAAQ,SAAQ,WAAW;IAC1C;;;OAGG;IACH,SAAS,EAAE,CACT,WAAW,EAAE,QAAQ,GAAG,gBAAgB,EACxC,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAElC;;;;;OAKG;IACH,qBAAqB,EAAE,CACrB,WAAW,EAAE,QAAQ,EACrB,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,cAAc,CAAC,CAAC;IAE7B;;;;;;OAMG;IACH,SAAS,EAAE,CACT,WAAW,EAAE,gBAAgB,EAC7B,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,cAAc,CAAC,CAAC;IAE7B;;;;OAIG;IACH,OAAO,EAAE,CACP,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC7B,MAAM,EAAE,iBAAiB,GAAG,eAAe,KACxC,OAAO,CAAC,cAAc,CAAC,CAAC;IAE7B;;;;;OAKG;IACH,IAAI,EAAE,OAAO,CAAC;IAEd;;;OAGG;IACH,SAAS,EAAE,CACT,WAAW,EAAE,QAAQ,EACrB,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAErC;;;;;OAKG;IACH,OAAO,EAAE,CACP,sBAAsB,EAAE,sBAAsB,EAAE,GAAG,sBAAsB,EACzE,OAAO,CAAC,EAAE,iBAAiB,KACxB,OAAO,CAAC,aAAa,CAAC,CAAC;IAE5B;;;;;;;;OAQG;IACH,OAAO,EAAE,CACP,qBAAqB,EAAE,sBAAsB,EAC7C,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC,cAAc,CAAC,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B;;;;;;OAMG;IACH,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE;QAAE,iBAAiB,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC;IAEtE;;;;;;OAMG;IACH,CACE,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE;QAC/B,OAAO,EAAE,OAAO,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,IAAI,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;KACrD,KAAK,MAAM,GAAG;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,WAAW,EAAE,iBAAiB,CAAA;KAAE,EAClE,QAAQ,CAAC,EAAE;QAAE,iBAAiB,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC;CACZ;AAYD;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,aAgM1B,CAAC"}