{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/client/utils/utils.ts"], "names": [], "mappings": ";;;AAMO,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC;IAC5C,OAAO,EAAE;QACP,cAAc,EAAE,kBAA2B;KAC5C;IACD,MAAM,EAAE,MAAe;IACvB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;CAC3B,CAAC,CAAC;AANU,QAAA,WAAW,eAMrB;AAEH,SAAgB,MAAM,CACpB,IAAY,EACZ,QAAgB,EAChB,MAAe;IAEf,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,MAAM,MAAM,GAAG,GAAG,SAAS,GACzB,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,EACpD,EAAE,CAAC;IACH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;IAE5B,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1C,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACxB,CAAC;AApBD,wBAoBC;AAED;;;;;;;;GAQG;AAEI,MAAM,UAAU,GAAG,CAAC,WAAmB,EAAE,EAAE;IAChD,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC;QACpC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,WAAW,CAAC;IAChB,OAAO,CAAC,EAAE,SAAS,EAAE,OAAO,EAAmB,EAAE,EAAE,CACjD,GAAG,IAAI,iBAAiB,SAAS,UAAU,OAAO,OAAO,CAAC;AAC9D,CAAC,CAAC;AANW,QAAA,UAAU,cAMrB;AAEK,MAAM,mBAAmB,GAAG,CAAC,EAClC,SAAS,EACT,OAAO,GACS,EAAU,EAAE;IAC5B,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,WAAW;YACd,OAAO,IAAA,kBAAU,EAAC,0BAA0B,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;QACxE,KAAK,WAAW;YACd,OAAO,IAAA,kBAAU,EAAC,kCAAkC,CAAC,CAAC;gBACpD,SAAS;gBACT,OAAO;aACR,CAAC,CAAC;QACL,KAAK,WAAW;YACd,OAAO,IAAA,kBAAU,EAAC,oCAAoC,CAAC,CAAC;gBACtD,SAAS;gBACT,OAAO;aACR,CAAC,CAAC;QACL;YACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,SAAS,EAAE,CAAC,CAAC;IACxD,CAAC;AACH,CAAC,CAAC;AApBW,QAAA,mBAAmB,uBAoB9B;AAUK,MAAM,UAAU,GAAG,GAAsB,EAAE;IAChD,IAAI,SAAS,GAAuB,GAAG,EAAE,GAAE,CAAC,CAAC;IAC7C,IAAI,QAAQ,GAA6B,GAAG,EAAE,GAAE,CAAC,CAAC;IAClD,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,MAAqB,CAAC;IAE1B,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjD,SAAS,GAAG,CAAC,IAAO,EAAE,EAAE;YACtB,MAAM,GAAG,IAAI,CAAC;YACd,SAAS,GAAG,IAAI,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC;QACF,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE;YACjB,SAAS,GAAG,IAAI,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,OAAO;QACP,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,QAAQ;QAChB,IAAI,SAAS;YACX,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,IAAI,IAAI;YACN,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AA7BW,QAAA,UAAU,cA6BrB;AAEK,MAAM,QAAQ,GAAG,CAAmB,OAAiB,EAAK,EAAE,CACjE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE,EAAO,CAAC,CAAC;AADnD,QAAA,QAAQ,YAC2C;AAEzD,MAAM,2BAA2B,GAAG,CACzC,OAAsC,EACd,EAAE;IAC1B,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAQ,CAAC,EAAE;QACxD,QAAQ,EAAE,OAAO,CAAC,MAAM,CACtB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,EAC7C,EAAgC,CACjC;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AATW,QAAA,2BAA2B,+BAStC;AAEF,8DAA8D;AACvD,MAAM,SAAS,GAAG,CACvB,MAAyB,EACzB,MAAc,EACsB,EAAE,CACtC,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CACjE,CAAC;AANS,QAAA,SAAS,aAMlB;AAEG,MAAM,WAAW,GAAG,CAKzB,EAAK,EAC4B,EAAE;IACnC,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,IAAO,EAAiB,EAAE;QACnC,OAAO,IAAI,CAAC,CAAC;QACb,OAAO,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,WAAW,eAYtB;AAEK,MAAM,KAAK,GAAG,CAAC,QAAgB,EAAiB,EAAE,CACvD,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;AAD7C,QAAA,KAAK,SACwC;AAEnD,MAAM,WAAW,GAAG,CACzB,KAIE,EACqE,EAAE;IACzE,MAAM,MAAM,GAAG,IAAI,GAAG,EAGnB,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE;;QAC3D,MAAM,IAAI,GAAG,MAAA,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,mCAAI,EAAE,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;AAC/B,CAAC,CAAC;AAhBW,QAAA,WAAW,eAgBtB", "sourcesContent": ["import type { ClientRequestInit } from '@kadena/chainweb-node-client';\nimport type {\n  INetworkOptions,\n  IPollRequestPromise,\n} from '../interfaces/interfaces';\n\nexport const jsonRequest = (body: object) => ({\n  headers: {\n    'Content-Type': 'application/json' as const,\n  },\n  method: 'POST' as const,\n  body: JSON.stringify(body),\n});\n\nexport function getUrl(\n  host: string,\n  endpoint: string,\n  params?: object,\n): string {\n  const cleanHost = host.endsWith('/') ? host.slice(0, host.length - 1) : host;\n  const urlStr = `${cleanHost}${\n    endpoint.startsWith('/') ? endpoint : `/${endpoint}`\n  }`;\n  const url = new URL(urlStr);\n\n  if (params !== undefined) {\n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        url.searchParams.append(key, value.toString());\n      }\n    });\n  }\n\n  return url.toString();\n}\n\n/**\n *\n * @public\n * Creates endpoint url based on the baseUrl, networkId and chainId\n *\n * @example\n * const getLocalHostUrl = getHostUrl('http://localhost:8080')\n * const client = createClient(getLocalHostUrl)\n */\n\nexport const getHostUrl = (hostBaseUrl: string) => {\n  const base = hostBaseUrl.endsWith('/')\n    ? hostBaseUrl.slice(0, hostBaseUrl.length - 1)\n    : hostBaseUrl;\n  return ({ networkId, chainId }: INetworkOptions) =>\n    `${base}/chainweb/0.0/${networkId}/chain/${chainId}/pact`;\n};\n\nexport const kadenaHostGenerator = ({\n  networkId,\n  chainId,\n}: INetworkOptions): string => {\n  switch (networkId) {\n    case 'mainnet01':\n      return getHostUrl('https://api.chainweb.com')({ networkId, chainId });\n    case 'testnet04':\n      return getHostUrl('https://api.testnet.chainweb.com')({\n        networkId,\n        chainId,\n      });\n    case 'testnet05':\n      return getHostUrl('https://api.testnet05.chainweb.com')({\n        networkId,\n        chainId,\n      });\n    default:\n      throw new Error(`UNKNOWN_NETWORK_ID: ${networkId}`);\n  }\n};\n\nexport interface IExtPromise<T> {\n  promise: Promise<T>;\n  resolve: (result: T) => void;\n  reject: (err: unknown) => void;\n  fulfilled: boolean;\n  data: T | undefined;\n}\n\nexport const getPromise = <T>(): IExtPromise<T> => {\n  let resolveFn: (value: T) => void = () => {};\n  let rejectFn: (value: unknown) => void = () => {};\n  let fulfilled = false;\n  let result: T | undefined;\n\n  const promise = new Promise<T>((resolve, reject) => {\n    resolveFn = (data: T) => {\n      result = data;\n      fulfilled = true;\n      resolve(data);\n    };\n    rejectFn = (err) => {\n      fulfilled = true;\n      reject(err);\n    };\n  });\n\n  return {\n    promise,\n    resolve: resolveFn,\n    reject: rejectFn,\n    get fulfilled() {\n      return fulfilled;\n    },\n    get data() {\n      return result;\n    },\n  };\n};\n\nexport const mergeAll = <T extends object>(results: Array<T>): T =>\n  results.reduce((acc, data) => ({ ...acc, ...data }), {} as T);\n\nexport const mergeAllPollRequestPromises = <T extends object | string>(\n  results: Array<IPollRequestPromise<T>>,\n): IPollRequestPromise<T> => {\n  return Object.assign(Promise.all(results).then(mergeAll), {\n    requests: results.reduce(\n      (acc, data) => ({ ...acc, ...data.requests }),\n      {} as Record<string, Promise<T>>,\n    ),\n  });\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const mapRecord = <T extends any, Mapper extends (item: T) => any>(\n  object: Record<string, T>,\n  mapper: Mapper,\n): Record<string, ReturnType<Mapper>> =>\n  Object.fromEntries(\n    Object.entries(object).map(([key, data]) => [key, mapper(data)]),\n  );\n\nexport const withCounter = <\n  A extends unknown[],\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  F extends (counter: number, ...args: [...A]) => any,\n>(\n  cb: F,\n): ((...args: A) => ReturnType<F>) => {\n  let counter = 0;\n  return (...args: A): ReturnType<F> => {\n    counter += 1;\n    return cb(counter, ...args);\n  };\n};\n\nexport const sleep = (duration: number): Promise<void> =>\n  new Promise((resolve) => setTimeout(resolve, duration));\n\nexport const groupByHost = (\n  items: Array<{\n    requestKey: string;\n    host: string;\n    requestInit?: ClientRequestInit;\n  }>,\n): [string, { requestInit?: ClientRequestInit; requestKey: string }[]][] => {\n  const byHost = new Map<\n    string,\n    { requestInit?: ClientRequestInit; requestKey: string }[]\n  >();\n  items.forEach(({ host: hostUrl, requestKey, requestInit }) => {\n    const prev = byHost.get(hostUrl) ?? [];\n    byHost.set(hostUrl, [...prev, { requestInit, requestKey }]);\n  });\n  return [...byHost.entries()];\n};\n"]}