{"version": 3, "file": "mergeOptions.js", "sourceRoot": "", "sources": ["../../../src/client/utils/mergeOptions.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,YAAY,CAC1B,KAAQ,EACR,MAAS;IAET,IAAI,CAAC,KAAK;QAAE,OAAO,MAAM,CAAC;IAC1B,IAAI,CAAC,MAAM;QAAE,OAAO,KAAK,CAAC;IAC1B,MAAM,MAAM,GAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC7C,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,OAAO;QACT,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,GAAG;gBACZ,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC3C,GAAI,MAAM,CAAC,GAAG,CAAoB;aACnC,CAAC;YACF,OAAO;QACT,CAAC;QACD,IACE,KAAK,KAAK,IAAI;YACd,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ;YAC/B,OAAO,KAAK,KAAK,QAAQ,EACzB,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CACxB,KAAgC,EAChC,MAAM,CAAC,GAAG,CAA4B,CACvC,CAAC;YACF,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAhCD,oCAgCC", "sourcesContent": ["export function mergeOptions<T extends Record<string, unknown> | undefined>(\n  first: T,\n  second: T,\n): T {\n  if (!first) return second;\n  if (!second) return first;\n  const merged: T = { ...second };\n  Object.entries(first).forEach(([key, value]) => {\n    if (merged[key] === undefined) {\n      merged[key] = value;\n      return;\n    }\n    if (Array.isArray(merged[key])) {\n      merged[key] = [\n        ...(Array.isArray(value) ? value : [value]),\n        ...(merged[key] as Array<unknown>),\n      ];\n      return;\n    }\n    if (\n      value !== null &&\n      typeof merged[key] === 'object' &&\n      typeof value === 'object'\n    ) {\n      merged[key] = mergeOptions(\n        value as Record<string, unknown>,\n        merged[key] as Record<string, unknown>,\n      );\n      return;\n    }\n  });\n  return merged;\n}\n"]}