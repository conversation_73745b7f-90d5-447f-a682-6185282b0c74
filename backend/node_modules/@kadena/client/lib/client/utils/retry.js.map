{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../../src/client/utils/retry.ts"], "names": [], "mappings": ";;;AACA,mCAAgC;AAEhC,MAAM,WAAW,GAAG,CAClB,OAAe,EAIf,EAAE;IACF,IAAI,SAAS,GAAG,GAAS,EAAE,GAAE,CAAC,CAAC;IAC/B,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACpD,MAAM,KAAK,GAAG,UAAU,CACtB,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAC1C,OAAO,CACR,CAAC;QACF,SAAS,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AAC3C,CAAC,CAAC;AAEK,MAAM,KAAK,GAAG,CACnB,IAAsB,EACtB,MAAoB,EACpB,EAAE,CACF,KAAK,UAAU,OAAO,CAAC,OAAsB,EAAE,KAAK,GAAG,CAAC;IACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,MAAM,EAAE,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IACnE,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAEzC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAChC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC9B,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,MAAK,IAAI,EAAE,CAAC;oBAC7B,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC/B,CAAC;gBACD,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC;YACF,WAAW,CAAC,OAAO;YACnB,yDAAyD;YACzD,IAAA,aAAK,EAAC,CAAC,CAAC;iBACL,IAAI,CAAC,IAAI,CAAC;iBACV,OAAO,CAAC,GAAG,EAAE;gBACZ,+CAA+C;gBAC/C,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,CAAC,CAAC;SACL,CAAC,CAAC;QACH,OAAO,MAAW,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IACE,KAAK,KAAK,SAAS;YACnB,CAAC,KAAK,CAAC,OAAO,KAAK,iBAAiB,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,CAAC,EACpE,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,MAAM,IAAA,aAAK,EAAC,QAAQ,CAAC,CAAC;QACtB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC5C,OAAO,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,GAAG,YAAY,EAAE,QAAQ,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IAC3E,CAAC;AACH,CAAC,CAAC;AAxCS,QAAA,KAAK,SAwCd", "sourcesContent": ["import type { IPollOptions } from '../interfaces/interfaces';\nimport { sleep } from './utils';\n\nconst rejectAfter = (\n  timeout: number,\n): {\n  stopTimer: () => void;\n  promise: Promise<void>;\n} => {\n  let stopTimer = (): void => {};\n  const promise = new Promise<void>((resolve, reject) => {\n    const timer = setTimeout(\n      () => reject(new Error('TIME_OUT_REJECT')),\n      timeout,\n    );\n    stopTimer = () => clearTimeout(timer);\n  });\n  return { stopTimer: stopTimer, promise };\n};\n\nexport const retry = <T extends object | string | void | boolean>(\n  task: () => Promise<T>,\n  signal?: AbortSignal,\n) =>\n  async function runTask(options?: IPollOptions, count = 0): Promise<T> {\n    const startTime = Date.now();\n\n    const { timeout = 1000 * 60 * 3, interval = 5000 } = options ?? {};\n    const rejectTimer = rejectAfter(timeout);\n\n    try {\n      const result = await Promise.race([\n        new Promise((resolve, reject) => {\n          if (signal?.aborted === true) {\n            reject(new Error('ABORTED'));\n          }\n          signal?.addEventListener('abort', () => reject(new Error('ABORTED')));\n        }),\n        rejectTimer.promise,\n        // sleep for 1ms to let the timeout promise reject first.\n        sleep(1)\n          .then(task)\n          .finally(() => {\n            // stop the timer if the task already fulfilled\n            rejectTimer.stopTimer();\n          }),\n      ]);\n      return result as T;\n    } catch (error) {\n      if (\n        error !== undefined &&\n        (error.message === 'TIME_OUT_REJECT' || error.message === 'ABORTED')\n      ) {\n        throw error;\n      }\n\n      await sleep(interval);\n      const durationTime = Date.now() - startTime;\n      return runTask({ timeout: timeout - durationTime, interval }, count + 1);\n    }\n  };\n"]}