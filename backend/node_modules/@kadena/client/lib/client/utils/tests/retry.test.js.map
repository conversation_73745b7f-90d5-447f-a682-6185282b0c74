{"version": 3, "file": "retry.test.js", "sourceRoot": "", "sources": ["../../../../src/client/utils/tests/retry.test.ts"], "names": [], "mappings": ";;AAAA,mCAAkD;AAClD,oCAAiC;AACjC,oCAAuC;AAEvC,IAAA,iBAAQ,EAAC,OAAO,EAAE,GAAG,EAAE;IACrB,IAAA,WAAE,EAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;QACvE,MAAM,IAAI,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;QAE5B,MAAM,MAAM,GAAG,MAAM,OAAO,EAAE,CAAC;QAE/B,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;QAC9D,MAAM,IAAI,GAAG,WAAE,CAAC,EAAE,CAChB,IAAA,mBAAW,EAAC,CAAC,GAAG,EAAE,EAAE;YAClB,IAAI,GAAG,KAAK,CAAC;gBAAE,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5C,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1B,CAAC,CAAC,CACH,CAAC;QACF,MAAM,OAAO,GAAG,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;QAE5B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAE9D,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;QACxF,MAAM,IAAI,GAAG,WAAE,CAAC,EAAE,CAChB,IAAA,mBAAW,EAAC,CAAC,GAAG,EAAE,EAAE;YAClB,IAAI,GAAG,KAAK,CAAC;gBAAE,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CACH,CAAC;QACF,MAAM,OAAO,GAAG,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;QAE5B,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAEzD,MAAM,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,MAAM,IAAI,GAAG,WAAE,CAAC,EAAE,CAChB,IAAA,mBAAW,EAAC,CAAC,GAAG,EAAE,EAAE;YAClB,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;gBACd,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CACH,CAAC;QACF,MAAM,OAAO,GAAG,IAAA,aAAK,EAAC,IAAI,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QAEpD,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAExD,MAAM,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;QAC5E,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,eAAe,CAAC,KAAK,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,WAAE,CAAC,EAAE,CAChB,IAAA,mBAAW,EAAC,CAAC,GAAG,EAAE,EAAE;YAClB,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;gBACd,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CACH,CAAC;QACF,MAAM,OAAO,GAAG,IAAA,aAAK,EAAC,IAAI,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QAEpD,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAExD,MAAM,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it, vi } from 'vitest';\nimport { retry } from '../retry';\nimport { withCounter } from '../utils';\n\ndescribe('retry', () => {\n  it('should calls the first try without waiting for interval', async () => {\n    const task = vi.fn().mockResolvedValue(true);\n    const runTask = retry(task);\n\n    const result = await runTask();\n\n    expect(result).toBe(true);\n    expect(task).toBeCalledTimes(1);\n  });\n\n  it('should retry a task till it returns the result', async () => {\n    const task = vi.fn(\n      withCounter((idx) => {\n        if (idx === 5) return Promise.resolve(true);\n        return Promise.reject();\n      }),\n    );\n    const runTask = retry(task);\n\n    const result = await runTask({ interval: 10, timeout: 1000 });\n\n    expect(result).toBe(true);\n    expect(task).toBeCalledTimes(5);\n  });\n\n  it('throws timeout exception if task does not return value after the timeout', async () => {\n    const task = vi.fn(\n      withCounter((idx) => {\n        if (idx === 5) return Promise.resolve(true);\n        return Promise.reject(new Error('its not ready'));\n      }),\n    );\n    const runTask = retry(task);\n\n    const promise = runTask({ interval: 100, timeout: 200 });\n\n    await expect(promise).rejects.toEqual(new Error('TIME_OUT_REJECT'));\n  });\n\n  it('it should abort the process if abortSignal is called', async () => {\n    const abortController = new AbortController();\n    const task = vi.fn(\n      withCounter((idx) => {\n        if (idx === 2) {\n          abortController.abort();\n          return Promise.resolve(true);\n        }\n        return Promise.reject(new Error('its not ready'));\n      }),\n    );\n    const runTask = retry(task, abortController.signal);\n\n    const promise = runTask({ interval: 10, timeout: 200 });\n\n    await expect(promise).rejects.toEqual(new Error('ABORTED'));\n  });\n\n  it('it should abort the process if abortSignal is alredy aborted', async () => {\n    const abortController = new AbortController();\n    abortController.abort();\n    const task = vi.fn(\n      withCounter((idx) => {\n        if (idx === 2) {\n          return Promise.resolve(true);\n        }\n        return Promise.reject(new Error('its not ready'));\n      }),\n    );\n    const runTask = retry(task, abortController.signal);\n\n    const promise = runTask({ interval: 10, timeout: 200 });\n\n    await expect(promise).rejects.toEqual(new Error('ABORTED'));\n  });\n});\n"]}