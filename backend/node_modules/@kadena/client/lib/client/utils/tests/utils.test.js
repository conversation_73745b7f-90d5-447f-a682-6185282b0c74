"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const utils_1 = require("../utils");
(0, vitest_1.describe)('client utils', () => {
    (0, vitest_1.describe)('jsonRequest', () => {
        (0, vitest_1.it)('returns a request object form a json', () => {
            const body = { prop: 'test' };
            const result = (0, utils_1.jsonRequest)(body);
            (0, vitest_1.expect)(result).toEqual({
                headers: { 'Content-Type': 'application/json' },
                method: 'POST',
                body: JSON.stringify(body),
            });
        });
    });
    (0, vitest_1.describe)('getUrl', () => {
        (0, vitest_1.it)('returns a full url from the host and endpoint', () => {
            const host = 'http://host';
            const endpoint = 'api/test';
            (0, vitest_1.expect)((0, utils_1.getUrl)(host, endpoint)).toBe('http://host/api/test');
        });
        (0, vitest_1.it)('skips the back slashed and join two parts only with one back slash', () => {
            const host = 'http://host/';
            const endpoint = '/api/test';
            (0, vitest_1.expect)((0, utils_1.getUrl)(host, endpoint)).toBe('http://host/api/test');
        });
        (0, vitest_1.it)('adds query params if they are presented and not undefined', () => {
            const host = 'http://host/';
            const endpoint = '/api/test';
            const params = {
                first: undefined,
                second: true,
                third: 'test',
            };
            (0, vitest_1.expect)((0, utils_1.getUrl)(host, endpoint, params)).toBe('http://host/api/test?second=true&third=test');
        });
    });
    (0, vitest_1.describe)('kadenaHostGenerator', () => {
        (0, vitest_1.it)('returns mainnet url with the correct chainId', () => {
            (0, vitest_1.expect)((0, utils_1.kadenaHostGenerator)({ networkId: 'mainnet01', chainId: '14' })).toBe('https://api.chainweb.com/chainweb/0.0/mainnet01/chain/14/pact');
        });
        (0, vitest_1.it)('returns testnet url with the correct chainId', () => {
            (0, vitest_1.expect)((0, utils_1.kadenaHostGenerator)({ networkId: 'testnet04', chainId: '14' })).toBe('https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/14/pact');
            (0, vitest_1.expect)((0, utils_1.kadenaHostGenerator)({ networkId: 'testnet05', chainId: '14' })).toBe('https://api.testnet05.chainweb.com/chainweb/0.0/testnet05/chain/14/pact');
        });
        (0, vitest_1.it)('throes exception if networkId is not either mainnet01 nor testnet04 ', () => {
            (0, vitest_1.expect)(() => (0, utils_1.kadenaHostGenerator)({ networkId: 'incorrect-network', chainId: '14' })).toThrowError(Error(`UNKNOWN_NETWORK_ID: incorrect-network`));
        });
    });
    (0, vitest_1.describe)('getHostUrl', () => {
        (0, vitest_1.it)('returns a function that generates host url based on the networkId and chainId', () => {
            const hostUrl = 'http://localhost:8080';
            const getLocalHostUrl = (0, utils_1.getHostUrl)(hostUrl);
            (0, vitest_1.expect)(getLocalHostUrl({ networkId: 'mainnet01', chainId: '14' })).toBe('http://localhost:8080/chainweb/0.0/mainnet01/chain/14/pact');
        });
        (0, vitest_1.it)("removes the last '/' from the host url", () => {
            const hostUrl = 'http://localhost:8080/';
            const getLocalHostUrl = (0, utils_1.getHostUrl)(hostUrl);
            (0, vitest_1.expect)(getLocalHostUrl({ networkId: 'mainnet01', chainId: '14' })).toBe('http://localhost:8080/chainweb/0.0/mainnet01/chain/14/pact');
        });
    });
    (0, vitest_1.describe)('getPromise', () => {
        (0, vitest_1.it)('returns a wrapped object of a promise the resolve and reject function', () => {
            const obj = (0, utils_1.getPromise)();
            (0, vitest_1.expect)(obj.promise).toBeDefined();
            (0, vitest_1.expect)(obj.resolve).toBeDefined();
            (0, vitest_1.expect)(obj.reject).toBeDefined();
        });
        (0, vitest_1.it)('resolves the promise if resolve function is called', () => {
            const pr = (0, utils_1.getPromise)();
            pr.promise
                .then((result) => {
                (0, vitest_1.expect)(result).toBe('result');
            })
                .catch(() => {
                (0, vitest_1.expect)('this should not happen').toBe(true);
            });
            pr.resolve('result');
            return pr;
        });
        (0, vitest_1.it)('rejects the promise if reject function is called', () => {
            const pr = (0, utils_1.getPromise)();
            pr.promise
                .then(() => {
                (0, vitest_1.expect)('this should not happen').toBe(true);
            })
                .catch((result) => {
                (0, vitest_1.expect)(result).toBe('rejected');
            });
            pr.reject('rejected');
            return pr;
        });
        (0, vitest_1.it)('sets fullfiled to true resolve is called', () => {
            const pr = (0, utils_1.getPromise)();
            pr.promise
                .then((result) => {
                (0, vitest_1.expect)(pr.fulfilled).toBe(true);
            })
                .catch(() => {
                (0, vitest_1.expect)('this should not happen').toBe(true);
            });
            pr.resolve('result');
            return pr;
        });
        (0, vitest_1.it)('sets fullfiled to true if reject is called', () => {
            const pr = (0, utils_1.getPromise)();
            pr.promise
                .then(() => { })
                .catch((result) => {
                (0, vitest_1.expect)(pr.fulfilled).toBe(true);
            });
            pr.reject('rejected');
            return pr;
        });
        (0, vitest_1.it)('sets data if resolve is called', () => {
            const pr = (0, utils_1.getPromise)();
            pr.promise
                .then((result) => {
                (0, vitest_1.expect)(pr.fulfilled).toBe(true);
                (0, vitest_1.expect)(pr.data).toBe('result');
                (0, vitest_1.expect)(result).toBe('result');
            })
                .catch(() => { });
            pr.resolve('result');
            return pr;
        });
    });
    (0, vitest_1.describe)('mergeAll', () => {
        (0, vitest_1.it)('merge all of the input objects to one', () => {
            (0, vitest_1.expect)((0, utils_1.mergeAll)([{ prop1: 'test' }, { prop2: 'test' }])).toEqual({
                prop1: 'test',
                prop2: 'test',
            });
        });
        (0, vitest_1.it)('uses last item value if there are duplicated property', () => {
            (0, vitest_1.expect)((0, utils_1.mergeAll)([{ prop: 'test-1' }, { prop: 'test-2' }])).toEqual({
                prop: 'test-2',
            });
        });
    });
    (0, vitest_1.describe)('mergeAllPollRequestPromises', () => {
        (0, vitest_1.it)('merge all input poll promises into one', async () => {
            const pr1 = Object.assign(Promise.resolve({ key1: 'r1' }), {
                requests: { key1: Promise.resolve('r1') },
            });
            const pr2 = Object.assign(Promise.resolve({ key2: 'r2' }), {
                requests: { key2: Promise.resolve('r2') },
            });
            const mergedPr = (0, utils_1.mergeAllPollRequestPromises)([pr1, pr2]);
            (0, vitest_1.expect)(Object.keys(mergedPr.requests)).toEqual(['key1', 'key2']);
            const res = await Promise.all([
                mergedPr,
                mergedPr.requests.key1,
                mergedPr.requests.key2,
            ]);
            (0, vitest_1.expect)(res[0]).toEqual({
                key1: 'r1',
                key2: 'r2',
            });
            (0, vitest_1.expect)(res[1]).toEqual('r1');
            (0, vitest_1.expect)(res[2]).toEqual('r2');
        });
    });
    (0, vitest_1.describe)('mapRecord', () => {
        (0, vitest_1.it)('map each item in a record by using the mapper function', () => {
            const input = {
                key1: { prop: 'one' },
                key2: { prop: 'two' },
            };
            const result = (0, utils_1.mapRecord)(input, ({ prop }) => prop);
            (0, vitest_1.expect)(result).toEqual({
                key1: 'one',
                key2: 'two',
            });
        });
    });
    (0, vitest_1.describe)('withCounter', () => {
        (0, vitest_1.it)('pass counter as the first are to the input function that counts the call numbers', () => {
            const fn = vitest_1.vi.fn();
            const wrappedFunction = (0, utils_1.withCounter)(fn);
            wrappedFunction('arg1', 'arg2');
            wrappedFunction('arg1', 'arg2');
            (0, vitest_1.expect)(fn).toBeCalledTimes(2);
            (0, vitest_1.expect)(fn.mock.calls[0]).toEqual([1, 'arg1', 'arg2']);
            (0, vitest_1.expect)(fn.mock.calls[1]).toEqual([2, 'arg1', 'arg2']);
        });
    });
    (0, vitest_1.describe)('sleep', () => {
        (0, vitest_1.beforeEach)(() => {
            vitest_1.vi.useFakeTimers();
        });
        (0, vitest_1.afterEach)(() => {
            vitest_1.vi.useRealTimers();
        });
        (0, vitest_1.it)('returns a promise that resolves after the sleep time', async () => {
            const start = Date.now();
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            (0, utils_1.sleep)(10).then(() => {
                const end = Date.now();
                (0, vitest_1.expect)(end - start).toBe(10);
            });
            vitest_1.vi.runAllTimers();
        });
    });
});
//# sourceMappingURL=utils.test.js.map