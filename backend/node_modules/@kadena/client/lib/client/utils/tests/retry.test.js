"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const retry_1 = require("../retry");
const utils_1 = require("../utils");
(0, vitest_1.describe)('retry', () => {
    (0, vitest_1.it)('should calls the first try without waiting for interval', async () => {
        const task = vitest_1.vi.fn().mockResolvedValue(true);
        const runTask = (0, retry_1.retry)(task);
        const result = await runTask();
        (0, vitest_1.expect)(result).toBe(true);
        (0, vitest_1.expect)(task).toBeCalledTimes(1);
    });
    (0, vitest_1.it)('should retry a task till it returns the result', async () => {
        const task = vitest_1.vi.fn((0, utils_1.withCounter)((idx) => {
            if (idx === 5)
                return Promise.resolve(true);
            return Promise.reject();
        }));
        const runTask = (0, retry_1.retry)(task);
        const result = await runTask({ interval: 10, timeout: 1000 });
        (0, vitest_1.expect)(result).toBe(true);
        (0, vitest_1.expect)(task).toBeCalledTimes(5);
    });
    (0, vitest_1.it)('throws timeout exception if task does not return value after the timeout', async () => {
        const task = vitest_1.vi.fn((0, utils_1.withCounter)((idx) => {
            if (idx === 5)
                return Promise.resolve(true);
            return Promise.reject(new Error('its not ready'));
        }));
        const runTask = (0, retry_1.retry)(task);
        const promise = runTask({ interval: 100, timeout: 200 });
        await (0, vitest_1.expect)(promise).rejects.toEqual(new Error('TIME_OUT_REJECT'));
    });
    (0, vitest_1.it)('it should abort the process if abortSignal is called', async () => {
        const abortController = new AbortController();
        const task = vitest_1.vi.fn((0, utils_1.withCounter)((idx) => {
            if (idx === 2) {
                abortController.abort();
                return Promise.resolve(true);
            }
            return Promise.reject(new Error('its not ready'));
        }));
        const runTask = (0, retry_1.retry)(task, abortController.signal);
        const promise = runTask({ interval: 10, timeout: 200 });
        await (0, vitest_1.expect)(promise).rejects.toEqual(new Error('ABORTED'));
    });
    (0, vitest_1.it)('it should abort the process if abortSignal is alredy aborted', async () => {
        const abortController = new AbortController();
        abortController.abort();
        const task = vitest_1.vi.fn((0, utils_1.withCounter)((idx) => {
            if (idx === 2) {
                return Promise.resolve(true);
            }
            return Promise.reject(new Error('its not ready'));
        }));
        const runTask = (0, retry_1.retry)(task, abortController.signal);
        const promise = runTask({ interval: 10, timeout: 200 });
        await (0, vitest_1.expect)(promise).rejects.toEqual(new Error('ABORTED'));
    });
});
//# sourceMappingURL=retry.test.js.map