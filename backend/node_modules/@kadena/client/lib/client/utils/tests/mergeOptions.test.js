"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const mergeOptions_1 = require("../mergeOptions");
(0, vitest_1.describe)('mergeOptions', () => {
    (0, vitest_1.it)('should merge options', () => {
        const options = { a: 1, b: 2 };
        const newOptions = { b: 3, c: 4 };
        const mergedOptions = (0, mergeOptions_1.mergeOptions)(options, newOptions);
        (0, vitest_1.expect)(mergedOptions).toEqual({ a: 1, b: 3, c: 4 });
    });
    (0, vitest_1.it)('should merge arrays', () => {
        const options = { a: 1, b: [2] };
        const newOptions = { b: [3], c: 4 };
        const mergedOptions = (0, mergeOptions_1.mergeOptions)(options, newOptions);
        (0, vitest_1.expect)(mergedOptions).toEqual({ a: 1, b: [2, 3], c: 4 });
    });
    (0, vitest_1.it)('should merge object sub properties', () => {
        const options = { a: 1, b: 2, c: { one: 'one' } };
        const newOptions = { b: [3], c: { second: 'second' } };
        const mergedOptions = (0, mergeOptions_1.mergeOptions)(options, newOptions);
        (0, vitest_1.expect)(mergedOptions).toEqual({
            a: 1,
            b: [2, 3],
            c: {
                one: 'one',
                second: 'second',
            },
        });
    });
    (0, vitest_1.it)('returns the second object if the first object is undefined', () => {
        const options = { a: 1, b: 2 };
        const mergedOptions = (0, mergeOptions_1.mergeOptions)(undefined, options);
        (0, vitest_1.expect)(mergedOptions).toEqual({ a: 1, b: 2 });
    });
    (0, vitest_1.it)('returns the first object if the second object is undefined', () => {
        const options = { a: 1, b: 2 };
        const mergedOptions = (0, mergeOptions_1.mergeOptions)(options, undefined);
        (0, vitest_1.expect)(mergedOptions).toEqual({ a: 1, b: 2 });
    });
});
//# sourceMappingURL=mergeOptions.test.js.map