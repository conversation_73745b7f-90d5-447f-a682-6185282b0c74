{"version": 3, "file": "utils.test.js", "sourceRoot": "", "sources": ["../../../../src/client/utils/tests/utils.test.ts"], "names": [], "mappings": ";;AAAA,mCAAyE;AAEzE,oCAWkB;AAElB,IAAA,iBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAA,iBAAQ,EAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,IAAA,WAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YAC9B,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC;YACjC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aAC3B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,aAAa,CAAC;YAC3B,MAAM,QAAQ,GAAG,UAAU,CAAC;YAC5B,IAAA,eAAM,EAAC,IAAA,cAAM,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,IAAI,GAAG,cAAc,CAAC;YAC5B,MAAM,QAAQ,GAAG,WAAW,CAAC;YAC7B,IAAA,eAAM,EAAC,IAAA,cAAM,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,cAAc,CAAC;YAC5B,MAAM,QAAQ,GAAG,WAAW,CAAC;YAC7B,MAAM,MAAM,GAAG;gBACb,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,MAAM;aACd,CAAC;YACF,IAAA,eAAM,EAAC,IAAA,cAAM,EAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CACzC,6CAA6C,CAC9C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAA,WAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,IAAA,eAAM,EACJ,IAAA,2BAAmB,EAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAC/D,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,IAAA,eAAM,EACJ,IAAA,2BAAmB,EAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAC/D,CAAC,IAAI,CACJ,uEAAuE,CACxE,CAAC;YACF,IAAA,eAAM,EACJ,IAAA,2BAAmB,EAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAC/D,CAAC,IAAI,CACJ,yEAAyE,CAC1E,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,IAAA,eAAM,EAAC,GAAG,EAAE,CACV,IAAA,2BAAmB,EAAC,EAAE,SAAS,EAAE,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CACvE,CAAC,YAAY,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,IAAA,WAAE,EAAC,+EAA+E,EAAE,GAAG,EAAE;YACvF,MAAM,OAAO,GAAG,uBAAuB,CAAC;YACxC,MAAM,eAAe,GAAG,IAAA,kBAAU,EAAC,OAAO,CAAC,CAAC;YAC5C,IAAA,eAAM,EAAC,eAAe,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CACrE,4DAA4D,CAC7D,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,IAAA,WAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAG,wBAAwB,CAAC;YACzC,MAAM,eAAe,GAAG,IAAA,kBAAU,EAAC,OAAO,CAAC,CAAC;YAC5C,IAAA,eAAM,EAAC,eAAe,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CACrE,4DAA4D,CAC7D,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,IAAA,WAAE,EAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,GAAG,GAAG,IAAA,kBAAU,GAAE,CAAC;YACzB,IAAA,eAAM,EAAC,GAAG,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,IAAA,eAAM,EAAC,GAAG,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,IAAA,eAAM,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,EAAE,GAAG,IAAA,kBAAU,GAAE,CAAC;YACxB,EAAE,CAAC,OAAO;iBACP,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC,CAAC;iBACD,KAAK,CAAC,GAAG,EAAE;gBACV,IAAA,eAAM,EAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACL,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,EAAE,GAAG,IAAA,kBAAU,GAAE,CAAC;YACxB,EAAE,CAAC,OAAO;iBACP,IAAI,CAAC,GAAG,EAAE;gBACT,IAAA,eAAM,EAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YACL,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACtB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,EAAE,GAAG,IAAA,kBAAU,GAAE,CAAC;YACxB,EAAE,CAAC,OAAO;iBACP,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,IAAA,eAAM,EAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC;iBACD,KAAK,CAAC,GAAG,EAAE;gBACV,IAAA,eAAM,EAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACL,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,EAAE,GAAG,IAAA,kBAAU,GAAE,CAAC;YACxB,EAAE,CAAC,OAAO;iBACP,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC;iBACd,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,IAAA,eAAM,EAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YACL,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACtB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,IAAA,kBAAU,GAAE,CAAC;YACxB,EAAE,CAAC,OAAO;iBACP,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,IAAA,eAAM,EAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAA,eAAM,EAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC/B,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC,CAAC;iBACD,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;YACnB,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;QACxB,IAAA,WAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,IAAA,eAAM,EAAC,IAAA,gBAAQ,EAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,MAAM;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,IAAA,eAAM,EAAC,IAAA,gBAAQ,EAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACjE,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,IAAA,WAAE,EAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,GAAG,GAAgC,MAAM,CAAC,MAAM,CACpD,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAC/B;gBACE,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;aAC1C,CACF,CAAC;YAEF,MAAM,GAAG,GAAgC,MAAM,CAAC,MAAM,CACpD,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAC/B;gBACE,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;aAC1C,CACF,CAAC;YACF,MAAM,QAAQ,GAAG,IAAA,mCAA2B,EAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAEzD,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YAEjE,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC5B,QAAQ;gBACR,QAAQ,CAAC,QAAQ,CAAC,IAAI;gBACtB,QAAQ,CAAC,QAAQ,CAAC,IAAI;aACvB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACrB,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,IAAI;aACX,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7B,IAAA,eAAM,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAA,WAAE,EAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,KAAK,GAAG;gBACZ,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;gBACrB,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;aACtB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,iBAAS,EAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YAEpD,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;aACZ,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,IAAA,WAAE,EAAC,kFAAkF,EAAE,GAAG,EAAE;YAC1F,MAAM,EAAE,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;YACnB,MAAM,eAAe,GAAG,IAAA,mBAAW,EAAC,EAAE,CAAC,CAAC;YACxC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAChC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAEhC,IAAA,eAAM,EAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAA,eAAM,EAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YACtD,IAAA,eAAM,EAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,OAAO,EAAE,GAAG,EAAE;QACrB,IAAA,mBAAU,EAAC,GAAG,EAAE;YACd,WAAE,CAAC,aAAa,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,IAAA,kBAAS,EAAC,GAAG,EAAE;YACb,WAAE,CAAC,aAAa,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACzB,mEAAmE;YACnE,IAAA,aAAK,EAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,IAAA,eAAM,EAAC,GAAG,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YACH,WAAE,CAAC,YAAY,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';\nimport type { IPollRequestPromise } from '../../interfaces/interfaces';\nimport {\n  getHostUrl,\n  getPromise,\n  getUrl,\n  jsonRequest,\n  kadenaHostGenerator,\n  mapRecord,\n  mergeAll,\n  mergeAllPollRequestPromises,\n  sleep,\n  withCounter,\n} from '../utils';\n\ndescribe('client utils', () => {\n  describe('jsonRequest', () => {\n    it('returns a request object form a json', () => {\n      const body = { prop: 'test' };\n      const result = jsonRequest(body);\n      expect(result).toEqual({\n        headers: { 'Content-Type': 'application/json' },\n        method: 'POST',\n        body: JSON.stringify(body),\n      });\n    });\n  });\n\n  describe('getUrl', () => {\n    it('returns a full url from the host and endpoint', () => {\n      const host = 'http://host';\n      const endpoint = 'api/test';\n      expect(getUrl(host, endpoint)).toBe('http://host/api/test');\n    });\n\n    it('skips the back slashed and join two parts only with one back slash', () => {\n      const host = 'http://host/';\n      const endpoint = '/api/test';\n      expect(getUrl(host, endpoint)).toBe('http://host/api/test');\n    });\n\n    it('adds query params if they are presented and not undefined', () => {\n      const host = 'http://host/';\n      const endpoint = '/api/test';\n      const params = {\n        first: undefined,\n        second: true,\n        third: 'test',\n      };\n      expect(getUrl(host, endpoint, params)).toBe(\n        'http://host/api/test?second=true&third=test',\n      );\n    });\n  });\n\n  describe('kadenaHostGenerator', () => {\n    it('returns mainnet url with the correct chainId', () => {\n      expect(\n        kadenaHostGenerator({ networkId: 'mainnet01', chainId: '14' }),\n      ).toBe('https://api.chainweb.com/chainweb/0.0/mainnet01/chain/14/pact');\n    });\n\n    it('returns testnet url with the correct chainId', () => {\n      expect(\n        kadenaHostGenerator({ networkId: 'testnet04', chainId: '14' }),\n      ).toBe(\n        'https://api.testnet.chainweb.com/chainweb/0.0/testnet04/chain/14/pact',\n      );\n      expect(\n        kadenaHostGenerator({ networkId: 'testnet05', chainId: '14' }),\n      ).toBe(\n        'https://api.testnet05.chainweb.com/chainweb/0.0/testnet05/chain/14/pact',\n      );\n    });\n\n    it('throes exception if networkId is not either mainnet01 nor testnet04 ', () => {\n      expect(() =>\n        kadenaHostGenerator({ networkId: 'incorrect-network', chainId: '14' }),\n      ).toThrowError(Error(`UNKNOWN_NETWORK_ID: incorrect-network`));\n    });\n  });\n\n  describe('getHostUrl', () => {\n    it('returns a function that generates host url based on the networkId and chainId', () => {\n      const hostUrl = 'http://localhost:8080';\n      const getLocalHostUrl = getHostUrl(hostUrl);\n      expect(getLocalHostUrl({ networkId: 'mainnet01', chainId: '14' })).toBe(\n        'http://localhost:8080/chainweb/0.0/mainnet01/chain/14/pact',\n      );\n    });\n    it(\"removes the last '/' from the host url\", () => {\n      const hostUrl = 'http://localhost:8080/';\n      const getLocalHostUrl = getHostUrl(hostUrl);\n      expect(getLocalHostUrl({ networkId: 'mainnet01', chainId: '14' })).toBe(\n        'http://localhost:8080/chainweb/0.0/mainnet01/chain/14/pact',\n      );\n    });\n  });\n\n  describe('getPromise', () => {\n    it('returns a wrapped object of a promise the resolve and reject function', () => {\n      const obj = getPromise();\n      expect(obj.promise).toBeDefined();\n      expect(obj.resolve).toBeDefined();\n      expect(obj.reject).toBeDefined();\n    });\n\n    it('resolves the promise if resolve function is called', () => {\n      const pr = getPromise();\n      pr.promise\n        .then((result) => {\n          expect(result).toBe('result');\n        })\n        .catch(() => {\n          expect('this should not happen').toBe(true);\n        });\n      pr.resolve('result');\n      return pr;\n    });\n\n    it('rejects the promise if reject function is called', () => {\n      const pr = getPromise();\n      pr.promise\n        .then(() => {\n          expect('this should not happen').toBe(true);\n        })\n        .catch((result) => {\n          expect(result).toBe('rejected');\n        });\n      pr.reject('rejected');\n      return pr;\n    });\n\n    it('sets fullfiled to true resolve is called', () => {\n      const pr = getPromise();\n      pr.promise\n        .then((result) => {\n          expect(pr.fulfilled).toBe(true);\n        })\n        .catch(() => {\n          expect('this should not happen').toBe(true);\n        });\n      pr.resolve('result');\n      return pr;\n    });\n\n    it('sets fullfiled to true if reject is called', () => {\n      const pr = getPromise();\n      pr.promise\n        .then(() => {})\n        .catch((result) => {\n          expect(pr.fulfilled).toBe(true);\n        });\n      pr.reject('rejected');\n      return pr;\n    });\n\n    it('sets data if resolve is called', () => {\n      const pr = getPromise();\n      pr.promise\n        .then((result) => {\n          expect(pr.fulfilled).toBe(true);\n          expect(pr.data).toBe('result');\n          expect(result).toBe('result');\n        })\n        .catch(() => {});\n      pr.resolve('result');\n      return pr;\n    });\n  });\n\n  describe('mergeAll', () => {\n    it('merge all of the input objects to one', () => {\n      expect(mergeAll([{ prop1: 'test' }, { prop2: 'test' }])).toEqual({\n        prop1: 'test',\n        prop2: 'test',\n      });\n    });\n\n    it('uses last item value if there are duplicated property', () => {\n      expect(mergeAll([{ prop: 'test-1' }, { prop: 'test-2' }])).toEqual({\n        prop: 'test-2',\n      });\n    });\n  });\n\n  describe('mergeAllPollRequestPromises', () => {\n    it('merge all input poll promises into one', async () => {\n      const pr1: IPollRequestPromise<string> = Object.assign(\n        Promise.resolve({ key1: 'r1' }),\n        {\n          requests: { key1: Promise.resolve('r1') },\n        },\n      );\n\n      const pr2: IPollRequestPromise<string> = Object.assign(\n        Promise.resolve({ key2: 'r2' }),\n        {\n          requests: { key2: Promise.resolve('r2') },\n        },\n      );\n      const mergedPr = mergeAllPollRequestPromises([pr1, pr2]);\n\n      expect(Object.keys(mergedPr.requests)).toEqual(['key1', 'key2']);\n\n      const res = await Promise.all([\n        mergedPr,\n        mergedPr.requests.key1,\n        mergedPr.requests.key2,\n      ]);\n\n      expect(res[0]).toEqual({\n        key1: 'r1',\n        key2: 'r2',\n      });\n\n      expect(res[1]).toEqual('r1');\n      expect(res[2]).toEqual('r2');\n    });\n  });\n\n  describe('mapRecord', () => {\n    it('map each item in a record by using the mapper function', () => {\n      const input = {\n        key1: { prop: 'one' },\n        key2: { prop: 'two' },\n      };\n\n      const result = mapRecord(input, ({ prop }) => prop);\n\n      expect(result).toEqual({\n        key1: 'one',\n        key2: 'two',\n      });\n    });\n  });\n\n  describe('withCounter', () => {\n    it('pass counter as the first are to the input function that counts the call numbers', () => {\n      const fn = vi.fn();\n      const wrappedFunction = withCounter(fn);\n      wrappedFunction('arg1', 'arg2');\n      wrappedFunction('arg1', 'arg2');\n\n      expect(fn).toBeCalledTimes(2);\n      expect(fn.mock.calls[0]).toEqual([1, 'arg1', 'arg2']);\n      expect(fn.mock.calls[1]).toEqual([2, 'arg1', 'arg2']);\n    });\n  });\n\n  describe('sleep', () => {\n    beforeEach(() => {\n      vi.useFakeTimers();\n    });\n\n    afterEach(() => {\n      vi.useRealTimers();\n    });\n\n    it('returns a promise that resolves after the sleep time', async () => {\n      const start = Date.now();\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      sleep(10).then(() => {\n        const end = Date.now();\n        expect(end - start).toBe(10);\n      });\n      vi.runAllTimers();\n    });\n  });\n});\n"]}