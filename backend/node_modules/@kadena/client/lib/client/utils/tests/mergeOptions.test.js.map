{"version": 3, "file": "mergeOptions.test.js", "sourceRoot": "", "sources": ["../../../../src/client/utils/tests/mergeOptions.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,kDAA+C;AAE/C,IAAA,iBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAA,WAAE,EAAC,sBAAsB,EAAE,GAAG,EAAE;QAC9B,MAAM,OAAO,GAA2B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACvD,MAAM,UAAU,GAA2B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1D,MAAM,aAAa,GAAG,IAAA,2BAAY,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxD,IAAA,eAAM,EAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qBAAqB,EAAE,GAAG,EAAE;QAC7B,MAAM,OAAO,GAAsC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,MAAM,UAAU,GAAsC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACvE,MAAM,aAAa,GAAG,IAAA,2BAAY,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxD,IAAA,eAAM,EAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,MAAM,OAAO,GAAwB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC;QACvE,MAAM,UAAU,GAAwB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;QAC5E,MAAM,aAAa,GAAG,IAAA,2BAAY,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxD,IAAA,eAAM,EAAC,aAAa,CAAC,CAAC,OAAO,CAAC;YAC5B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,EAAE;gBACD,GAAG,EAAE,KAAK;gBACV,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,4DAA4D,EAAE,GAAG,EAAE;QACpE,MAAM,OAAO,GAA2B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACvD,MAAM,aAAa,GAAG,IAAA,2BAAY,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACvD,IAAA,eAAM,EAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,4DAA4D,EAAE,GAAG,EAAE;QACpE,MAAM,OAAO,GAA2B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACvD,MAAM,aAAa,GAAG,IAAA,2BAAY,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACvD,IAAA,eAAM,EAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { mergeOptions } from '../mergeOptions';\n\ndescribe('mergeOptions', () => {\n  it('should merge options', () => {\n    const options: Record<string, number> = { a: 1, b: 2 };\n    const newOptions: Record<string, number> = { b: 3, c: 4 };\n    const mergedOptions = mergeOptions(options, newOptions);\n    expect(mergedOptions).toEqual({ a: 1, b: 3, c: 4 });\n  });\n\n  it('should merge arrays', () => {\n    const options: Record<string, number | [number]> = { a: 1, b: [2] };\n    const newOptions: Record<string, number | [number]> = { b: [3], c: 4 };\n    const mergedOptions = mergeOptions(options, newOptions);\n    expect(mergedOptions).toEqual({ a: 1, b: [2, 3], c: 4 });\n  });\n\n  it('should merge object sub properties', () => {\n    const options: Record<string, any> = { a: 1, b: 2, c: { one: 'one' } };\n    const newOptions: Record<string, any> = { b: [3], c: { second: 'second' } };\n    const mergedOptions = mergeOptions(options, newOptions);\n    expect(mergedOptions).toEqual({\n      a: 1,\n      b: [2, 3],\n      c: {\n        one: 'one',\n        second: 'second',\n      },\n    });\n  });\n  it('returns the second object if the first object is undefined', () => {\n    const options: Record<string, number> = { a: 1, b: 2 };\n    const mergedOptions = mergeOptions(undefined, options);\n    expect(mergedOptions).toEqual({ a: 1, b: 2 });\n  });\n  it('returns the first object if the second object is undefined', () => {\n    const options: Record<string, number> = { a: 1, b: 2 };\n    const mergedOptions = mergeOptions(options, undefined);\n    expect(mergedOptions).toEqual({ a: 1, b: 2 });\n  });\n});\n"]}