{"version": 3, "file": "status.js", "sourceRoot": "", "sources": ["../../../src/client/api/status.ts"], "names": [], "mappings": ";;;AACA,uEAAoD;AAKpD,0CAAuC;AAEvC,0CAAiE;AAUjE;;GAEG;AACI,MAAM,UAAU,GAAgB,CACrC,IAAY,EACZ,UAAoB,EACpB,OAAsB,EACe,EAAE;;IACvC,MAAM,EACJ,MAAM,GAAG,GAAG,EAAE,GAAE,CAAC,EACjB,OAAO,EACP,QAAQ,EACR,iBAAiB,GAAG,CAAC,EACrB,QAAQ,GAAG,GAAG,EAAE,GAAE,CAAC,EACnB,GAAG,WAAW,EACf,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IAClB,MAAM,MAAM,GAAG,MAAA,WAAW,CAAC,MAAM,mCAAI,SAAS,CAAC;IAC/C,IAAI,WAAW,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;IAClC,MAAM,GAAG,GAAgD,WAAW,CAAC,MAAM,CACzE,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QACpB,GAAG,GAAG;QACN,CAAC,UAAU,CAAC,EAAE,IAAA,kBAAU,GAAE;KAC3B,CAAC,EACF,EAAE,CACH,CAAC;IACF,MAAM,IAAI,GAAG,KAAK,IAAmB,EAAE;QACrC,IAAI,CAAC;YACH,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC5B,MAAM,YAAY,GAAG,MAAM,IAAA,2BAAI,EAC7B,EAAE,WAAW,EAAE,EACf,IAAI,EACJ,iBAAiB,EACjB,WAAW,CACZ,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC3C,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC/B,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC5B,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACzB,MAAM,KAAK,CAAC;QACd,CAAC;QACD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC,CAAC;IACF,MAAM,WAAW,GAAG,IAAA,aAAK,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAExC,WAAW,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QAC/C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAChC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC;gBAClB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,aAAa,GAAwC,MAAM,CAAC,MAAM,CACtE,OAAO,CAAC,GAAG,CACT,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,EAAE,CAC3C,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CACpD,CACF,CAAC,IAAI,CAAC,gBAAQ,CAAC,EAChB,EAAE,QAAQ,EAAE,IAAA,iBAAS,EAAC,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CACvD,CAAC;IAEF,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AAhEW,QAAA,UAAU,cAgErB", "sourcesContent": ["import type { ICommandResult } from '@kadena/chainweb-node-client';\nimport { poll } from '@kadena/chainweb-node-client';\nimport type {\n  IPollOptions,\n  IPollRequestPromise,\n} from '../interfaces/interfaces';\nimport { retry } from '../utils/retry';\nimport type { IExtPromise } from '../utils/utils';\nimport { getPromise, mapRecord, mergeAll } from '../utils/utils';\n\nexport interface IPollStatus {\n  (\n    host: string,\n    requestIds: string[],\n    options?: IPollOptions,\n  ): IPollRequestPromise<ICommandResult>;\n}\n\n/**\n * poll until all request are fulfilled\n */\nexport const pollStatus: IPollStatus = (\n  host: string,\n  requestIds: string[],\n  options?: IPollOptions,\n): IPollRequestPromise<ICommandResult> => {\n  const {\n    onPoll = () => {},\n    timeout,\n    interval,\n    confirmationDepth = 0,\n    onResult = () => {},\n    ...requestInit\n  } = options ?? {};\n  const signal = requestInit.signal ?? undefined;\n  let requestKeys = [...requestIds];\n  const prs: Record<string, IExtPromise<ICommandResult>> = requestKeys.reduce(\n    (acc, requestKey) => ({\n      ...acc,\n      [requestKey]: getPromise(),\n    }),\n    {},\n  );\n  const task = async (): Promise<void> => {\n    try {\n      requestKeys.forEach(onPoll);\n      const pollResponse = await poll(\n        { requestKeys },\n        host,\n        confirmationDepth,\n        requestInit,\n      );\n      Object.values(pollResponse).forEach((item) => {\n        prs[item.reqKey].resolve(item);\n        onResult(item.reqKey, item);\n        requestKeys = requestKeys.filter((key) => key !== item.reqKey);\n      });\n    } catch (error) {\n      onPoll(undefined, error);\n      throw error;\n    }\n    if (requestKeys.length > 0) {\n      return Promise.reject(new Error('NOT_COMPLETED'));\n    }\n  };\n  const retryStatus = retry(task, signal);\n\n  retryStatus({ interval, timeout }).catch((err) => {\n    Object.values(prs).forEach((pr) => {\n      if (!pr.fulfilled) {\n        pr.reject(err);\n      }\n    });\n  });\n\n  const returnPromise: IPollRequestPromise<ICommandResult> = Object.assign(\n    Promise.all(\n      Object.entries(prs).map(([requestKey, pr]) =>\n        pr.promise.then((data) => ({ [requestKey]: data })),\n      ),\n    ).then(mergeAll),\n    { requests: mapRecord(prs, ({ promise }) => promise) },\n  );\n\n  return returnPromise;\n};\n"]}