"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const spv_1 = require("../spv");
const server = (0, node_1.setupServer)();
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
const post = (path, response, status = 200) => msw_1.http.post(path, () => typeof response === 'string'
    ? new msw_1.HttpResponse(response, { status })
    : msw_1.HttpResponse.json(response, { status }), { once: true });
(0, vitest_1.describe)('getSpv', () => {
    (0, vitest_1.it)('calls /spv endpoint to generate spv for a request and a target chain', async () => {
        const response = 'spv-proof';
        server.resetHandlers(post('http://test-blockchain-host.com/spv', response));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKey = 'request-key';
        const targetChainId = '1';
        const result = await (0, spv_1.getSpv)(hostUrl, requestKey, targetChainId);
        (0, vitest_1.expect)(result).toBe(response);
    });
    (0, vitest_1.it)('throws exception if spv function does not return string', async () => {
        server.resetHandlers(post('http://test-blockchain-host.com/spv', 'PROOF_IS_NOT_AVAILABLE', 500));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKey = 'request-key';
        const targetChainId = '1';
        await (0, vitest_1.expect)(() => (0, spv_1.getSpv)(hostUrl, requestKey, targetChainId)).rejects.toThrowError(new Error('PROOF_IS_NOT_AVAILABLE'));
    });
});
(0, vitest_1.describe)('pollSpv', () => {
    (0, vitest_1.it)('calls /spv endpoint several times to generate spv for a request and a target chain', async () => {
        const response = 'spv-proof';
        server.resetHandlers(post('http://test-blockchain-host.com/spv', 'not found', 404), post('http://test-blockchain-host.com/spv', 'not found', 404), post('http://test-blockchain-host.com/spv', 'not found', 404), post('http://test-blockchain-host.com/spv', 'not found', 404), post('http://test-blockchain-host.com/spv', response));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKey = 'request-key';
        const targetChainId = '1';
        const result = await (0, spv_1.pollSpv)(hostUrl, requestKey, targetChainId, {
            interval: 10,
        });
        (0, vitest_1.expect)(result).toBe(response);
    });
});
//# sourceMappingURL=spv.test.js.map