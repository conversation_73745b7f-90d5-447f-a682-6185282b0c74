{"version": 3, "file": "status.test.js", "sourceRoot": "", "sources": ["../../../../src/client/api/tests/status.test.ts"], "names": [], "mappings": ";;AAAA,6BAAgD;AAChD,mCAAuC;AACvC,mCAQgB;AAChB,sCAAuC;AAEvC,MAAM,MAAM,GAAG,IAAA,kBAAW,GAAE,CAAC;AAC7B,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,MAAM,IAAI,GAAG,CACX,IAAY,EACZ,QAAqD,EACrD,MAAM,GAAG,GAAG,EACZ,IAAa,EACiB,EAAE,CAChC,UAAI,CAAC,IAAI,CACP,IAAI,EACJ,KAAK,IAAI,EAAE;IACT,MAAM,IAAA,WAAK,EAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,CAAC,CAAC,CAAC;IACvB,OAAO,OAAO,QAAQ,KAAK,QAAQ;QACjC,CAAC,CAAC,IAAI,kBAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC;QACxC,CAAC,CAAC,kBAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;AAC9C,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;AAEJ,IAAA,iBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,IAAA,WAAE,EAAC,oFAAoF,EAAE,KAAK,IAAI,EAAE;QAClG,MAAM,SAAS,GAAG;YAChB,EAAE;YACF,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YAChC,EAAE;YACF,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;SACjC,CAAC;QAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAU,EAAC,OAAO,EAAE,WAAW,EAAE;YACpD,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;YAC5B,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;QACxF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CACjE,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,OAAO,GAAG,IAAA,mBAAU,EAAC,OAAO,EAAE,WAAW,EAAE;YAC/C,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;QAEH,MAAM,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qEAAqE,EAAE,KAAK,IAAI,EAAE;QACnF,MAAM,SAAS,GAAG;YAChB,EAAE;YACF,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YAChC,EAAE;YACF,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;SACjC,CAAC;QAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;QAEF,MAAM,MAAM,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;QAEvB,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,IAAA,mBAAU,EAAC,OAAO,EAAE,WAAW,EAAE;YACrC,QAAQ,EAAE,EAAE;YACZ,MAAM;SACP,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAElC,YAAY;QACZ,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9C,sCAAsC;QACtC,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9C,iBAAiB;QACjB,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9C,qCAAqC;QACrC,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;QAC5D,MAAM,SAAS,GAAG;YAChB,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;SAC/D,CAAC;QAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAU,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAEtD,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;YAC5B,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;QAC1E,MAAM,SAAS,GAAG;YAChB,EAAE;YACF,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YAChC,EAAE;YACF,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;SACjC,CAAC;QAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EACjE,IAAI,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,QAAQ,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;QAEzB,MAAM,IAAA,mBAAU,EAAC,OAAO,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QACnE,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACpC,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACvE,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;QACtE,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,6CAA6C,EAAE,EAAE,CAAC,CACxD,CAAC;QAEF,MAAM,MAAM,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;QAEvB,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,IAAA,eAAM,EACV,IAAA,mBAAU,EAAC,OAAO,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CACxE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAEhD,IAAA,eAAM,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { HttpResponse, delay, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport {\n  afterAll,\n  afterEach,\n  beforeAll,\n  describe,\n  expect,\n  it,\n  vi,\n} from 'vitest';\nimport { pollStatus } from '../status';\n\nconst server = setupServer();\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\nconst post = (\n  path: string,\n  response: string | Record<string, unknown> | Response,\n  status = 200,\n  wait?: number,\n): ReturnType<typeof http.post> =>\n  http.post(\n    path,\n    async () => {\n      await delay(wait ?? 0);\n      return typeof response === 'string'\n        ? new HttpResponse(response, { status })\n        : HttpResponse.json(response, { status });\n    },\n    { once: true },\n  );\n\ndescribe('pollStatus', () => {\n  it('calls the /poll endpoint several times till it gets the status of all request keys', async () => {\n    const responses = [\n      {},\n      { 'key-1': { reqKey: 'key-1' } },\n      {},\n      { 'key-2': { reqKey: 'key-2' } },\n    ];\n\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/poll', responses[0]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[1]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[2]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[3]),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKeys = ['key-1', 'key-2'];\n\n    const result = await pollStatus(hostUrl, requestKeys, {\n      interval: 10,\n    });\n\n    expect(result).toEqual({\n      'key-1': { reqKey: 'key-1' },\n      'key-2': { reqKey: 'key-2' },\n    });\n  });\n\n  it('throws TIME_OUT_REJECT if the task get longer than set in timeout option', async () => {\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/poll', {}, 200, 75),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKeys = ['key-1', 'key-2'];\n\n    const promise = pollStatus(hostUrl, requestKeys, {\n      interval: 10,\n      timeout: 50,\n    });\n\n    await expect(promise).rejects.toEqual(new Error('TIME_OUT_REJECT'));\n  });\n\n  it('calls onPoll call back before fetching each request key in each try', async () => {\n    const responses = [\n      {},\n      { 'key-1': { reqKey: 'key-1' } },\n      {},\n      { 'key-2': { reqKey: 'key-2' } },\n    ];\n\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/poll', responses[0]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[1]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[2]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[3]),\n    );\n\n    const onPoll = vi.fn();\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKeys = ['key-1', 'key-2'];\n\n    await pollStatus(hostUrl, requestKeys, {\n      interval: 10,\n      onPoll,\n    });\n\n    expect(onPoll).toBeCalledTimes(6);\n\n    // first try\n    expect(onPoll.mock.calls[0][0]).toBe('key-1');\n    expect(onPoll.mock.calls[1][0]).toBe('key-2');\n\n    //second try that returns key-1 status\n    expect(onPoll.mock.calls[2][0]).toBe('key-1');\n    expect(onPoll.mock.calls[3][0]).toBe('key-2');\n\n    // third try that\n    expect(onPoll.mock.calls[4][0]).toBe('key-2');\n\n    //forth try that returns key-2 status\n    expect(onPoll.mock.calls[5][0]).toBe('key-2');\n  });\n\n  it(\"uses default options if they aren't provided\", async () => {\n    const responses = [\n      { 'key-1': { reqKey: 'key-1' }, 'key-2': { reqKey: 'key-2' } },\n    ];\n\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/poll', responses[0]),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKeys = ['key-1', 'key-2'];\n\n    const result = await pollStatus(hostUrl, requestKeys);\n\n    expect(result).toEqual({\n      'key-1': { reqKey: 'key-1' },\n      'key-2': { reqKey: 'key-2' },\n    });\n  });\n\n  it('calls onResult call back after each request key is fetched', async () => {\n    const responses = [\n      {},\n      { 'key-1': { reqKey: 'key-1' } },\n      {},\n      { 'key-2': { reqKey: 'key-2' } },\n    ];\n\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/poll', responses[0]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[1]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[2]),\n      post('http://test-blockchain-host.com/api/v1/poll', responses[3]),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKeys = ['key-1', 'key-2'];\n\n    const onResult = vi.fn();\n\n    await pollStatus(hostUrl, requestKeys, { interval: 10, onResult });\n    expect(onResult).toBeCalledTimes(2);\n    expect(onResult.mock.calls[0]).toEqual(['key-1', { reqKey: 'key-1' }]);\n    expect(onResult.mock.calls[1]).toEqual(['key-2', { reqKey: 'key-2' }]);\n  });\n  it('calls onPoll call back with error if the request fails', async () => {\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/api/v1/poll', {}),\n    );\n\n    const onPoll = vi.fn();\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKeys = ['key-1', 'key-2'];\n\n    await expect(\n      pollStatus(hostUrl, requestKeys, { interval: 10, timeout: 50, onPoll }),\n    ).rejects.toEqual(new Error('TIME_OUT_REJECT'));\n\n    expect(onPoll.mock.calls.at(-1)[1] instanceof Error).toBe(true);\n  });\n});\n"]}