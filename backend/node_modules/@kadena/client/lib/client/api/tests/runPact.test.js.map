{"version": 3, "file": "runPact.test.js", "sourceRoot": "", "sources": ["../../../../src/client/api/tests/runPact.test.ts"], "names": [], "mappings": ";;AACA,uEAAqD;AACrD,6BAAyC;AACzC,mCAAuC;AACvC,mCAQgB;AAChB,wCAAqC;AAErC,mCAAmC;AACnC,WAAE,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IAC/D,MAAM,GAAG,GAA8B,MAAM,cAAc,EAAE,CAAC;IAC9D,MAAM,KAAK,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpD,OAAO,EAAE,GAAG,GAAG,EAAE,KAAK,EAAE,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,IAAA,kBAAW,GAAE,CAAC;AAC7B,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,IAAA,iBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;IACvB,IAAA,kBAAS,EAAC,GAAG,EAAE;QACb,WAAE,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,GAAG,EAAE;QACZ,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;QACtF,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CACP,gCAAgC,EAChC,GAAG,EAAE,CAAC,kBAAY,CAAC,IAAI,CAAC,YAAY,CAAC,EACrC,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CACF,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAO,EAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;QAE7D,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAE3C,IAAA,eAAM,EAAC,4BAAK,CAAC,CAAC,cAAc,CAC1B;YACE,GAAG,EAAE,kGAAkG;YACvG,IAAI,EAAE,6CAA6C;YACnD,IAAI,EAAE,EAAE;SACT,EACD,mBAAmB,EACnB,EAAE,SAAS,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAE,CACnD,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import type * as ChainWebNodeClient from '@kadena/chainweb-node-client';\nimport { local } from '@kadena/chainweb-node-client';\nimport { HttpResponse, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport {\n  afterAll,\n  afterEach,\n  beforeAll,\n  describe,\n  expect,\n  it,\n  vi,\n} from 'vitest';\nimport { runPact } from '../runPact';\n\n// Hack to spy on exported function\nvi.mock('@kadena/chainweb-node-client', async (importOriginal) => {\n  const mod: typeof ChainWebNodeClient = await importOriginal();\n  const local = vi.fn().mockImplementation(mod.local);\n  return { ...mod, local };\n});\n\nconst server = setupServer();\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\ndescribe('runPact', () => {\n  beforeAll(() => {\n    vi.useFakeTimers().setSystemTime(new Date('2023-07-31'));\n  });\n\n  afterAll(() => {\n    vi.useRealTimers();\n  });\n\n  it('create a complete pact command from the input and send it to the chain', async () => {\n    const mockResponse = {};\n\n    server.resetHandlers(\n      http.post(\n        'http://blockchain/api/v1/local',\n        () => HttpResponse.json(mockResponse),\n        { once: true },\n      ),\n    );\n\n    const result = await runPact('http://blockchain', '(+ 1 1)');\n\n    expect(result).toStrictEqual(mockResponse);\n\n    expect(local).toBeCalledWith(\n      {\n        cmd: '{\"payload\":{\"exec\":{\"code\":\"(+ 1 1)\",\"data\":{}}},\"nonce\":\"kjs:nonce:1690761600000\",\"signers\":[]}',\n        hash: 'BFstB5srkwenVbxQYjMsdSIQiyaakhaYGjHA3ZKmntY',\n        sigs: [],\n      },\n      'http://blockchain',\n      { preflight: false, signatureVerification: false },\n    );\n  });\n});\n"]}