{"version": 3, "file": "spv.test.js", "sourceRoot": "", "sources": ["../../../../src/client/api/tests/spv.test.ts"], "names": [], "mappings": ";;AAAA,6BAAyC;AACzC,mCAAuC;AACvC,mCAA8E;AAC9E,gCAAyC;AAEzC,MAAM,MAAM,GAAG,IAAA,kBAAW,GAAE,CAAC;AAC7B,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,MAAM,IAAI,GAAG,CACX,IAAY,EACZ,QAA0C,EAC1C,MAAM,GAAG,GAAG,EACkB,EAAE,CAChC,UAAI,CAAC,IAAI,CACP,IAAI,EACJ,GAAG,EAAE,CACH,OAAO,QAAQ,KAAK,QAAQ;IAC1B,CAAC,CAAC,IAAI,kBAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC;IACxC,CAAC,CAAC,kBAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,EAC7C,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;AAEJ,IAAA,iBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,IAAA,WAAE,EAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;QACpF,MAAM,QAAQ,GAAG,WAAW,CAAC;QAE7B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE5E,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,UAAU,GAAG,aAAa,CAAC;QACjC,MAAM,aAAa,GAAG,GAAG,CAAC;QAE1B,MAAM,MAAM,GAAG,MAAM,IAAA,YAAM,EAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QAEhE,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;QACvE,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,qCAAqC,EACrC,wBAAwB,EACxB,GAAG,CACJ,CACF,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,UAAU,GAAG,aAAa,CAAC;QACjC,MAAM,aAAa,GAAG,GAAG,CAAC;QAE1B,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,IAAA,YAAM,EAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,CAC3C,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;IACvB,IAAA,WAAE,EAAC,oFAAoF,EAAE,KAAK,IAAI,EAAE;QAClG,MAAM,QAAQ,GAAG,WAAW,CAAC;QAE7B,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,qCAAqC,EAAE,WAAW,EAAE,GAAG,CAAC,EAC7D,IAAI,CAAC,qCAAqC,EAAE,WAAW,EAAE,GAAG,CAAC,EAC7D,IAAI,CAAC,qCAAqC,EAAE,WAAW,EAAE,GAAG,CAAC,EAC7D,IAAI,CAAC,qCAAqC,EAAE,WAAW,EAAE,GAAG,CAAC,EAC7D,IAAI,CAAC,qCAAqC,EAAE,QAAQ,CAAC,CACtD,CAAC;QAEF,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,MAAM,UAAU,GAAG,aAAa,CAAC;QACjC,MAAM,aAAa,GAAG,GAAG,CAAC;QAE1B,MAAM,MAAM,GAAG,MAAM,IAAA,aAAO,EAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE;YAC/D,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { HttpResponse, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, afterEach, beforeAll, describe, expect, it } from 'vitest';\nimport { getSpv, pollSpv } from '../spv';\n\nconst server = setupServer();\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\nconst post = (\n  path: string,\n  response: string | Record<string, unknown>,\n  status = 200,\n): ReturnType<typeof http.post> =>\n  http.post(\n    path,\n    () =>\n      typeof response === 'string'\n        ? new HttpResponse(response, { status })\n        : HttpResponse.json(response, { status }),\n    { once: true },\n  );\n\ndescribe('getSpv', () => {\n  it('calls /spv endpoint to generate spv for a request and a target chain', async () => {\n    const response = 'spv-proof';\n\n    server.resetHandlers(post('http://test-blockchain-host.com/spv', response));\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKey = 'request-key';\n    const targetChainId = '1';\n\n    const result = await getSpv(hostUrl, requestKey, targetChainId);\n\n    expect(result).toBe(response);\n  });\n\n  it('throws exception if spv function does not return string', async () => {\n    server.resetHandlers(\n      post(\n        'http://test-blockchain-host.com/spv',\n        'PROOF_IS_NOT_AVAILABLE',\n        500,\n      ),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKey = 'request-key';\n    const targetChainId = '1';\n\n    await expect(() =>\n      getSpv(hostUrl, requestKey, targetChainId),\n    ).rejects.toThrowError(new Error('PROOF_IS_NOT_AVAILABLE'));\n  });\n});\n\ndescribe('pollSpv', () => {\n  it('calls /spv endpoint several times to generate spv for a request and a target chain', async () => {\n    const response = 'spv-proof';\n\n    server.resetHandlers(\n      post('http://test-blockchain-host.com/spv', 'not found', 404),\n      post('http://test-blockchain-host.com/spv', 'not found', 404),\n      post('http://test-blockchain-host.com/spv', 'not found', 404),\n      post('http://test-blockchain-host.com/spv', 'not found', 404),\n      post('http://test-blockchain-host.com/spv', response),\n    );\n\n    const hostUrl = 'http://test-blockchain-host.com';\n\n    const requestKey = 'request-key';\n    const targetChainId = '1';\n\n    const result = await pollSpv(hostUrl, requestKey, targetChainId, {\n      interval: 10,\n    });\n\n    expect(result).toBe(response);\n  });\n});\n"]}