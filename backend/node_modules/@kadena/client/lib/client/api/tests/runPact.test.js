"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const chainweb_node_client_1 = require("@kadena/chainweb-node-client");
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const runPact_1 = require("../runPact");
// Hack to spy on exported function
vitest_1.vi.mock('@kadena/chainweb-node-client', async (importOriginal) => {
    const mod = await importOriginal();
    const local = vitest_1.vi.fn().mockImplementation(mod.local);
    return { ...mod, local };
});
const server = (0, node_1.setupServer)();
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
(0, vitest_1.describe)('runPact', () => {
    (0, vitest_1.beforeAll)(() => {
        vitest_1.vi.useFakeTimers().setSystemTime(new Date('2023-07-31'));
    });
    (0, vitest_1.afterAll)(() => {
        vitest_1.vi.useRealTimers();
    });
    (0, vitest_1.it)('create a complete pact command from the input and send it to the chain', async () => {
        const mockResponse = {};
        server.resetHandlers(msw_1.http.post('http://blockchain/api/v1/local', () => msw_1.HttpResponse.json(mockResponse), { once: true }));
        const result = await (0, runPact_1.runPact)('http://blockchain', '(+ 1 1)');
        (0, vitest_1.expect)(result).toStrictEqual(mockResponse);
        (0, vitest_1.expect)(chainweb_node_client_1.local).toBeCalledWith({
            cmd: '{"payload":{"exec":{"code":"(+ 1 1)","data":{}}},"nonce":"kjs:nonce:1690761600000","signers":[]}',
            hash: 'BFstB5srkwenVbxQYjMsdSIQiyaakhaYGjHA3ZKmntY',
            sigs: [],
        }, 'http://blockchain', { preflight: false, signatureVerification: false });
    });
});
//# sourceMappingURL=runPact.test.js.map