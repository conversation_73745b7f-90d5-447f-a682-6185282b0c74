"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const status_1 = require("../status");
const server = (0, node_1.setupServer)();
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
const post = (path, response, status = 200, wait) => msw_1.http.post(path, async () => {
    await (0, msw_1.delay)(wait !== null && wait !== void 0 ? wait : 0);
    return typeof response === 'string'
        ? new msw_1.HttpResponse(response, { status })
        : msw_1.HttpResponse.json(response, { status });
}, { once: true });
(0, vitest_1.describe)('pollStatus', () => {
    (0, vitest_1.it)('calls the /poll endpoint several times till it gets the status of all request keys', async () => {
        const responses = [
            {},
            { 'key-1': { reqKey: 'key-1' } },
            {},
            { 'key-2': { reqKey: 'key-2' } },
        ];
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/poll', responses[0]), post('http://test-blockchain-host.com/api/v1/poll', responses[1]), post('http://test-blockchain-host.com/api/v1/poll', responses[2]), post('http://test-blockchain-host.com/api/v1/poll', responses[3]));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKeys = ['key-1', 'key-2'];
        const result = await (0, status_1.pollStatus)(hostUrl, requestKeys, {
            interval: 10,
        });
        (0, vitest_1.expect)(result).toEqual({
            'key-1': { reqKey: 'key-1' },
            'key-2': { reqKey: 'key-2' },
        });
    });
    (0, vitest_1.it)('throws TIME_OUT_REJECT if the task get longer than set in timeout option', async () => {
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/poll', {}, 200, 75));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKeys = ['key-1', 'key-2'];
        const promise = (0, status_1.pollStatus)(hostUrl, requestKeys, {
            interval: 10,
            timeout: 50,
        });
        await (0, vitest_1.expect)(promise).rejects.toEqual(new Error('TIME_OUT_REJECT'));
    });
    (0, vitest_1.it)('calls onPoll call back before fetching each request key in each try', async () => {
        const responses = [
            {},
            { 'key-1': { reqKey: 'key-1' } },
            {},
            { 'key-2': { reqKey: 'key-2' } },
        ];
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/poll', responses[0]), post('http://test-blockchain-host.com/api/v1/poll', responses[1]), post('http://test-blockchain-host.com/api/v1/poll', responses[2]), post('http://test-blockchain-host.com/api/v1/poll', responses[3]));
        const onPoll = vitest_1.vi.fn();
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKeys = ['key-1', 'key-2'];
        await (0, status_1.pollStatus)(hostUrl, requestKeys, {
            interval: 10,
            onPoll,
        });
        (0, vitest_1.expect)(onPoll).toBeCalledTimes(6);
        // first try
        (0, vitest_1.expect)(onPoll.mock.calls[0][0]).toBe('key-1');
        (0, vitest_1.expect)(onPoll.mock.calls[1][0]).toBe('key-2');
        //second try that returns key-1 status
        (0, vitest_1.expect)(onPoll.mock.calls[2][0]).toBe('key-1');
        (0, vitest_1.expect)(onPoll.mock.calls[3][0]).toBe('key-2');
        // third try that
        (0, vitest_1.expect)(onPoll.mock.calls[4][0]).toBe('key-2');
        //forth try that returns key-2 status
        (0, vitest_1.expect)(onPoll.mock.calls[5][0]).toBe('key-2');
    });
    (0, vitest_1.it)("uses default options if they aren't provided", async () => {
        const responses = [
            { 'key-1': { reqKey: 'key-1' }, 'key-2': { reqKey: 'key-2' } },
        ];
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/poll', responses[0]));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKeys = ['key-1', 'key-2'];
        const result = await (0, status_1.pollStatus)(hostUrl, requestKeys);
        (0, vitest_1.expect)(result).toEqual({
            'key-1': { reqKey: 'key-1' },
            'key-2': { reqKey: 'key-2' },
        });
    });
    (0, vitest_1.it)('calls onResult call back after each request key is fetched', async () => {
        const responses = [
            {},
            { 'key-1': { reqKey: 'key-1' } },
            {},
            { 'key-2': { reqKey: 'key-2' } },
        ];
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/poll', responses[0]), post('http://test-blockchain-host.com/api/v1/poll', responses[1]), post('http://test-blockchain-host.com/api/v1/poll', responses[2]), post('http://test-blockchain-host.com/api/v1/poll', responses[3]));
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKeys = ['key-1', 'key-2'];
        const onResult = vitest_1.vi.fn();
        await (0, status_1.pollStatus)(hostUrl, requestKeys, { interval: 10, onResult });
        (0, vitest_1.expect)(onResult).toBeCalledTimes(2);
        (0, vitest_1.expect)(onResult.mock.calls[0]).toEqual(['key-1', { reqKey: 'key-1' }]);
        (0, vitest_1.expect)(onResult.mock.calls[1]).toEqual(['key-2', { reqKey: 'key-2' }]);
    });
    (0, vitest_1.it)('calls onPoll call back with error if the request fails', async () => {
        server.resetHandlers(post('http://test-blockchain-host.com/api/v1/poll', {}));
        const onPoll = vitest_1.vi.fn();
        const hostUrl = 'http://test-blockchain-host.com';
        const requestKeys = ['key-1', 'key-2'];
        await (0, vitest_1.expect)((0, status_1.pollStatus)(hostUrl, requestKeys, { interval: 10, timeout: 50, onPoll })).rejects.toEqual(new Error('TIME_OUT_REJECT'));
        (0, vitest_1.expect)(onPoll.mock.calls.at(-1)[1] instanceof Error).toBe(true);
    });
});
//# sourceMappingURL=status.test.js.map