{"version": 3, "file": "runPact.js", "sourceRoot": "", "sources": ["../../../src/client/api/runPact.ts"], "names": [], "mappings": ";;;AAIA,uEAAqD;AACrD,mEAA+D;AAC/D,iEAAyE;AAEzE,SAAgB,OAAO,CACrB,OAAe,EACf,IAAY,EACZ,OAAgC,EAAE,EAClC,WAA+B;IAE/B,MAAM,WAAW,GAAG,IAAA,uCAAkB,EAAC,IAAA,8BAAS,EAAC,IAAI,CAAC,EAAE;QACtD,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;KAC5B,CAAC,EAAE,CAAC;IAEL,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IACxC,OAAO,IAAA,4BAAK,EACV;QACE,GAAG;QACH,IAAI,EAAE,IAAA,yBAAS,EAAC,GAAG,CAAC;QACpB,IAAI,EAAE,EAAE;KACT,EACD,OAAO,EACP;QACE,SAAS,EAAE,KAAK;QAChB,qBAAqB,EAAE,KAAK;QAC5B,GAAG,WAAW;KACf,CACF,CAAC;AACJ,CAAC;AAxBD,0BAwBC", "sourcesContent": ["import type {\n  ClientRequestInit,\n  ICommandResult,\n} from '@kadena/chainweb-node-client';\nimport { local } from '@kadena/chainweb-node-client';\nimport { hash as blackHash } from '@kadena/cryptography-utils';\nimport { composePactCommand, execution } from '../../composePactCommand';\n\nexport function runPact(\n  hostUrl: string,\n  code: string,\n  data: Record<string, unknown> = {},\n  requestInit?: ClientRequestInit,\n): Promise<ICommandResult> {\n  const pactCommand = composePactCommand(execution(code), {\n    payload: { exec: { data } },\n  })();\n\n  const cmd = JSON.stringify(pactCommand);\n  return local(\n    {\n      cmd,\n      hash: blackHash(cmd),\n      sigs: [],\n    },\n    hostUrl,\n    {\n      preflight: false,\n      signatureVerification: false,\n      ...requestInit,\n    },\n  );\n}\n"]}