{"version": 3, "file": "spv.js", "sourceRoot": "", "sources": ["../../../src/client/api/spv.ts"], "names": [], "mappings": ";;;AAIA,uEAAmD;AAGnD,0CAAuC;AAEhC,KAAK,UAAU,MAAM,CAC1B,IAAY,EACZ,UAAkB,EAClB,aAAsB,EACtB,cAAiC,EAAE;IAEnC,MAAM,KAAK,GAAG,MAAM,IAAA,0BAAG,EAAC,EAAE,UAAU,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IAC1E,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;IACzE,OAAO,KAAK,CAAC;AACf,CAAC;AATD,wBASC;AAEM,MAAM,OAAO,GAAG,CACrB,IAAY,EACZ,UAAkB,EAClB,aAAsB,EACtB,cAA6B,EACP,EAAE;IACxB,MAAM,IAAI,GAAG,KAAK,IAA0B,EAAE,CAC5C,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAE1D,MAAM,QAAQ,GAAG,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;IAE7B,OAAO,QAAQ,CAAC,cAAc,CAAC,CAAC;AAClC,CAAC,CAAC;AAZW,QAAA,OAAO,WAYlB", "sourcesContent": ["import type {\n  ClientRequestInit,\n  SPVResponse,\n} from '@kadena/chainweb-node-client';\nimport { spv } from '@kadena/chainweb-node-client';\nimport type { ChainId } from '@kadena/types';\nimport type { IPollOptions } from '../interfaces/interfaces';\nimport { retry } from '../utils/retry';\n\nexport async function getSpv(\n  host: string,\n  requestKey: string,\n  targetChainId: ChainId,\n  requestInit: ClientRequestInit = {},\n): Promise<SPVResponse> {\n  const proof = await spv({ requestKey, targetChainId }, host, requestInit);\n  if (typeof proof !== 'string') throw new Error('PROOF_IS_NOT_AVAILABLE');\n  return proof;\n}\n\nexport const pollSpv = (\n  host: string,\n  requestKey: string,\n  targetChainId: ChainId,\n  pollingOptions?: IPollOptions,\n): Promise<SPVResponse> => {\n  const task = async (): Promise<SPVResponse> =>\n    getSpv(host, requestKey, targetChainId, pollingOptions);\n\n  const retrySpv = retry(task);\n\n  return retrySpv(pollingOptions);\n};\n"]}