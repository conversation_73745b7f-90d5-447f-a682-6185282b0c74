{"version": 3, "file": "utils.test.js", "sourceRoot": "", "sources": ["../../src/tests/utils.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,gEAA6D,CAAC,sCAAsC;AAEpG,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,WAAE,EAAC,oFAAoF,EAAE,GAAG,EAAE;QAC5F,MAAM,GAAG,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gFAAgF,EAAE,GAAG,EAAE;QACxF,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACzB,MAAM,MAAM,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qFAAqF,EAAE,GAAG,EAAE;QAC7F,MAAM,GAAG,GAAG,IAAI,CAAC;QACjB,MAAM,MAAM,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;QAC7C,MAAM,GAAG,GAAG,OAAO,CAAC;QACpB,MAAM,MAAM,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,MAAM,GAAG,GAAG,GAAW,EAAE,CAAC,SAAS,CAAC;QACpC,MAAM,MAAM,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QACrC,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;QAC9C,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAA,eAAM,EAAC,GAAG,EAAE;YACV,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { parseAsPactValue } from '../utils/parseAsPactValue'; // Replace with the actual module path\n\ndescribe('parseType', () => {\n  it('returns the decimal value when input is a PactLiteral object with decimal property', () => {\n    const arg = { decimal: '10' };\n    const result = parseAsPactValue(arg);\n    expect(result).toBe('10.0');\n  });\n\n  it('returns the integer value when input is a PactLiteral object with int property', () => {\n    const arg = { int: '5' };\n    const result = parseAsPactValue(arg);\n    expect(result).toBe('5');\n  });\n\n  it('returns the input argument if it is neither an object, number, string, nor function', () => {\n    const arg = true;\n    const result = parseAsPactValue(arg);\n    expect(result).toBe('true');\n  });\n\n  it('wraps string input in double quotes', () => {\n    const arg = 'hello';\n    const result = parseAsPactValue(arg);\n    expect(result).toBe('\"hello\"');\n  });\n\n  it('invokes the function and return its result', () => {\n    const arg = (): string => 'dynamic';\n    const result = parseAsPactValue(arg);\n    expect(result).toBe('dynamic');\n  });\n\n  it('throws an error if input is a number', () => {\n    const arg = 42;\n    expect(() => {\n      parseAsPactValue(arg);\n    }).toThrow('Type `number` is not allowed');\n  });\n});\n"]}