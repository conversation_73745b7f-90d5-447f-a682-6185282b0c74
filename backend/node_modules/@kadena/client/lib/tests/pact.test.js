"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const pact_1 = require("../pact");
const pact_helpers_1 = require("../utils/pact-helpers");
(0, vitest_1.describe)('Pact.modules', () => {
    (0, vitest_1.it)('returns pact string equivalent of a function call', () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const code = pact_1.Pact.modules.coin.transfer('alice', 'bob', {
            decimal: '1',
        });
        (0, vitest_1.expect)(code).toBe('(coin.transfer "alice" "bob" 1.0)');
    });
    (0, vitest_1.it)('returns pact string equivalent of a defpact call', () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const code = pact_1.Pact.modules.coin.defpact['transfer-crosschain']('alice', 'bob', () => 'myGuard', '1', { decimal: '1' });
        (0, vitest_1.expect)(code).toBe('(coin.transfer-crosschain "alice" "bob" myGuard "1" 1.0)');
    });
    (0, vitest_1.it)('returns code for time and module reference', () => {
        const now = new Date('2023-07-19T10:04:15.599Z');
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const code = pact_1.Pact.modules.crowdfund['create-project']('id', 'an awesome project', (0, pact_helpers_1.literal)('coin'), { decimal: '1000' }, { decimal: '800' }, now, new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), 'bob', (0, pact_helpers_1.readKeyset)('ks'));
        (0, vitest_1.expect)(code).toBe('(crowdfund.create-project "id" "an awesome project" coin 1000.0 800.0 (time "2023-07-19T10:04:15Z") (time "2023-08-18T10:04:15Z") "bob" (read-keyset "ks"))');
    });
});
//# sourceMappingURL=pact.test.js.map