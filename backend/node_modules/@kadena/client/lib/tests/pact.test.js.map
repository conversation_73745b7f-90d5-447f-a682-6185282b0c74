{"version": 3, "file": "pact.test.js", "sourceRoot": "", "sources": ["../../src/tests/pact.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,kCAA+B;AAC/B,wDAA4D;AAE5D,IAAA,iBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAA,WAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,8DAA8D;QAC9D,MAAM,IAAI,GAAI,WAAI,CAAC,OAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;YAC/D,OAAO,EAAE,GAAG;SACb,CAAC,CAAC;QACH,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kDAAkD,EAAE,GAAG,EAAE;QAC1D,8DAA8D;QAC9D,MAAM,IAAI,GAAI,WAAI,CAAC,OAAe,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CACpE,OAAO,EACP,KAAK,EACL,GAAG,EAAE,CAAC,SAAS,EACf,GAAG,EACH,EAAE,OAAO,EAAE,GAAG,EAAE,CACjB,CAAC;QACF,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,IAAI,CACf,0DAA0D,CAC3D,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACjD,8DAA8D;QAC9D,MAAM,IAAI,GAAI,WAAI,CAAC,OAAe,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAC5D,IAAI,EACJ,oBAAoB,EACpB,IAAA,sBAAO,EAAC,MAAM,CAAC,EACf,EAAE,OAAO,EAAE,MAAM,EAAE,EACnB,EAAE,OAAO,EAAE,KAAK,EAAE,EAClB,GAAG,EACH,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAClD,KAAK,EACL,IAAA,yBAAU,EAAC,IAAI,CAAC,CACjB,CAAC;QACF,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,IAAI,CACf,6JAA6J,CAC9J,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { Pact } from '../pact';\nimport { literal, readKeyset } from '../utils/pact-helpers';\n\ndescribe('Pact.modules', () => {\n  it('returns pact string equivalent of a function call', () => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const code = (Pact.modules as any).coin.transfer('alice', 'bob', {\n      decimal: '1',\n    });\n    expect(code).toBe('(coin.transfer \"alice\" \"bob\" 1.0)');\n  });\n\n  it('returns pact string equivalent of a defpact call', () => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const code = (Pact.modules as any).coin.defpact['transfer-crosschain'](\n      'alice',\n      'bob',\n      () => 'myGuard',\n      '1',\n      { decimal: '1' },\n    );\n    expect(code).toBe(\n      '(coin.transfer-crosschain \"alice\" \"bob\" myGuard \"1\" 1.0)',\n    );\n  });\n\n  it('returns code for time and module reference', () => {\n    const now = new Date('2023-07-19T10:04:15.599Z');\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const code = (Pact.modules as any).crowdfund['create-project'](\n      'id',\n      'an awesome project',\n      literal('coin'),\n      { decimal: '1000' },\n      { decimal: '800' },\n      now,\n      new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),\n      'bob',\n      readKeyset('ks'),\n    );\n    expect(code).toBe(\n      '(crowdfund.create-project \"id\" \"an awesome project\" coin 1000.0 800.0 (time \"2023-07-19T10:04:15Z\") (time \"2023-08-18T10:04:15Z\") \"bob\" (read-keyset \"ks\"))',\n    );\n  });\n});\n"]}