"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const parseAsPactValue_1 = require("../utils/parseAsPactValue"); // Replace with the actual module path
(0, vitest_1.describe)('parseType', () => {
    (0, vitest_1.it)('returns the decimal value when input is a PactLiteral object with decimal property', () => {
        const arg = { decimal: '10' };
        const result = (0, parseAsPactValue_1.parseAsPactValue)(arg);
        (0, vitest_1.expect)(result).toBe('10.0');
    });
    (0, vitest_1.it)('returns the integer value when input is a PactLiteral object with int property', () => {
        const arg = { int: '5' };
        const result = (0, parseAsPactValue_1.parseAsPactValue)(arg);
        (0, vitest_1.expect)(result).toBe('5');
    });
    (0, vitest_1.it)('returns the input argument if it is neither an object, number, string, nor function', () => {
        const arg = true;
        const result = (0, parseAsPactValue_1.parseAsPactValue)(arg);
        (0, vitest_1.expect)(result).toBe('true');
    });
    (0, vitest_1.it)('wraps string input in double quotes', () => {
        const arg = 'hello';
        const result = (0, parseAsPactValue_1.parseAsPactValue)(arg);
        (0, vitest_1.expect)(result).toBe('"hello"');
    });
    (0, vitest_1.it)('invokes the function and return its result', () => {
        const arg = () => 'dynamic';
        const result = (0, parseAsPactValue_1.parseAsPactValue)(arg);
        (0, vitest_1.expect)(result).toBe('dynamic');
    });
    (0, vitest_1.it)('throws an error if input is a number', () => {
        const arg = 42;
        (0, vitest_1.expect)(() => {
            (0, parseAsPactValue_1.parseAsPactValue)(arg);
        }).toThrow('Type `number` is not allowed');
    });
});
//# sourceMappingURL=utils.test.js.map