{"version": 3, "file": "parseTransactionCommand.js", "sourceRoot": "", "sources": ["../../../src/signing/utils/parseTransactionCommand.ts"], "names": [], "mappings": ";;;AAGA;;;;GAIG;AACI,MAAM,uBAAuB,GAEhB,CAAC,WAAW,EAAE,EAAE;IAClC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC,CAAC;AAJW,QAAA,uBAAuB,2BAIlC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\n\n/**\n * parse a ICommand or IUnsignedCommand JSON object to IPactCommand\n *\n * @internal\n */\nexport const parseTransactionCommand: (\n  transaction: IUnsignedCommand | ICommand,\n) => IPactCommand = (transaction) => {\n  return JSON.parse(transaction.cmd);\n};\n"]}