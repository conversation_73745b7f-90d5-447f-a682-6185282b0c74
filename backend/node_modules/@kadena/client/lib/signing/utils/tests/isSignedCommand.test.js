"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const isSignedTransaction_1 = require("../isSignedTransaction");
(0, vitest_1.describe)('isSignedCommand', () => {
    (0, vitest_1.it)('returns true if command is signed', () => {
        const command = {
            cmd: 'cmd',
            hash: 'hash',
            sigs: [{ sig: 'sig' }],
        };
        (0, vitest_1.expect)((0, isSignedTransaction_1.isSignedTransaction)(command)).toBe(true);
    });
    (0, vitest_1.it)('returns false if command is not signed', () => {
        const command = {
            cmd: 'cmd',
            hash: 'hash',
            sigs: [undefined],
        };
        (0, vitest_1.expect)((0, isSignedTransaction_1.isSignedTransaction)(command)).toBe(false);
    });
});
//# sourceMappingURL=isSignedCommand.test.js.map