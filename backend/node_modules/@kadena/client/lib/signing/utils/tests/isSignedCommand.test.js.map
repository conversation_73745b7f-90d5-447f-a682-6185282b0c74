{"version": 3, "file": "isSignedCommand.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/utils/tests/isSignedCommand.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,gEAA6D;AAE7D,IAAA,iBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAA,WAAE,EAAC,mCAAmC,EAAE,GAAG,EAAE;QAC3C,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;SACvB,CAAC;QACF,IAAA,eAAM,EAAC,IAAA,yCAAmB,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;QAChD,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,SAAS,CAAC;SAClB,CAAC;QACF,IAAA,eAAM,EAAC,IAAA,yCAAmB,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { isSignedTransaction } from '../isSignedTransaction';\n\ndescribe('isSignedCommand', () => {\n  it('returns true if command is signed', () => {\n    const command = {\n      cmd: 'cmd',\n      hash: 'hash',\n      sigs: [{ sig: 'sig' }],\n    };\n    expect(isSignedTransaction(command)).toBe(true);\n  });\n\n  it('returns false if command is not signed', () => {\n    const command = {\n      cmd: 'cmd',\n      hash: 'hash',\n      sigs: [undefined],\n    };\n    expect(isSignedTransaction(command)).toBe(false);\n  });\n});\n"]}