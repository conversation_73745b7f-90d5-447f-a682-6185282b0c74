"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const addSignatures_1 = require("../addSignatures");
(0, vitest_1.describe)('addSignature', () => {
    (0, vitest_1.it)('returns a new transaction object by adding the input signatures to the sigs array', () => {
        const command = {
            payload: { exec: { code: 'some-pact-code', data: {} } },
            signers: [
                {
                    pubKey: 'first_public_key',
                },
                {
                    pubKey: 'second_public_key',
                },
            ],
        };
        const originalTr = {
            cmd: JSON.stringify(command),
            hash: 'test-hash',
            sigs: [undefined, undefined],
        };
        const withFirstSig = (0, addSignatures_1.addSignatures)(originalTr, {
            pubKey: 'first_public_key',
            sig: 'first-sig',
        });
        (0, vitest_1.expect)(withFirstSig).not.toBe(originalTr);
        (0, vitest_1.expect)(withFirstSig).toStrictEqual({
            cmd: JSON.stringify(command),
            hash: 'test-hash',
            sigs: [
                { sig: 'first-sig', pubKey: 'first_public_key' },
                { pubKey: 'second_public_key' },
            ],
        });
        const withAllSigs = (0, addSignatures_1.addSignatures)(withFirstSig, {
            pubKey: 'second_public_key',
            sig: 'second-sig',
        });
        (0, vitest_1.expect)(withAllSigs).toStrictEqual({
            cmd: JSON.stringify(command),
            hash: 'test-hash',
            sigs: [
                { sig: 'first-sig', pubKey: 'first_public_key' },
                { sig: 'second-sig', pubKey: 'second_public_key' },
            ],
        });
    });
    (0, vitest_1.it)('returns a new transaction object by adding the input signatures without public key to the sigs array', () => {
        const command = {
            payload: { exec: { code: 'some-pact-code', data: {} } },
            signers: [
                {
                    pubKey: 'first_public_key',
                },
                {
                    pubKey: 'second_public_key',
                },
            ],
        };
        const originalTr = {
            cmd: JSON.stringify(command),
            hash: 'test-hash',
            sigs: [undefined, undefined],
        };
        const withAllSigs = (0, addSignatures_1.addSignatures)(originalTr, {
            sig: 'first-sig',
        }, {
            sig: 'second-sig',
        });
        (0, vitest_1.expect)(withAllSigs).toStrictEqual({
            cmd: JSON.stringify(command),
            hash: 'test-hash',
            sigs: [
                { sig: 'first-sig', pubKey: 'first_public_key' },
                { sig: 'second-sig', pubKey: 'second_public_key' },
            ],
        });
    });
    (0, vitest_1.it)('throws an exception if signatures are not matched', () => {
        const command = {
            payload: { exec: { code: 'some-pact-code', data: {} } },
            signers: [
                {
                    pubKey: 'first_public_key',
                },
                {
                    pubKey: 'second_public_key',
                },
            ],
        };
        const originalTr = {
            cmd: JSON.stringify(command),
            hash: 'test-hash',
            sigs: [undefined, undefined],
        };
        (0, vitest_1.expect)(() => (0, addSignatures_1.addSignatures)(originalTr, { sig: '' }, { sig: '' }, { sig: '' })).toThrowError();
    });
});
//# sourceMappingURL=addSignature.test.js.map