{"version": 3, "file": "addSignature.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/utils/tests/addSignature.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAE9C,oDAAiD;AAEjD,IAAA,iBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAA,WAAE,EAAC,mFAAmF,EAAE,GAAG,EAAE;QAC3F,MAAM,OAAO,GAA0B;YACrC,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;YACvD,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,kBAAkB;iBAC3B;gBACD;oBACE,MAAM,EAAE,mBAAmB;iBAC5B;aACF;SACF,CAAC;QACF,MAAM,UAAU,GAAG;YACjB,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;SAC7B,CAAC;QAEF,MAAM,YAAY,GAAG,IAAA,6BAAa,EAAC,UAAU,EAAE;YAC7C,MAAM,EAAE,kBAAkB;YAC1B,GAAG,EAAE,WAAW;SACjB,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,YAAY,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE1C,IAAA,eAAM,EAAC,YAAY,CAAC,CAAC,aAAa,CAAC;YACjC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE;gBACJ,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBAChD,EAAE,MAAM,EAAE,mBAAmB,EAAE;aAChC;SACF,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,IAAA,6BAAa,EAAC,YAAY,EAAE;YAC9C,MAAM,EAAE,mBAAmB;YAC3B,GAAG,EAAE,YAAY;SAClB,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,WAAW,CAAC,CAAC,aAAa,CAAC;YAChC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE;gBACJ,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBAChD,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,EAAE,mBAAmB,EAAE;aACnD;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sGAAsG,EAAE,GAAG,EAAE;QAC9G,MAAM,OAAO,GAA0B;YACrC,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;YACvD,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,kBAAkB;iBAC3B;gBACD;oBACE,MAAM,EAAE,mBAAmB;iBAC5B;aACF;SACF,CAAC;QACF,MAAM,UAAU,GAAG;YACjB,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;SAC7B,CAAC;QAEF,MAAM,WAAW,GAAG,IAAA,6BAAa,EAC/B,UAAU,EACV;YACE,GAAG,EAAE,WAAW;SACjB,EACD;YACE,GAAG,EAAE,YAAY;SAClB,CACF,CAAC;QAEF,IAAA,eAAM,EAAC,WAAW,CAAC,CAAC,aAAa,CAAC;YAChC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE;gBACJ,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBAChD,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,EAAE,mBAAmB,EAAE;aACnD;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,MAAM,OAAO,GAA0B;YACrC,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;YACvD,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,kBAAkB;iBAC3B;gBACD;oBACE,MAAM,EAAE,mBAAmB;iBAC5B;aACF;SACF,CAAC;QACF,MAAM,UAAU,GAAG;YACjB,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;SAC7B,CAAC;QACF,IAAA,eAAM,EAAC,GAAG,EAAE,CACV,IAAA,6BAAa,EAAC,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CACjE,CAAC,YAAY,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport type { IPactCommand } from '../../../interfaces/IPactCommand';\nimport { addSignatures } from '../addSignatures';\n\ndescribe('addSignature', () => {\n  it('returns a new transaction object by adding the input signatures to the sigs array', () => {\n    const command: Partial<IPactCommand> = {\n      payload: { exec: { code: 'some-pact-code', data: {} } },\n      signers: [\n        {\n          pubKey: 'first_public_key',\n        },\n        {\n          pubKey: 'second_public_key',\n        },\n      ],\n    };\n    const originalTr = {\n      cmd: JSON.stringify(command),\n      hash: 'test-hash',\n      sigs: [undefined, undefined],\n    };\n\n    const withFirstSig = addSignatures(originalTr, {\n      pubKey: 'first_public_key',\n      sig: 'first-sig',\n    });\n\n    expect(withFirstSig).not.toBe(originalTr);\n\n    expect(withFirstSig).toStrictEqual({\n      cmd: JSON.stringify(command),\n      hash: 'test-hash',\n      sigs: [\n        { sig: 'first-sig', pubKey: 'first_public_key' },\n        { pubKey: 'second_public_key' },\n      ],\n    });\n\n    const withAllSigs = addSignatures(withFirstSig, {\n      pubKey: 'second_public_key',\n      sig: 'second-sig',\n    });\n\n    expect(withAllSigs).toStrictEqual({\n      cmd: JSON.stringify(command),\n      hash: 'test-hash',\n      sigs: [\n        { sig: 'first-sig', pubKey: 'first_public_key' },\n        { sig: 'second-sig', pubKey: 'second_public_key' },\n      ],\n    });\n  });\n\n  it('returns a new transaction object by adding the input signatures without public key to the sigs array', () => {\n    const command: Partial<IPactCommand> = {\n      payload: { exec: { code: 'some-pact-code', data: {} } },\n      signers: [\n        {\n          pubKey: 'first_public_key',\n        },\n        {\n          pubKey: 'second_public_key',\n        },\n      ],\n    };\n    const originalTr = {\n      cmd: JSON.stringify(command),\n      hash: 'test-hash',\n      sigs: [undefined, undefined],\n    };\n\n    const withAllSigs = addSignatures(\n      originalTr,\n      {\n        sig: 'first-sig',\n      },\n      {\n        sig: 'second-sig',\n      },\n    );\n\n    expect(withAllSigs).toStrictEqual({\n      cmd: JSON.stringify(command),\n      hash: 'test-hash',\n      sigs: [\n        { sig: 'first-sig', pubKey: 'first_public_key' },\n        { sig: 'second-sig', pubKey: 'second_public_key' },\n      ],\n    });\n  });\n  it('throws an exception if signatures are not matched', () => {\n    const command: Partial<IPactCommand> = {\n      payload: { exec: { code: 'some-pact-code', data: {} } },\n      signers: [\n        {\n          pubKey: 'first_public_key',\n        },\n        {\n          pubKey: 'second_public_key',\n        },\n      ],\n    };\n    const originalTr = {\n      cmd: JSON.stringify(command),\n      hash: 'test-hash',\n      sigs: [undefined, undefined],\n    };\n    expect(() =>\n      addSignatures(originalTr, { sig: '' }, { sig: '' }, { sig: '' }),\n    ).toThrowError();\n  });\n});\n"]}