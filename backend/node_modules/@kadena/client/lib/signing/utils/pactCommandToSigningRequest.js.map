{"version": 3, "file": "pactCommandToSigningRequest.js", "sourceRoot": "", "sources": ["../../../src/signing/utils/pactCommandToSigningRequest.ts"], "names": [], "mappings": ";;;AACA,kEAA+D;AAGxD,MAAM,2BAA2B,GAAG,CACzC,iBAA+B,EACd,EAAE;;IACnB,IAAI,CAAC,IAAA,6BAAa,EAAC,iBAAiB,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED,OAAO;QACL,IAAI,EAAE,MAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,mCAAI,EAAE;QAC/C,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAkC;QACvE,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACjD,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;gBACzC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAEhC,OAAO;oBACL,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;oBACjC,WAAW,EAAE,mBAAmB,IAAI,EAAE;oBACtC,GAAG,EAAE;wBACH,IAAI;wBACJ,IAAI;qBACL;iBACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QACF,KAAK,EAAE,iBAAiB,CAAC,KAAK;QAC9B,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC,OAAO;QACvC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ;QACzC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ;QACzC,MAAM,EAAE,iBAAiB,CAAC,IAAI,CAAC,MAAM;QACrC,GAAG,EAAE,iBAAiB,CAAC,IAAI,CAAC,GAAG;KAChC,CAAC;AACJ,CAAC,CAAC;AAlCW,QAAA,2BAA2B,+BAkCtC", "sourcesContent": ["import type { IPactCommand } from '../../interfaces/IPactCommand';\nimport { isExecCommand } from '../../interfaces/isExecCommand';\nimport type { ISigningRequest } from '../../interfaces/ISigningRequest';\n\nexport const pactCommandToSigningRequest = (\n  parsedTransaction: IPactCommand,\n): ISigningRequest => {\n  if (!isExecCommand(parsedTransaction)) {\n    throw new Error('`cont` transactions are not supported');\n  }\n\n  return {\n    code: parsedTransaction.payload.exec.code ?? '',\n    data: parsedTransaction.payload.exec.data as { [key: string]: unknown },\n    caps: parsedTransaction.signers.flatMap((signer) => {\n      if (signer.clist === undefined) {\n        return [];\n      }\n      return signer.clist.map(({ name, args }) => {\n        const nameArr = name.split('.');\n\n        return {\n          role: nameArr[nameArr.length - 1],\n          description: `Description for ${name}`,\n          cap: {\n            name,\n            args,\n          },\n        };\n      });\n    }),\n    nonce: parsedTransaction.nonce,\n    chainId: parsedTransaction.meta.chainId,\n    gasLimit: parsedTransaction.meta.gasLimit,\n    gasPrice: parsedTransaction.meta.gasPrice,\n    sender: parsedTransaction.meta.sender,\n    ttl: parsedTransaction.meta.ttl,\n  };\n};\n"]}