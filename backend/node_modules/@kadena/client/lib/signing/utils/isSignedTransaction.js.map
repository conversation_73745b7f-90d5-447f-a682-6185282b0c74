{"version": 3, "file": "isSignedTransaction.js", "sourceRoot": "", "sources": ["../../../src/signing/utils/isSignedTransaction.ts"], "names": [], "mappings": ";;;AAEA;;;;;;;GAOG;AACH,SAAgB,mBAAmB,CACjC,OAAoC;IAEpC,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,GAAG,MAAK,SAAS,CAAC,CAAC;AACzD,CAAC;AAJD,kDAIC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\n\n/**\n * Determines if a command is fully signed.\n *\n * @param command - The command to check.\n * @returns True if the command is signed, false otherwise.\n\n * @public\n */\nexport function isSignedTransaction(\n  command: IUnsignedCommand | ICommand,\n): command is ICommand {\n  return command.sigs.every((s) => s?.sig !== undefined);\n}\n"]}