{"version": 3, "file": "addSignatures.js", "sourceRoot": "", "sources": ["../../../src/signing/utils/addSignatures.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA2B;AAE3B,uEAAoE;AAEpE,MAAM,KAAK,GAAa,IAAA,eAAM,EAAC,qCAAqC,CAAC,CAAC;AAEtE;;;;GAIG;AACI,MAAM,aAAa,GAGS,CAAC,WAAW,EAAE,GAAG,UAAU,EAAE,EAAE;IAChE,KAAK,CAAC;iBACS,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAE5C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;IACxC,MAAM,iBAAiB,GAAG,IAAA,iDAAuB,EAAC,WAAW,CAAC,CAAC;IAC/D,MAAM,WAAW,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7E,IAAI,wBAAwB,CAAC,UAAU,CAAC,EAAE,CAAC;QACzC,yDAAyD;QACzD,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAE5C,OAAO;YACL,GAAG;YACH,IAAI;YACJ,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM,MAAK,MAAM,CAAC,CAAC;gBAC3D,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;oBAC3B,OAAO,OAAO,CAAC;gBACjB,CAAC;gBACD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAC/B,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,KAAK,MAAM,CAC3C,CAAC;gBACF,OAAO;oBACL,MAAM;oBACN,GAAG,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,GAAG,EAAC,CAAC,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;iBAClD,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;SAAM,IAAI,wBAAwB,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE,CAAC;QACnE,+EAA+E;QAC/E,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACxD,OAAO;YACL,GAAG;YACH,IAAI;YACJ,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;gBAAC,OAAA,CAAC;oBACpC,MAAM;oBACN,GAAG,EAAE,MAAA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,mCAAI,MAAA,IAAI,CAAC,CAAC,CAAC,0CAAE,GAAG;iBACvC,CAAC,CAAA;aAAA,CAAC;SACJ,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,iEAAiE;QACjE,MAAM,GAAG,GAAG,4HAA4H,CAAC;QACzI,KAAK,CAAC,GAAG,CAAC,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;AACH,CAAC,CAAC;AAjDW,QAAA,aAAa,iBAiDxB;AAEF,SAAS,wBAAwB,CAC/B,WAAyB,EACzB,IAAoD;IAEpD,OAAO,WAAW,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC;AACpD,CAAC;AAED,SAAS,wBAAwB,CAC/B,IAAoD;IAEpD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;AACvD,CAAC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\nimport type { Debugger } from 'debug';\nimport _debug from 'debug';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\nimport { parseTransactionCommand } from './parseTransactionCommand';\n\nconst debug: Debugger = _debug('@kadena/client:signing:addSignature');\n\n/**\n * adds signatures to an {@link @kadena/types#IUnsignedCommand | unsigned command}\n *\n * @public\n */\nexport const addSignatures: (\n  transaction: IUnsignedCommand,\n  ...signatures: { sig: string; pubKey?: string }[]\n) => IUnsignedCommand | ICommand = (transaction, ...signatures) => {\n  debug(`Adding signatures to transaction\n  transaction: ${JSON.stringify(transaction)}\n  signatures: ${JSON.stringify(signatures)}`);\n\n  const { cmd, hash, sigs } = transaction;\n  const parsedTransaction = parseTransactionCommand(transaction);\n  const pubKeyOrder = parsedTransaction.signers.map((signer) => signer.pubKey);\n  if (allSignaturesHavePubKeys(signatures)) {\n    // signatures have pubKeys, use pubKeys to identify order\n    debug(`Adding signatures based on pubKeys`);\n\n    return {\n      cmd,\n      hash,\n      sigs: pubKeyOrder.map((pubKey, i) => {\n        const existed = sigs.find((sig) => sig?.pubKey === pubKey);\n        if (existed && existed.sig) {\n          return existed;\n        }\n        const signature = signatures.find(\n          (signature) => signature.pubKey === pubKey,\n        );\n        return {\n          pubKey,\n          ...(signature?.sig ? { sig: signature.sig } : {}),\n        };\n      }),\n    };\n  } else if (signaturesMatchesSigners(parsedTransaction, signatures)) {\n    // signatures do not have pubKeys, but matching length, use order of signatures\n    debug(`Adding signatures based on order of signatures`);\n    return {\n      cmd,\n      hash,\n      sigs: pubKeyOrder.map((pubKey, i) => ({\n        pubKey,\n        sig: signatures[i].sig ?? sigs[i]?.sig,\n      })),\n    };\n  } else {\n    // signatures do not have pubKeys, and do not match length, ERROR\n    const msg = `Signatures do not have pubKeys, and the length of signatures, does not match the length of signers. Cannot add signatures.`;\n    debug(msg);\n    throw new Error(msg);\n  }\n};\n\nfunction signaturesMatchesSigners(\n  transaction: IPactCommand,\n  sigs: { sig: string; pubKey?: string | undefined }[],\n): boolean {\n  return transaction.signers.length === sigs.length;\n}\n\nfunction allSignaturesHavePubKeys(\n  sigs: { sig: string; pubKey?: string | undefined }[],\n): sigs is { sig: string; pubKey: string }[] {\n  return sigs.every((sig) => sig.pubKey !== undefined);\n}\n"]}