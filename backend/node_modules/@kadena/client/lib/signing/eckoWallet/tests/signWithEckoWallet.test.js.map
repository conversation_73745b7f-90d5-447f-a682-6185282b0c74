{"version": 3, "file": "signWithEckoWallet.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/eckoWallet/tests/signWithEckoWallet.test.ts"], "names": [], "mappings": ";;AAAA,mCAAwE;AAOxE,wEAAqE;AACrE,8DAA6D;AAE7D,+BAAgD;AAIhD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,WAAW,EAAX,kBAAW,EAAE,WAAW,EAAX,kBAAW,EAAE,CAAC,CAAC;AAEpD,IAAA,iBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,MAAM,cAAc,GAAG,GAAgB,EAAE,CAAC,CAAC;QACzC,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,IAAI,EAAE,oCAAoC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;SACF;QACD,IAAI,EAAE;YACJ,OAAO,EAAE,GAAG;YACZ,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,UAAU;YACpB,MAAM,EAAE,aAAa;YACrB,GAAG,EAAE,IAAI;YACT,YAAY,EAAE,SAAS;SACxB;QACD,OAAO,EAAE;YACP;gBACE,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,mBAAmB;wBACzB,IAAI,EAAE,CAAC,cAAc,CAAC;qBACvB;iBACF;aACF;SACF;QACD,SAAS,EAAE,iBAAiB;QAC5B,KAAK,EAAE,UAAU;KAClB,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;IAEhC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE;QACtC,KAAK,EAAE;YACL,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,eAAe;SACzB;QACD,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,eAAe,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,GAAG,EAAE;QACZ,eAAe,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACnC,eAAe,CAAC,iBAAiB,CAAC;YAChC,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE;SAC5D,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG,IAAA,yCAAoB,GAAE,CAAC;QAClD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,iBAAiB,GAAG,MAAM,kBAAkB,CAChD,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAC/B,CAAC;QAEF,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC;YAC3C,MAAM,EAAE,iBAAiB;YACzB,IAAI,EAAE;gBACJ,SAAS,EAAE,iBAAiB;gBAC5B,UAAU,EAAE;oBACV,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,eAAe;4BACrB,WAAW,EAAE,mCAAmC;4BAChD,GAAG,EAAE;gCACH,IAAI,EAAE,mBAAmB;gCACzB,IAAI,EAAE,CAAC,cAAc,CAAC;6BACvB;yBACF;qBACF;oBACD,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO;oBACjC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM;oBAC/B,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG;iBAC1B;aACF;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAA,eAAM,EAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;YAChC,GAAG,EAAE,UAAU;YACf,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;SAC5B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,kBAAkB,GAAG,IAAA,yCAAoB,GAAE,CAAC;QAElD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,uEAAuE;QACvE,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;QAEhC,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,kBAAkB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CACnD,CAAC,OAAO,CAAC,YAAY,CAAC,uCAAuC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;;QAClE,eAAe,CAAC,iBAAiB,CAAC;YAChC,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE;SAC5D,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG,IAAA,yCAAoB,GAAE,CAAC;QAElD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpC,MAAM,kBAAkB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CAAC;QAEzD,IAAA,eAAM,EAAC,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAAC,CAAC,oBAAoB,CAAC;YAClD,MAAM,EAAE,iBAAiB;YACzB,IAAI,EAAE;gBACJ,SAAS,EAAE,iBAAiB;gBAC5B,UAAU,EAAE;oBACV,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE,EAAE;oBACR,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO;oBACjC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM;oBAC/B,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG;iBAC1B;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,kBAAkB,GAAG,IAAA,yCAAoB,GAAE,CAAC;QAElD,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,kBAAkB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CACnD,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { afterAll, beforeEach, describe, expect, it, vi } from 'vitest';\n/** @vitest-environment jsdom */\n\nimport type {\n  IExecutionPayloadObject,\n  IPactCommand,\n} from '../../../interfaces/IPactCommand';\nimport { createTransaction } from '../../../utils/createTransaction';\nimport { createEckoWalletSign } from '../signWithEckoWallet';\n\nimport { TextDecoder, TextEncoder } from 'util';\n\ntype Transaction = IPactCommand & { payload: IExecutionPayloadObject };\n\nObject.assign(global, { TextDecoder, TextEncoder });\n\ndescribe('signWithEckoWallet', () => {\n  const getTransaction = (): Transaction => ({\n    payload: {\n      exec: {\n        code: '(coin.transfer \"bonnie\" \"clyde\" 1)',\n        data: {\n          test: 'test-data',\n        },\n      },\n    },\n    meta: {\n      chainId: '0',\n      gasLimit: 2300,\n      gasPrice: 0.00000001,\n      sender: 'test-sender',\n      ttl: 3600,\n      creationTime: 123456789,\n    },\n    signers: [\n      {\n        pubKey: '',\n        clist: [\n          {\n            name: 'cap.test-cap-name',\n            args: ['test-cap-arg'],\n          },\n        ],\n      },\n    ],\n    networkId: 'test-network-id',\n    nonce: 'kjs-test',\n  });\n\n  const mockEckoRequest = vi.fn();\n\n  Object.defineProperty(window, 'kadena', {\n    value: {\n      isKadena: true,\n      request: mockEckoRequest,\n    },\n    writable: true,\n  });\n\n  beforeEach(() => {\n    mockEckoRequest.mockReset();\n  });\n\n  afterAll(() => {\n    mockEckoRequest.mockRestore();\n  });\n\n  it('signs a transaction', async () => {\n    mockEckoRequest.mockResolvedValue({\n      status: 'success',\n      signedCmd: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },\n    });\n\n    const signWithEckoWallet = createEckoWalletSign();\n    const transaction = getTransaction();\n    const signedTransaction = await signWithEckoWallet(\n      createTransaction(transaction),\n    );\n\n    expect(mockEckoRequest).toHaveBeenCalledWith({\n      method: 'kda_requestSign',\n      data: {\n        networkId: 'test-network-id',\n        signingCmd: {\n          code: transaction.payload.exec.code,\n          data: transaction.payload.exec.data,\n          caps: [\n            {\n              role: 'test-cap-name',\n              description: 'Description for cap.test-cap-name',\n              cap: {\n                name: 'cap.test-cap-name',\n                args: ['test-cap-arg'],\n              },\n            },\n          ],\n          nonce: transaction.nonce,\n          chainId: transaction.meta.chainId,\n          gasLimit: transaction.meta.gasLimit,\n          gasPrice: transaction.meta.gasPrice,\n          sender: transaction.meta.sender,\n          ttl: transaction.meta.ttl,\n        },\n      },\n    });\n\n    expect(signedTransaction.cmd).toBe('test-cmd');\n\n    expect(signedTransaction).toEqual({\n      cmd: 'test-cmd',\n      sigs: [{ sig: 'test-sig' }],\n    });\n  });\n\n  it('throws when there is no signing response', async () => {\n    const signWithEckoWallet = createEckoWalletSign();\n\n    const transaction = getTransaction();\n    //@ts-expect-error The operand of a 'delete' operator must be optional.\n    delete transaction.payload.exec;\n\n    await expect(() =>\n      signWithEckoWallet(createTransaction(transaction)),\n    ).rejects.toThrowError('`cont` transactions are not supported');\n  });\n\n  it('adds an empty clist when signer.clist is undefined', async () => {\n    mockEckoRequest.mockResolvedValue({\n      status: 'success',\n      signedCmd: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },\n    });\n\n    const signWithEckoWallet = createEckoWalletSign();\n\n    const transaction = getTransaction();\n    delete transaction.signers[0].clist;\n\n    await signWithEckoWallet(createTransaction(transaction));\n\n    expect(window.kadena?.request).toHaveBeenCalledWith({\n      method: 'kda_requestSign',\n      data: {\n        networkId: 'test-network-id',\n        signingCmd: {\n          code: transaction.payload.exec.code,\n          data: transaction.payload.exec.data,\n          caps: [],\n          nonce: transaction.nonce,\n          chainId: transaction.meta.chainId,\n          gasLimit: transaction.meta.gasLimit,\n          gasPrice: transaction.meta.gasPrice,\n          sender: transaction.meta.sender,\n          ttl: transaction.meta.ttl,\n        },\n      },\n    });\n  });\n\n  it('throws when signing cont command', async () => {\n    const transaction = getTransaction();\n    const signWithEckoWallet = createEckoWalletSign();\n\n    await expect(() =>\n      signWithEckoWallet(createTransaction(transaction)),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n});\n"]}