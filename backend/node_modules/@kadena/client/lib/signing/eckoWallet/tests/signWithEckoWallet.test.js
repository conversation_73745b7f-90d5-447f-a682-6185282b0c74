"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createTransaction_1 = require("../../../utils/createTransaction");
const signWithEckoWallet_1 = require("../signWithEckoWallet");
const util_1 = require("util");
Object.assign(global, { TextDecoder: util_1.TextDecoder, TextEncoder: util_1.TextEncoder });
(0, vitest_1.describe)('signWithEckoWallet', () => {
    const getTransaction = () => ({
        payload: {
            exec: {
                code: '(coin.transfer "bonnie" "clyde" 1)',
                data: {
                    test: 'test-data',
                },
            },
        },
        meta: {
            chainId: '0',
            gasLimit: 2300,
            gasPrice: 0.00000001,
            sender: 'test-sender',
            ttl: 3600,
            creationTime: 123456789,
        },
        signers: [
            {
                pubKey: '',
                clist: [
                    {
                        name: 'cap.test-cap-name',
                        args: ['test-cap-arg'],
                    },
                ],
            },
        ],
        networkId: 'test-network-id',
        nonce: 'kjs-test',
    });
    const mockEckoRequest = vitest_1.vi.fn();
    Object.defineProperty(window, 'kadena', {
        value: {
            isKadena: true,
            request: mockEckoRequest,
        },
        writable: true,
    });
    (0, vitest_1.beforeEach)(() => {
        mockEckoRequest.mockReset();
    });
    (0, vitest_1.afterAll)(() => {
        mockEckoRequest.mockRestore();
    });
    (0, vitest_1.it)('signs a transaction', async () => {
        mockEckoRequest.mockResolvedValue({
            status: 'success',
            signedCmd: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },
        });
        const signWithEckoWallet = (0, signWithEckoWallet_1.createEckoWalletSign)();
        const transaction = getTransaction();
        const signedTransaction = await signWithEckoWallet((0, createTransaction_1.createTransaction)(transaction));
        (0, vitest_1.expect)(mockEckoRequest).toHaveBeenCalledWith({
            method: 'kda_requestSign',
            data: {
                networkId: 'test-network-id',
                signingCmd: {
                    code: transaction.payload.exec.code,
                    data: transaction.payload.exec.data,
                    caps: [
                        {
                            role: 'test-cap-name',
                            description: 'Description for cap.test-cap-name',
                            cap: {
                                name: 'cap.test-cap-name',
                                args: ['test-cap-arg'],
                            },
                        },
                    ],
                    nonce: transaction.nonce,
                    chainId: transaction.meta.chainId,
                    gasLimit: transaction.meta.gasLimit,
                    gasPrice: transaction.meta.gasPrice,
                    sender: transaction.meta.sender,
                    ttl: transaction.meta.ttl,
                },
            },
        });
        (0, vitest_1.expect)(signedTransaction.cmd).toBe('test-cmd');
        (0, vitest_1.expect)(signedTransaction).toEqual({
            cmd: 'test-cmd',
            sigs: [{ sig: 'test-sig' }],
        });
    });
    (0, vitest_1.it)('throws when there is no signing response', async () => {
        const signWithEckoWallet = (0, signWithEckoWallet_1.createEckoWalletSign)();
        const transaction = getTransaction();
        //@ts-expect-error The operand of a 'delete' operator must be optional.
        delete transaction.payload.exec;
        await (0, vitest_1.expect)(() => signWithEckoWallet((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('`cont` transactions are not supported');
    });
    (0, vitest_1.it)('adds an empty clist when signer.clist is undefined', async () => {
        var _a;
        mockEckoRequest.mockResolvedValue({
            status: 'success',
            signedCmd: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },
        });
        const signWithEckoWallet = (0, signWithEckoWallet_1.createEckoWalletSign)();
        const transaction = getTransaction();
        delete transaction.signers[0].clist;
        await signWithEckoWallet((0, createTransaction_1.createTransaction)(transaction));
        (0, vitest_1.expect)((_a = window.kadena) === null || _a === void 0 ? void 0 : _a.request).toHaveBeenCalledWith({
            method: 'kda_requestSign',
            data: {
                networkId: 'test-network-id',
                signingCmd: {
                    code: transaction.payload.exec.code,
                    data: transaction.payload.exec.data,
                    caps: [],
                    nonce: transaction.nonce,
                    chainId: transaction.meta.chainId,
                    gasLimit: transaction.meta.gasLimit,
                    gasPrice: transaction.meta.gasPrice,
                    sender: transaction.meta.sender,
                    ttl: transaction.meta.ttl,
                },
            },
        });
    });
    (0, vitest_1.it)('throws when signing cont command', async () => {
        const transaction = getTransaction();
        const signWithEckoWallet = (0, signWithEckoWallet_1.createEckoWalletSign)();
        await (0, vitest_1.expect)(() => signWithEckoWallet((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Error signing transaction');
    });
});
//# sourceMappingURL=signWithEckoWallet.test.js.map