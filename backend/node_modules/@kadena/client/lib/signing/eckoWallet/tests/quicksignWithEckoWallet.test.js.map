{"version": 3, "file": "quicksignWithEckoWallet.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/eckoWallet/tests/quicksignWithEckoWallet.test.ts"], "names": [], "mappings": ";;AAAA,mCAAwE;AAIxE,wEAAqE;AACrE,wEAAuE;AAEvE,+BAAgD;AAEhD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,WAAW,EAAX,kBAAW,EAAE,WAAW,EAAX,kBAAW,EAAE,CAAC,CAAC;AAEpD,IAAA,iBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,MAAM,cAAc,GAAG,GAAiB,EAAE,CAAC,CAAC;QAC1C,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,IAAI,EAAE,oCAAoC;gBAC1C,IAAI,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE;aACnC;SACF;QACD,IAAI,EAAE;YACJ,OAAO,EAAE,GAAG;YACZ,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,aAAa;YACrB,GAAG,EAAE,EAAE,GAAG,CAAC;YACX,YAAY,EAAE,IAAI;SACnB;QACD,OAAO,EAAE;YACP;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,CAAC,cAAc,CAAC;qBACvB;iBACF;gBACD,MAAM,EAAE,cAAc;aACvB;SACF;QACD,SAAS,EAAE,YAAY;QACvB,KAAK,EAAE,EAAE;KACV,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;IAEhC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE;QACtC,KAAK,EAAE;YACL,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,eAAe;SACzB;QACD,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,eAAe,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,GAAG,EAAE;QACZ,eAAe,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACtD,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAE5D,sDAAsD;QACtD,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAAC,uBAAuB,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAChE,2BAA2B,CAC5B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;;QACnC,eAAe,CAAC,iBAAiB,CAAC;YAChC,MAAM,EAAE,SAAS;YACjB,aAAa,EAAE;gBACb;oBACE,OAAO,EAAE;wBACP,MAAM,EAAE,SAAS;wBACjB,IAAI,EAAE,WAAW;qBAClB;oBACD,cAAc,EAAE;wBACd,GAAG,EAAE,UAAU;wBACf,IAAI,EAAE;4BACJ;gCACE,IAAI,EAAE;oCACJ;wCACE,IAAI,EAAE,CAAC,cAAc,CAAC;wCACtB,IAAI,EAAE,eAAe;qCACtB;iCACF;gCACD,MAAM,EAAE,cAAc;gCACtB,GAAG,EAAE,UAAU;6BAChB;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,mBAAmB,GAAG,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC;QAC3D,mBAAmB,CAAC,IAAI,GAAG,WAAW,CAAC;QACvC,MAAM,MAAM,GAAG,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;QAElE,IAAA,eAAM,EAAC,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAAC,CAAC,oBAAoB,CAAC;YAClD,MAAM,EAAE,sBAAsB;YAC9B,IAAI,EAAE;gBACJ,SAAS,EAAE,YAAY;gBACvB,eAAe,EAAE;oBACf;wBACE,GAAG,EAAE,+VAA+V;wBACpW,IAAI,EAAE;4BACJ;gCACE,MAAM,EAAE,cAAc;gCACtB,GAAG,EAAE,IAAI;6BACV;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,GAAG,EAAE,+VAA+V;YACpW,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;SACpD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;;QAC3C,eAAe,CAAC,iBAAiB,CAAC;YAChC,MAAM,EAAE,SAAS;YACjB,aAAa,EAAE;gBACb;oBACE,OAAO,EAAE;wBACP,MAAM,EAAE,SAAS;wBACjB,IAAI,EAAE,WAAW;qBAClB;oBACD,cAAc,EAAE;wBACd,GAAG,EAAE,UAAU;wBACf,IAAI,EAAE;4BACJ;gCACE,IAAI,EAAE;oCACJ;wCACE,IAAI,EAAE,CAAC,cAAc,CAAC;wCACtB,IAAI,EAAE,eAAe;qCACtB;iCACF;gCACD,MAAM,EAAE,cAAc;gCACtB,GAAG,EAAE,UAAU;6BAChB;yBACF;qBACF;iBACF;gBACD;oBACE,OAAO,EAAE;wBACP,MAAM,EAAE,SAAS;wBACjB,IAAI,EAAE,aAAa;qBACpB;oBACD,cAAc,EAAE;wBACd,GAAG,EAAE,YAAY;wBACjB,IAAI,EAAE;4BACJ;gCACE,IAAI,EAAE;oCACJ;wCACE,IAAI,EAAE,CAAC,cAAc,CAAC;wCACtB,IAAI,EAAE,eAAe;qCACtB;iCACF;gCACD,MAAM,EAAE,gBAAgB;gCACxB,GAAG,EAAE,YAAY;6BAClB;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,oBAAoB,GAAG;YAC3B,IAAA,qCAAiB,EAAC,WAAW,CAAC;YAC9B,IAAA,qCAAiB,EAAC;gBAChB,GAAG,cAAc,EAAE;gBACnB,OAAO,EAAE;oBACP;wBACE,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,eAAe;gCACrB,IAAI,EAAE,CAAC,cAAc,CAAC;6BACvB;yBACF;wBACD,MAAM,EAAE,gBAAgB;qBACzB;iBACF;aACF,CAAC;SACH,CAAC;QACF,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC;QAC3C,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,aAAa,CAAC;QAC7C,MAAM,MAAM,GAAG,MAAM,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;QAEnE,IAAA,eAAM,EAAC,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAAC,CAAC,oBAAoB,CAAC;YAClD,MAAM,EAAE,sBAAsB;YAC9B,IAAI,EAAE;gBACJ,SAAS,EAAE,YAAY;gBACvB,eAAe,EAAE;oBACf;wBACE,GAAG,EAAE,+VAA+V;wBACpW,IAAI,EAAE;4BACJ;gCACE,MAAM,EAAE,cAAc;gCACtB,GAAG,EAAE,IAAI;6BACV;yBACF;qBACF;oBACD;wBACE,GAAG,EAAE,iWAAiW;wBACtW,IAAI,EAAE;4BACJ;gCACE,MAAM,EAAE,gBAAgB;gCACxB,GAAG,EAAE,IAAI;6BACV;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB;gBACE,GAAG,EAAE,+VAA+V;gBACpW,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;aACpD;YACD;gBACE,GAAG,EAAE,iWAAiW;gBACtW,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC;aACxD;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QAErC,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,uBAAuB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CACxD,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;QAC/E,eAAe,CAAC,iBAAiB,CAAC;YAChC,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,mBAAmB,GAAG,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC;QAE3D,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,uBAAuB,CAAC,mBAAmB,CAAC,CAC7C,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QAClD,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QACrC,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,uBAAuB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CACxD,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;QACxF,eAAe,CAAC,iBAAiB,CAAC;YAChC,MAAM,EAAE,SAAS;YACjB,aAAa,EAAE;gBACb;oBACE,OAAO,EAAE;wBACP,MAAM,EAAE,SAAS;wBACjB,IAAI,EAAE,qBAAqB;qBAC5B;oBACD,cAAc,EAAE;wBACd,GAAG,EAAE,UAAU;wBACf,IAAI,EAAE;4BACJ;gCACE,IAAI,EAAE;oCACJ;wCACE,IAAI,EAAE,CAAC,cAAc,CAAC;wCACtB,IAAI,EAAE,eAAe;qCACtB;iCACF;gCACD,MAAM,EAAE,cAAc;gCACtB,GAAG,EAAE,UAAU;6BAChB;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QAErC,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,uBAAuB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CACxD,CAAC,OAAO,CAAC,YAAY,CACpB,uEAAuE,CACxE,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,uBAAuB,GAAG,IAAA,mDAAyB,GAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;QAErC,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,uBAAuB,CAAC;YACtB,IAAA,qCAAiB,EAAC,WAAW,CAAC;YAC9B,IAAA,qCAAiB,EAAC,EAAE,GAAG,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;SACjE,CAAC,CACH,CAAC,OAAO,CAAC,YAAY,CAAC,2CAA2C,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { afterAll, beforeEach, describe, expect, it, vi } from 'vitest';\n/** @vitest-environment jsdom */\n\nimport type { IPactCommand } from '../../../interfaces/IPactCommand';\nimport { createTransaction } from '../../../utils/createTransaction';\nimport { createEckoWalletQuicksign } from '../quicksignWithEckoWallet';\n\nimport { TextDecoder, TextEncoder } from 'util';\n\nObject.assign(global, { TextDecoder, TextEncoder });\n\ndescribe('quicksignWithEckoWallet', () => {\n  const getTransaction = (): IPactCommand => ({\n    payload: {\n      exec: {\n        code: '(coin.transfer \"bonnie\" \"clyde\" 1)',\n        data: { 'test-data': 'test-data' },\n      },\n    },\n    meta: {\n      chainId: '1',\n      gasLimit: 10000,\n      gasPrice: 1e-8,\n      sender: 'test-sender',\n      ttl: 30 * 6,\n      creationTime: 1234,\n    },\n    signers: [\n      {\n        clist: [\n          {\n            name: 'test-cap-name',\n            args: ['test-cap-arg'],\n          },\n        ],\n        pubKey: 'test-pub-key',\n      },\n    ],\n    networkId: 'testnet-id',\n    nonce: '',\n  });\n\n  const mockEckoRequest = vi.fn();\n\n  Object.defineProperty(window, 'kadena', {\n    value: {\n      isKadena: true,\n      request: mockEckoRequest,\n    },\n    writable: true,\n  });\n\n  beforeEach(() => {\n    mockEckoRequest.mockReset();\n  });\n\n  afterAll(() => {\n    mockEckoRequest.mockRestore();\n  });\n\n  it('throws when no transactions are passed', async () => {\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n\n    // @ts-expect-error - Expected 1 arguments, but got 0.\n    await expect(() => quicksignWithEckoWallet()).rejects.toThrowError(\n      'No transaction(s) to sign',\n    );\n  });\n\n  it('signs a transaction', async () => {\n    mockEckoRequest.mockResolvedValue({\n      status: 'success',\n      quickSignData: [\n        {\n          outcome: {\n            result: 'success',\n            hash: 'test-hash',\n          },\n          commandSigData: {\n            cmd: 'test-cmd',\n            sigs: [\n              {\n                caps: [\n                  {\n                    args: ['test-cap-arg'],\n                    name: 'test-cap-name',\n                  },\n                ],\n                pubKey: 'test-pub-key',\n                sig: 'test-sig',\n              },\n            ],\n          },\n        },\n      ],\n    });\n\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n    const unsignedTransaction = createTransaction(transaction);\n    unsignedTransaction.hash = 'test-hash';\n    const result = await quicksignWithEckoWallet(unsignedTransaction);\n\n    expect(window.kadena?.request).toHaveBeenCalledWith({\n      method: 'kda_requestQuickSign',\n      data: {\n        networkId: 'testnet-id',\n        commandSigDatas: [\n          {\n            cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n            sigs: [\n              {\n                pubKey: 'test-pub-key',\n                sig: null,\n              },\n            ],\n          },\n        ],\n      },\n    });\n\n    expect(result).toEqual({\n      cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n      hash: 'test-hash',\n      sigs: [{ sig: 'test-sig', pubKey: 'test-pub-key' }],\n    });\n  });\n\n  it('signs multiple transactions', async () => {\n    mockEckoRequest.mockResolvedValue({\n      status: 'success',\n      quickSignData: [\n        {\n          outcome: {\n            result: 'success',\n            hash: 'test-hash',\n          },\n          commandSigData: {\n            cmd: 'test-cmd',\n            sigs: [\n              {\n                caps: [\n                  {\n                    args: ['test-cap-arg'],\n                    name: 'test-cap-name',\n                  },\n                ],\n                pubKey: 'test-pub-key',\n                sig: 'test-sig',\n              },\n            ],\n          },\n        },\n        {\n          outcome: {\n            result: 'success',\n            hash: 'test-hash-2',\n          },\n          commandSigData: {\n            cmd: 'test-cmd-2',\n            sigs: [\n              {\n                caps: [\n                  {\n                    args: ['test-cap-arg'],\n                    name: 'test-cap-name',\n                  },\n                ],\n                pubKey: 'test-pub-key-2',\n                sig: 'test-sig-2',\n              },\n            ],\n          },\n        },\n      ],\n    });\n\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n    const unsignedTransactions = [\n      createTransaction(transaction),\n      createTransaction({\n        ...getTransaction(),\n        signers: [\n          {\n            clist: [\n              {\n                name: 'test-cap-name',\n                args: ['test-cap-arg'],\n              },\n            ],\n            pubKey: 'test-pub-key-2',\n          },\n        ],\n      }),\n    ];\n    unsignedTransactions[0].hash = 'test-hash';\n    unsignedTransactions[1].hash = 'test-hash-2';\n    const result = await quicksignWithEckoWallet(unsignedTransactions);\n\n    expect(window.kadena?.request).toHaveBeenCalledWith({\n      method: 'kda_requestQuickSign',\n      data: {\n        networkId: 'testnet-id',\n        commandSigDatas: [\n          {\n            cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n            sigs: [\n              {\n                pubKey: 'test-pub-key',\n                sig: null,\n              },\n            ],\n          },\n          {\n            cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key-2\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n            sigs: [\n              {\n                pubKey: 'test-pub-key-2',\n                sig: null,\n              },\n            ],\n          },\n        ],\n      },\n    });\n\n    expect(result).toEqual([\n      {\n        cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n        hash: 'test-hash',\n        sigs: [{ sig: 'test-sig', pubKey: 'test-pub-key' }],\n      },\n      {\n        cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key-2\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n        hash: 'test-hash-2',\n        sigs: [{ sig: 'test-sig-2', pubKey: 'test-pub-key-2' }],\n      },\n    ]);\n  });\n\n  it('throws when there is no signing response', async () => {\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n\n    await expect(() =>\n      quicksignWithEckoWallet(createTransaction(transaction)),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n\n  it('throws when there is no quickSignData in the response from Ecko', async () => {\n    mockEckoRequest.mockResolvedValue({\n      status: 'success',\n    });\n\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n    const unsignedTransaction = createTransaction(transaction);\n\n    await expect(() =>\n      quicksignWithEckoWallet(unsignedTransaction),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n\n  it('throws when there are no responses', async () => {\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n    await expect(() =>\n      quicksignWithEckoWallet(createTransaction(transaction)),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n\n  it('throws when the hash of the unsigned and signed transaction do not match', async () => {\n    mockEckoRequest.mockResolvedValue({\n      status: 'success',\n      quickSignData: [\n        {\n          outcome: {\n            result: 'success',\n            hash: 'test-hash-different',\n          },\n          commandSigData: {\n            cmd: 'test-cmd',\n            sigs: [\n              {\n                caps: [\n                  {\n                    args: ['test-cap-arg'],\n                    name: 'test-cap-name',\n                  },\n                ],\n                pubKey: 'test-pub-key',\n                sig: 'test-sig',\n              },\n            ],\n          },\n        },\n      ],\n    });\n\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n\n    await expect(() =>\n      quicksignWithEckoWallet(createTransaction(transaction)),\n    ).rejects.toThrowError(\n      'Hash of the transaction signed by the wallet does not match. Our hash',\n    );\n  });\n\n  it('throws when the networks of the transactions are not the same', async () => {\n    const quicksignWithEckoWallet = createEckoWalletQuicksign();\n    const transaction = getTransaction();\n\n    await expect(() =>\n      quicksignWithEckoWallet([\n        createTransaction(transaction),\n        createTransaction({ ...transaction, networkId: 'testnet-id-2' }),\n      ]),\n    ).rejects.toThrowError('Network is not equal for all transactions');\n  });\n});\n"]}