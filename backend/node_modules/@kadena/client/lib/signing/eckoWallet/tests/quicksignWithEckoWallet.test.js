"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createTransaction_1 = require("../../../utils/createTransaction");
const quicksignWithEckoWallet_1 = require("../quicksignWithEckoWallet");
const util_1 = require("util");
Object.assign(global, { TextDecoder: util_1.TextDecoder, TextEncoder: util_1.TextEncoder });
(0, vitest_1.describe)('quicksignWithEckoWallet', () => {
    const getTransaction = () => ({
        payload: {
            exec: {
                code: '(coin.transfer "bonnie" "clyde" 1)',
                data: { 'test-data': 'test-data' },
            },
        },
        meta: {
            chainId: '1',
            gasLimit: 10000,
            gasPrice: 1e-8,
            sender: 'test-sender',
            ttl: 30 * 6,
            creationTime: 1234,
        },
        signers: [
            {
                clist: [
                    {
                        name: 'test-cap-name',
                        args: ['test-cap-arg'],
                    },
                ],
                pubKey: 'test-pub-key',
            },
        ],
        networkId: 'testnet-id',
        nonce: '',
    });
    const mockEckoRequest = vitest_1.vi.fn();
    Object.defineProperty(window, 'kadena', {
        value: {
            isKadena: true,
            request: mockEckoRequest,
        },
        writable: true,
    });
    (0, vitest_1.beforeEach)(() => {
        mockEckoRequest.mockReset();
    });
    (0, vitest_1.afterAll)(() => {
        mockEckoRequest.mockRestore();
    });
    (0, vitest_1.it)('throws when no transactions are passed', async () => {
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        // @ts-expect-error - Expected 1 arguments, but got 0.
        await (0, vitest_1.expect)(() => quicksignWithEckoWallet()).rejects.toThrowError('No transaction(s) to sign');
    });
    (0, vitest_1.it)('signs a transaction', async () => {
        var _a;
        mockEckoRequest.mockResolvedValue({
            status: 'success',
            quickSignData: [
                {
                    outcome: {
                        result: 'success',
                        hash: 'test-hash',
                    },
                    commandSigData: {
                        cmd: 'test-cmd',
                        sigs: [
                            {
                                caps: [
                                    {
                                        args: ['test-cap-arg'],
                                        name: 'test-cap-name',
                                    },
                                ],
                                pubKey: 'test-pub-key',
                                sig: 'test-sig',
                            },
                        ],
                    },
                },
            ],
        });
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        const unsignedTransaction = (0, createTransaction_1.createTransaction)(transaction);
        unsignedTransaction.hash = 'test-hash';
        const result = await quicksignWithEckoWallet(unsignedTransaction);
        (0, vitest_1.expect)((_a = window.kadena) === null || _a === void 0 ? void 0 : _a.request).toHaveBeenCalledWith({
            method: 'kda_requestQuickSign',
            data: {
                networkId: 'testnet-id',
                commandSigDatas: [
                    {
                        cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key"}],"networkId":"testnet-id","nonce":""}',
                        sigs: [
                            {
                                pubKey: 'test-pub-key',
                                sig: null,
                            },
                        ],
                    },
                ],
            },
        });
        (0, vitest_1.expect)(result).toEqual({
            cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key"}],"networkId":"testnet-id","nonce":""}',
            hash: 'test-hash',
            sigs: [{ sig: 'test-sig', pubKey: 'test-pub-key' }],
        });
    });
    (0, vitest_1.it)('signs multiple transactions', async () => {
        var _a;
        mockEckoRequest.mockResolvedValue({
            status: 'success',
            quickSignData: [
                {
                    outcome: {
                        result: 'success',
                        hash: 'test-hash',
                    },
                    commandSigData: {
                        cmd: 'test-cmd',
                        sigs: [
                            {
                                caps: [
                                    {
                                        args: ['test-cap-arg'],
                                        name: 'test-cap-name',
                                    },
                                ],
                                pubKey: 'test-pub-key',
                                sig: 'test-sig',
                            },
                        ],
                    },
                },
                {
                    outcome: {
                        result: 'success',
                        hash: 'test-hash-2',
                    },
                    commandSigData: {
                        cmd: 'test-cmd-2',
                        sigs: [
                            {
                                caps: [
                                    {
                                        args: ['test-cap-arg'],
                                        name: 'test-cap-name',
                                    },
                                ],
                                pubKey: 'test-pub-key-2',
                                sig: 'test-sig-2',
                            },
                        ],
                    },
                },
            ],
        });
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        const unsignedTransactions = [
            (0, createTransaction_1.createTransaction)(transaction),
            (0, createTransaction_1.createTransaction)({
                ...getTransaction(),
                signers: [
                    {
                        clist: [
                            {
                                name: 'test-cap-name',
                                args: ['test-cap-arg'],
                            },
                        ],
                        pubKey: 'test-pub-key-2',
                    },
                ],
            }),
        ];
        unsignedTransactions[0].hash = 'test-hash';
        unsignedTransactions[1].hash = 'test-hash-2';
        const result = await quicksignWithEckoWallet(unsignedTransactions);
        (0, vitest_1.expect)((_a = window.kadena) === null || _a === void 0 ? void 0 : _a.request).toHaveBeenCalledWith({
            method: 'kda_requestQuickSign',
            data: {
                networkId: 'testnet-id',
                commandSigDatas: [
                    {
                        cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key"}],"networkId":"testnet-id","nonce":""}',
                        sigs: [
                            {
                                pubKey: 'test-pub-key',
                                sig: null,
                            },
                        ],
                    },
                    {
                        cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key-2"}],"networkId":"testnet-id","nonce":""}',
                        sigs: [
                            {
                                pubKey: 'test-pub-key-2',
                                sig: null,
                            },
                        ],
                    },
                ],
            },
        });
        (0, vitest_1.expect)(result).toEqual([
            {
                cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key"}],"networkId":"testnet-id","nonce":""}',
                hash: 'test-hash',
                sigs: [{ sig: 'test-sig', pubKey: 'test-pub-key' }],
            },
            {
                cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key-2"}],"networkId":"testnet-id","nonce":""}',
                hash: 'test-hash-2',
                sigs: [{ sig: 'test-sig-2', pubKey: 'test-pub-key-2' }],
            },
        ]);
    });
    (0, vitest_1.it)('throws when there is no signing response', async () => {
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        await (0, vitest_1.expect)(() => quicksignWithEckoWallet((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Error signing transaction');
    });
    (0, vitest_1.it)('throws when there is no quickSignData in the response from Ecko', async () => {
        mockEckoRequest.mockResolvedValue({
            status: 'success',
        });
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        const unsignedTransaction = (0, createTransaction_1.createTransaction)(transaction);
        await (0, vitest_1.expect)(() => quicksignWithEckoWallet(unsignedTransaction)).rejects.toThrowError('Error signing transaction');
    });
    (0, vitest_1.it)('throws when there are no responses', async () => {
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        await (0, vitest_1.expect)(() => quicksignWithEckoWallet((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Error signing transaction');
    });
    (0, vitest_1.it)('throws when the hash of the unsigned and signed transaction do not match', async () => {
        mockEckoRequest.mockResolvedValue({
            status: 'success',
            quickSignData: [
                {
                    outcome: {
                        result: 'success',
                        hash: 'test-hash-different',
                    },
                    commandSigData: {
                        cmd: 'test-cmd',
                        sigs: [
                            {
                                caps: [
                                    {
                                        args: ['test-cap-arg'],
                                        name: 'test-cap-name',
                                    },
                                ],
                                pubKey: 'test-pub-key',
                                sig: 'test-sig',
                            },
                        ],
                    },
                },
            ],
        });
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        await (0, vitest_1.expect)(() => quicksignWithEckoWallet((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Hash of the transaction signed by the wallet does not match. Our hash');
    });
    (0, vitest_1.it)('throws when the networks of the transactions are not the same', async () => {
        const quicksignWithEckoWallet = (0, quicksignWithEckoWallet_1.createEckoWalletQuicksign)();
        const transaction = getTransaction();
        await (0, vitest_1.expect)(() => quicksignWithEckoWallet([
            (0, createTransaction_1.createTransaction)(transaction),
            (0, createTransaction_1.createTransaction)({ ...transaction, networkId: 'testnet-id-2' }),
        ])).rejects.toThrowError('Network is not equal for all transactions');
    });
});
//# sourceMappingURL=quicksignWithEckoWallet.test.js.map