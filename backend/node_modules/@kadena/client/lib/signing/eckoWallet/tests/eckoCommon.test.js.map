{"version": 3, "file": "eckoCommon.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/eckoWallet/tests/eckoCommon.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8D;AAC9D,gCAAgC;AAEhC,8CAA+E;AAE/E,+BAAgD;AAEhD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,WAAW,EAAX,kBAAW,EAAE,WAAW,EAAX,kBAAW,EAAE,CAAC,CAAC;AAEpD,IAAA,iBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,MAAM,eAAe,GAAG,WAAE,CAAC,EAAE,EAAE,CAAC;IAEhC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE;QACtC,KAAK,EAAE;YACL,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,eAAe;SACzB;QACD,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,IAAI,MAAM,CAAC,MAAM;YAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;QACjD,eAAe,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,IAAA,wBAAW,GAAE,CAAC;YAE7B,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;YAElD,MAAM,MAAM,GAAG,IAAA,wBAAW,GAAE,CAAC;YAE7B,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,WAAE,EAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAW,EAAC,WAAW,CAAC,CAAC;YAE9C,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,uBAAuB;YACvB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAW,EAAC,WAAW,CAAC,CAAC;YAE9C,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;YAElD,IAAI,CAAC;gBACH,MAAM,IAAA,oBAAO,EAAC,WAAW,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAA,eAAM,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,uBAAuB;YACvB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,oBAAO,EAAC,WAAW,CAAC,CAAC;YAE1C,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,uBAAuB;YACvB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,mBAAmB;YACnB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,oBAAO,EAAC,WAAW,CAAC,CAAC;YAE1C,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,MAAM,EAAE,aAAa;gBACrB,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,uBAAuB;YACvB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,mBAAmB;YACnB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,IAAI,CAAC;gBACH,MAAM,IAAA,oBAAO,EAAC,WAAW,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAA,eAAM,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;YAElD,IAAI,CAAC;gBACH,MAAM,IAAA,wBAAW,EAAC,WAAW,CAAC,CAAC;YACjC,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAA,eAAM,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,mBAAmB;YACnB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,uBAAuB;YACvB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE;oBACP,OAAO,EAAE,QAAQ;oBACjB,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,CAAC,WAAW,CAAC;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAW,EAAC,WAAW,CAAC,CAAC;YAE9C,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE;oBACP,OAAO,EAAE,QAAQ;oBACjB,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,CAAC,WAAW,CAAC;iBAC9B;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;YACtF,mBAAmB;YACnB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,qCAAqC;YACrC,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,yCAAyC;YACzC,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE;oBACP,OAAO,EAAE,QAAQ;oBACjB,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,CAAC,WAAW,CAAC;iBAC9B;gBACD,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAW,EAAC,WAAW,CAAC,CAAC;YAE9C,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;YAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE;oBACP,OAAO,EAAE,QAAQ;oBACjB,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,CAAC,WAAW,CAAC;iBAC9B;gBACD,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,uBAAuB;YACvB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,mBAAmB;YACnB,eAAe,CAAC,qBAAqB,CAAC;gBACpC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,IAAI,CAAC;gBACH,MAAM,IAAA,oBAAO,EAAC,WAAW,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAA,eAAM,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { beforeEach, describe, expect, it, vi } from 'vitest';\n/** @vitest-environment jsdom */\n\nimport { checkStatus, connect, isConnected, isInstalled } from '../eckoCommon';\n\nimport { TextDecoder, TextEncoder } from 'util';\n\nObject.assign(global, { TextDecoder, TextEncoder });\n\ndescribe('eckoCommon', () => {\n  const mockEckoRequest = vi.fn();\n\n  Object.defineProperty(window, 'kadena', {\n    value: {\n      isKadena: true,\n      request: mockEckoRequest,\n    },\n    writable: true,\n  });\n\n  beforeEach(() => {\n    if (window.kadena) window.kadena.isKadena = true;\n    mockEckoRequest.mockReset();\n  });\n\n  describe('isInstalled()', () => {\n    it('returns true when Ecko Wallet is installed', () => {\n      const result = isInstalled();\n\n      expect(result).toBeTruthy();\n    });\n\n    it('returns false when Ecko Wallet is NOT installed', () => {\n      if (window.kadena) window.kadena.isKadena = false;\n\n      const result = isInstalled();\n\n      expect(result).toBeFalsy();\n    });\n  });\n\n  describe('isConnected()', () => {\n    it('returns false when Ecko Wallet is not installed', async () => {\n      if (window.kadena) window.kadena.isKadena = false;\n\n      const result = await isConnected('testnet04');\n\n      expect(result).toBeFalsy();\n    });\n\n    it('returns true when already connected', async () => {\n      // mock kda_checkStatus\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n      });\n\n      const result = await isConnected('testnet04');\n\n      expect(result).toBeTruthy();\n    });\n  });\n\n  describe('connect()', () => {\n    it('throws when Ecko Wallet is not installed', async () => {\n      if (window.kadena) window.kadena.isKadena = false;\n\n      try {\n        await connect('testnet04');\n      } catch (e) {\n        expect(e.message).toContain('Ecko Wallet is not installed');\n      }\n    });\n\n    it('returns true when already connected', async () => {\n      // mock kda_checkStatus\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n      });\n\n      const result = await connect('testnet04');\n\n      expect(result).toBeTruthy();\n    });\n\n    it('connects when not connected yet', async () => {\n      // mock kda_checkStatus\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'fail',\n      });\n\n      // mock kda_connect\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n      });\n\n      const result = await connect('testnet04');\n\n      expect(mockEckoRequest).toHaveBeenCalledWith({\n        method: 'kda_checkStatus',\n        networkId: 'testnet04',\n      });\n\n      expect(mockEckoRequest).toHaveBeenCalledWith({\n        method: 'kda_connect',\n        networkId: 'testnet04',\n      });\n\n      expect(result).toBeTruthy();\n    });\n\n    it('throws when the user declines the connection', async () => {\n      // mock kda_checkStatus\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'fail',\n      });\n\n      // mock kda_connect\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'fail',\n      });\n\n      try {\n        await connect('testnet04');\n      } catch (e) {\n        expect(e.message).toContain('User declined connection');\n      }\n    });\n  });\n\n  describe('checkStatus()', () => {\n    it('throws when Ecko Wallet is not installed', async () => {\n      if (window.kadena) window.kadena.isKadena = false;\n\n      try {\n        await checkStatus('testnet04');\n      } catch (e) {\n        expect(e.message).toContain('Ecko Wallet is not installed');\n      }\n    });\n\n    it('returns the current account when connected', async () => {\n      // mock kda_connect\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n      });\n\n      // mock kda_checkStatus\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n        account: {\n          account: 'k:abcd',\n          publicKey: 'abcd',\n          connectedSites: ['kadena.io'],\n        },\n      });\n\n      const result = await checkStatus('testnet04');\n\n      expect(result).toEqual({\n        status: 'success',\n        account: {\n          account: 'k:abcd',\n          publicKey: 'abcd',\n          connectedSites: ['kadena.io'],\n        },\n      });\n    });\n\n    it('connects when not connected yet, then returns the checkStatus response', async () => {\n      // mock kda_connect\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'fail',\n      });\n\n      // mock kda_checkStatus for connect()\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n      });\n\n      // mock kda_checkStatus for checkStatus()\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'success',\n        account: {\n          account: 'k:abcd',\n          publicKey: 'abcd',\n          connectedSites: ['kadena.io'],\n        },\n        publicKey: 'abcd',\n      });\n\n      const result = await checkStatus('testnet04');\n\n      expect(mockEckoRequest).toHaveBeenCalledWith({\n        method: 'kda_checkStatus',\n        networkId: 'testnet04',\n      });\n\n      expect(result).toEqual({\n        status: 'success',\n        account: {\n          account: 'k:abcd',\n          publicKey: 'abcd',\n          connectedSites: ['kadena.io'],\n        },\n        publicKey: 'abcd',\n      });\n    });\n\n    it('throws when the user declines the connection', async () => {\n      // mock kda_checkStatus\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'fail',\n      });\n\n      // mock kda_connect\n      mockEckoRequest.mockResolvedValueOnce({\n        status: 'fail',\n      });\n\n      try {\n        await connect('testnet04');\n      } catch (e) {\n        expect(e.message).toContain('User declined connection');\n      }\n    });\n  });\n});\n"]}