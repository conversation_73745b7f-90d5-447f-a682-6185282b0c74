"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
/** @vitest-environment jsdom */
const eckoCommon_1 = require("../eckoCommon");
const util_1 = require("util");
Object.assign(global, { TextDecoder: util_1.TextDecoder, TextEncoder: util_1.TextEncoder });
(0, vitest_1.describe)('eckoCommon', () => {
    const mockEckoRequest = vitest_1.vi.fn();
    Object.defineProperty(window, 'kadena', {
        value: {
            isKadena: true,
            request: mockEckoRequest,
        },
        writable: true,
    });
    (0, vitest_1.beforeEach)(() => {
        if (window.kadena)
            window.kadena.isKadena = true;
        mockEckoRequest.mockReset();
    });
    (0, vitest_1.describe)('isInstalled()', () => {
        (0, vitest_1.it)('returns true when Ecko Wallet is installed', () => {
            const result = (0, eckoCommon_1.isInstalled)();
            (0, vitest_1.expect)(result).toBeTruthy();
        });
        (0, vitest_1.it)('returns false when Ecko Wallet is NOT installed', () => {
            if (window.kadena)
                window.kadena.isKadena = false;
            const result = (0, eckoCommon_1.isInstalled)();
            (0, vitest_1.expect)(result).toBeFalsy();
        });
    });
    (0, vitest_1.describe)('isConnected()', () => {
        (0, vitest_1.it)('returns false when Ecko Wallet is not installed', async () => {
            if (window.kadena)
                window.kadena.isKadena = false;
            const result = await (0, eckoCommon_1.isConnected)('testnet04');
            (0, vitest_1.expect)(result).toBeFalsy();
        });
        (0, vitest_1.it)('returns true when already connected', async () => {
            // mock kda_checkStatus
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
            });
            const result = await (0, eckoCommon_1.isConnected)('testnet04');
            (0, vitest_1.expect)(result).toBeTruthy();
        });
    });
    (0, vitest_1.describe)('connect()', () => {
        (0, vitest_1.it)('throws when Ecko Wallet is not installed', async () => {
            if (window.kadena)
                window.kadena.isKadena = false;
            try {
                await (0, eckoCommon_1.connect)('testnet04');
            }
            catch (e) {
                (0, vitest_1.expect)(e.message).toContain('Ecko Wallet is not installed');
            }
        });
        (0, vitest_1.it)('returns true when already connected', async () => {
            // mock kda_checkStatus
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
            });
            const result = await (0, eckoCommon_1.connect)('testnet04');
            (0, vitest_1.expect)(result).toBeTruthy();
        });
        (0, vitest_1.it)('connects when not connected yet', async () => {
            // mock kda_checkStatus
            mockEckoRequest.mockResolvedValueOnce({
                status: 'fail',
            });
            // mock kda_connect
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
            });
            const result = await (0, eckoCommon_1.connect)('testnet04');
            (0, vitest_1.expect)(mockEckoRequest).toHaveBeenCalledWith({
                method: 'kda_checkStatus',
                networkId: 'testnet04',
            });
            (0, vitest_1.expect)(mockEckoRequest).toHaveBeenCalledWith({
                method: 'kda_connect',
                networkId: 'testnet04',
            });
            (0, vitest_1.expect)(result).toBeTruthy();
        });
        (0, vitest_1.it)('throws when the user declines the connection', async () => {
            // mock kda_checkStatus
            mockEckoRequest.mockResolvedValueOnce({
                status: 'fail',
            });
            // mock kda_connect
            mockEckoRequest.mockResolvedValueOnce({
                status: 'fail',
            });
            try {
                await (0, eckoCommon_1.connect)('testnet04');
            }
            catch (e) {
                (0, vitest_1.expect)(e.message).toContain('User declined connection');
            }
        });
    });
    (0, vitest_1.describe)('checkStatus()', () => {
        (0, vitest_1.it)('throws when Ecko Wallet is not installed', async () => {
            if (window.kadena)
                window.kadena.isKadena = false;
            try {
                await (0, eckoCommon_1.checkStatus)('testnet04');
            }
            catch (e) {
                (0, vitest_1.expect)(e.message).toContain('Ecko Wallet is not installed');
            }
        });
        (0, vitest_1.it)('returns the current account when connected', async () => {
            // mock kda_connect
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
            });
            // mock kda_checkStatus
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
                account: {
                    account: 'k:abcd',
                    publicKey: 'abcd',
                    connectedSites: ['kadena.io'],
                },
            });
            const result = await (0, eckoCommon_1.checkStatus)('testnet04');
            (0, vitest_1.expect)(result).toEqual({
                status: 'success',
                account: {
                    account: 'k:abcd',
                    publicKey: 'abcd',
                    connectedSites: ['kadena.io'],
                },
            });
        });
        (0, vitest_1.it)('connects when not connected yet, then returns the checkStatus response', async () => {
            // mock kda_connect
            mockEckoRequest.mockResolvedValueOnce({
                status: 'fail',
            });
            // mock kda_checkStatus for connect()
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
            });
            // mock kda_checkStatus for checkStatus()
            mockEckoRequest.mockResolvedValueOnce({
                status: 'success',
                account: {
                    account: 'k:abcd',
                    publicKey: 'abcd',
                    connectedSites: ['kadena.io'],
                },
                publicKey: 'abcd',
            });
            const result = await (0, eckoCommon_1.checkStatus)('testnet04');
            (0, vitest_1.expect)(mockEckoRequest).toHaveBeenCalledWith({
                method: 'kda_checkStatus',
                networkId: 'testnet04',
            });
            (0, vitest_1.expect)(result).toEqual({
                status: 'success',
                account: {
                    account: 'k:abcd',
                    publicKey: 'abcd',
                    connectedSites: ['kadena.io'],
                },
                publicKey: 'abcd',
            });
        });
        (0, vitest_1.it)('throws when the user declines the connection', async () => {
            // mock kda_checkStatus
            mockEckoRequest.mockResolvedValueOnce({
                status: 'fail',
            });
            // mock kda_connect
            mockEckoRequest.mockResolvedValueOnce({
                status: 'fail',
            });
            try {
                await (0, eckoCommon_1.connect)('testnet04');
            }
            catch (e) {
                (0, vitest_1.expect)(e.message).toContain('User declined connection');
            }
        });
    });
});
//# sourceMappingURL=eckoCommon.test.js.map