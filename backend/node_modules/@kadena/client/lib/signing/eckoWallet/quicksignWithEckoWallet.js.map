{"version": 3, "file": "quicksignWithEckoWallet.js", "sourceRoot": "", "sources": ["../../../src/signing/eckoWallet/quicksignWithEckoWallet.ts"], "names": [], "mappings": ";;;AACA,0DAAuD;AACvD,8EAA2E;AAC3E,6CAA8E;AAG9E;;;;GAIG;AACH,SAAgB,6BAA6B;IAC3C,MAAM,uBAAuB,GAAsB,CAAC,KAAK,EACvD,eAAsE,EACtE,EAAE;;QACF,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAElE,MAAM,iBAAiB,GAAa,EAAE,CAAC;QAEvC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,iDAAuB,EAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACvD,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;YAClC,MAAM,iBAAiB,GAAG,IAAA,iDAAuB,EAAC,WAAW,CAAC,CAAC;YAC/D,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7B,IAAI,SAAS,KAAK,iBAAiB,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;YAED,OAAO;gBACL,GAAG;gBACH,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;oBAAC,OAAA,CAAC;wBAClD,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,GAAG,EAAE,MAAA,MAAA,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,0CAAE,GAAG,mCAAI,IAAI;qBACtC,CAAC,CAAA;iBAAA,CAAC;aACJ,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,MAAM,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAAyB;YACxE,MAAM,EAAE,sBAAsB;YAC9B,IAAI,EAAE;gBACJ,SAAS;gBACT,eAAe;aAChB;SACF,CAAC,CAAA,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,MAAK,MAAM,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,SAAS,GACb,WAAW,IAAI,YAAY;YACzB,CAAC,CAAC,YAAY,CAAC,SAAS;YACxB,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC;QAEjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,SAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;YACjC,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC/C,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,KAAK,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;oBACxD,MAAM,IAAI,KAAK,CACb,0EAA0E,iBAAiB,CAAC,CAAC,CAAC,kBAAkB,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,CAC7I,CAAC;gBACJ,CAAC;gBAED,MAAM,IAAI,GAAG,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CACnD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,CACW,CAAC;gBAEvC,8EAA8E;gBAC9E,YAAY,CAAC,CAAC,CAAC,GAAG,IAAA,6BAAa,EAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,CAAsB,CAAC;IAExB,uBAAuB,CAAC,WAAW,GAAG,wBAAW,CAAC;IAClD,uBAAuB,CAAC,WAAW,GAAG,wBAAW,CAAC;IAClD,uBAAuB,CAAC,OAAO,GAAG,oBAAO,CAAC;IAC1C,uBAAuB,CAAC,WAAW,GAAG,wBAAW,CAAC;IAElD,OAAO,uBAAuB,CAAC;AACjC,CAAC;AA/ED,sEA+EC;AAED;;;;;GAKG;AACU,QAAA,yBAAyB,GAAG,6BAA6B,CAAC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\nimport { addSignatures } from '../utils/addSignatures';\nimport { parseTransactionCommand } from '../utils/parseTransactionCommand';\nimport { checkStatus, connect, isConnected, isInstalled } from './eckoCommon';\nimport type { IEckoQuicksignResponse, IEckoSignFunction } from './eckoTypes';\n\n/**\n * Creates the quicksignWithEckoWallet function with interface {@link ISingleSignFunction}\n *\n * @public\n */\nexport function createQuicksignWithEckoWallet(): IEckoSignFunction {\n  const quicksignWithEckoWallet: IEckoSignFunction = (async (\n    transactionList: IUnsignedCommand | Array<IUnsignedCommand | ICommand>,\n  ) => {\n    if (transactionList === undefined) {\n      throw new Error('No transaction(s) to sign');\n    }\n    const isList = Array.isArray(transactionList);\n    const transactions = isList ? transactionList : [transactionList];\n\n    const transactionHashes: string[] = [];\n\n    const { networkId } = parseTransactionCommand(transactions[0]);\n\n    const commandSigDatas = transactions.map((pactCommand) => {\n      const { cmd, hash } = pactCommand;\n      const parsedTransaction = parseTransactionCommand(pactCommand);\n      transactionHashes.push(hash);\n\n      if (networkId !== parsedTransaction.networkId) {\n        throw new Error('Network is not equal for all transactions');\n      }\n\n      return {\n        cmd,\n        sigs: parsedTransaction.signers.map((signer, i) => ({\n          pubKey: signer.pubKey,\n          sig: pactCommand.sigs[i]?.sig ?? null,\n        })),\n      };\n    });\n\n    const eckoResponse = await window.kadena?.request<IEckoQuicksignResponse>({\n      method: 'kda_requestQuickSign',\n      data: {\n        networkId,\n        commandSigDatas,\n      },\n    });\n\n    if (!eckoResponse || eckoResponse?.status === 'fail') {\n      throw new Error('Error signing transaction');\n    }\n\n    const responses =\n      'responses' in eckoResponse\n        ? eckoResponse.responses\n        : eckoResponse.quickSignData;\n\n    if (!Array.isArray(responses)) {\n      throw new Error('Error signing transaction');\n    }\n\n    responses.map((signedCommand, i) => {\n      if (signedCommand.outcome.result === 'success') {\n        if (signedCommand.outcome.hash !== transactionHashes[i]) {\n          throw new Error(\n            `Hash of the transaction signed by the wallet does not match. Our hash: ${transactionHashes[i]}, wallet hash: ${signedCommand.outcome.hash}`,\n          );\n        }\n\n        const sigs = signedCommand.commandSigData.sigs.filter(\n          (sig) => sig.sig !== null,\n        ) as { pubKey: string; sig: string }[];\n\n        // Add the signature(s) that we received from the wallet to the PactCommand(s)\n        transactions[i] = addSignatures(transactions[i], ...sigs);\n      }\n    });\n\n    return isList ? transactions : transactions[0];\n  }) as IEckoSignFunction;\n\n  quicksignWithEckoWallet.isInstalled = isInstalled;\n  quicksignWithEckoWallet.isConnected = isConnected;\n  quicksignWithEckoWallet.connect = connect;\n  quicksignWithEckoWallet.checkStatus = checkStatus;\n\n  return quicksignWithEckoWallet;\n}\n\n/**\n * Creates the quicksignWithEckoWallet function with interface {@link ISingleSignFunction}\n *\n * @deprecated Use {@link createQuicksignWithEckoWallet} instead\n * @public\n */\nexport const createEckoWalletQuicksign = createQuicksignWithEckoWallet;\n"]}