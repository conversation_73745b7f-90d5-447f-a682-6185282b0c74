{"version": 3, "file": "eckoTypes.d.ts", "sourceRoot": "", "sources": ["../../../src/signing/eckoWallet/eckoTypes.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,gCAAgC,CAAC;AACjF,OAAO,KAAK,EAAE,aAAa,EAAE,mBAAmB,EAAE,MAAM,kBAAkB,CAAC;AAE3E;;;GAGG;AACH,MAAM,MAAM,UAAU,GAAG,SAAS,GAAG,MAAM,CAAC;AAE5C;;;GAGG;AACH,MAAM,WAAW,oBAAoB;IACnC,WAAW,EAAE,MAAM,OAAO,CAAC;IAC3B,WAAW,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IACrD,OAAO,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IACjD,WAAW,EAAE,CACX,SAAS,EAAE,MAAM,KACd,OAAO,CAAC,4BAA4B,GAAG,SAAS,CAAC,CAAC;CACxD;AACD;;;GAGG;AACH,MAAM,WAAW,uBACf,SAAQ,mBAAmB,EACzB,oBAAoB;CAAG;AAE3B;;;GAGG;AACH,MAAM,WAAW,iBACf,SAAQ,aAAa,EACnB,oBAAoB;CAAG;AAE3B;;;GAGG;AACH,MAAM,WAAW,4BAA4B;IAC3C,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE;QACR,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,EAAE,MAAM,EAAE,CAAC;KAC1B,CAAC;CACH;AAED,MAAM,WAAW,iBAAiB;IAChC,MAAM,EAAE,UAAU,CAAC;IACnB,SAAS,EAAE,QAAQ,CAAC;CACrB;AAED,KAAK,6BAA6B,GAAG;IACnC,MAAM,EAAE,SAAS,CAAC;CACnB,GAAG,CACA;IAEE,aAAa,EAAE,0BAA0B,CAAC,WAAW,CAAC,CAAC;CACxD,GACD;IACE,SAAS,EAAE,0BAA0B,CAAC,WAAW,CAAC,CAAC;CACpD,CACJ,CAAC;AAEF,MAAM,WAAW,0BAA0B;IACzC,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,sBAAsB,GAC9B,6BAA6B,GAC7B,0BAA0B,CAAC;AAE/B,MAAM,WAAW,qBAAqB;IACpC,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE;QACP,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,EAAE,MAAM,EAAE,CAAC;QACzB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;CACH"}