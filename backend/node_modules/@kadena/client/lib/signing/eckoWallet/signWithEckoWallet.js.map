{"version": 3, "file": "signWithEckoWallet.js", "sourceRoot": "", "sources": ["../../../src/signing/eckoWallet/signWithEckoWallet.ts"], "names": [], "mappings": ";;;AAAA,sFAAmF;AACnF,8EAA2E;AAC3E,6CAA8E;AAa9E;;;;;;;GAOG;AACH,SAAgB,wBAAwB;IACtC,MAAM,kBAAkB,GAA4B,KAAK,EAAE,WAAW,EAAE,EAAE;;QACxE,MAAM,iBAAiB,GAAG,IAAA,iDAAuB,EAAC,WAAW,CAAC,CAAC;QAC/D,MAAM,cAAc,GAAG,IAAA,yDAA2B,EAAC,iBAAiB,CAAC,CAAC;QAEtE,MAAM,IAAA,oBAAO,EAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,MAAM,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAAoB;YAC/D,MAAM,EAAE,iBAAiB;YACzB,IAAI,EAAE;gBACJ,SAAS,EAAE,iBAAiB,CAAC,SAAS;gBACtC,UAAU,EAAE,cAAc;aAC3B;SACF,CAAC,CAAA,CAAC;QAEH,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,MAAK,SAAS,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,QAAQ,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC;IAEF,kBAAkB,CAAC,WAAW,GAAG,wBAAW,CAAC;IAC7C,kBAAkB,CAAC,WAAW,GAAG,wBAAW,CAAC;IAC7C,kBAAkB,CAAC,OAAO,GAAG,oBAAO,CAAC;IACrC,kBAAkB,CAAC,WAAW,GAAG,wBAAW,CAAC;IAE7C,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AA5BD,4DA4BC;AAED;;;;;;;;GAQG;AACU,QAAA,oBAAoB,GAAG,wBAAwB,CAAC", "sourcesContent": ["import { pactCommandToSigningRequest } from '../utils/pactCommandToSigningRequest';\nimport { parseTransactionCommand } from '../utils/parseTransactionCommand';\nimport { checkStatus, connect, isConnected, isInstalled } from './eckoCommon';\nimport type { IEckoSignResponse, IEckoSignSingleFunction } from './eckoTypes';\n\ndeclare global {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  interface Window {\n    kadena?: {\n      isKadena: boolean;\n      request<T>(args: unknown): Promise<T>;\n    };\n  }\n}\n\n/**\n * Creates the signWithEckoWallet function with interface {@link ISingleSignFunction}\n *\n * @remarks\n * It is preferred to use the {@link createEckoWalletQuicksign} function\n *\n * @public\n */\nexport function createSignWithEckoWallet(): IEckoSignSingleFunction {\n  const signWithEckoWallet: IEckoSignSingleFunction = async (transaction) => {\n    const parsedTransaction = parseTransactionCommand(transaction);\n    const signingRequest = pactCommandToSigningRequest(parsedTransaction);\n\n    await connect(parsedTransaction.networkId);\n\n    const response = await window.kadena?.request<IEckoSignResponse>({\n      method: 'kda_requestSign',\n      data: {\n        networkId: parsedTransaction.networkId,\n        signingCmd: signingRequest,\n      },\n    });\n\n    if (response?.signedCmd === undefined) {\n      throw new Error('Error signing transaction');\n    }\n\n    return response.signedCmd;\n  };\n\n  signWithEckoWallet.isInstalled = isInstalled;\n  signWithEckoWallet.isConnected = isConnected;\n  signWithEckoWallet.connect = connect;\n  signWithEckoWallet.checkStatus = checkStatus;\n\n  return signWithEckoWallet;\n}\n\n/**\n * Creates the signWithEckoWallet function with interface {@link ISingleSignFunction}\n *\n * @remarks\n * It is preferred to use the {@link createQuicksignWithEckoWallet} function\n *\n * @deprecated Use {@link createSignWithEckoWallet} instead\n * @public\n */\nexport const createEckoWalletSign = createSignWithEckoWallet;\n"]}