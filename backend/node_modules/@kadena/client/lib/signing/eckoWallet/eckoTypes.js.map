{"version": 3, "file": "eckoTypes.js", "sourceRoot": "", "sources": ["../../../src/signing/eckoWallet/eckoTypes.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ICommand } from '@kadena/types';\nimport type { IQuicksignResponseOutcomes } from '../../signing-api/v1/quicksign';\nimport type { ISignFunction, ISingleSignFunction } from '../ISignFunction';\n\n/**\n * The response status of the Ecko Wallet request\n * @public\n */\nexport type EckoStatus = 'success' | 'fail';\n\n/**\n * Interface that describes the common functions to be used with Ecko Wallet\n * @public\n */\nexport interface ICommonEckoFunctions {\n  isInstalled: () => boolean;\n  isConnected: (networkId: string) => Promise<boolean>;\n  connect: (networkId: string) => Promise<boolean>;\n  checkStatus: (\n    networkId: string,\n  ) => Promise<IEckoConnectOrStatusResponse | undefined>;\n}\n/**\n * Interface to use when writing a signing function for Ecko Wallet that accepts a single transaction\n * @public\n */\nexport interface IEckoSignSingleFunction\n  extends ISingleSignFunction,\n    ICommonEckoFunctions {}\n\n/**\n * Interface to use when writing a signing function for Ecko Wallet that accepts multiple transactions\n * @public\n */\nexport interface IEckoSignFunction\n  extends ISignFunction,\n    ICommonEckoFunctions {}\n\n/**\n * Interface that describes the response from Ecko Wallet when checking status or connecting\n * @public\n */\nexport interface IEckoConnectOrStatusResponse {\n  status: EckoStatus;\n  message?: string;\n  account?: {\n    account: string;\n    publicKey: string;\n    connectedSites: string[];\n  };\n}\n\nexport interface IEckoSignResponse {\n  status: EckoStatus;\n  signedCmd: ICommand;\n}\n\ntype IEckoQuicksignSuccessResponse = {\n  status: 'success';\n} & (\n  | {\n      // in old versions of Ecko Wallet, the response was called quickSignData\n      quickSignData: IQuicksignResponseOutcomes['responses'];\n    }\n  | {\n      responses: IQuicksignResponseOutcomes['responses'];\n    }\n);\n\nexport interface IEckoQuicksignFailResponse {\n  status: 'fail';\n  error: string;\n}\n\nexport type IEckoQuicksignResponse =\n  | IEckoQuicksignSuccessResponse\n  | IEckoQuicksignFailResponse;\n\nexport interface IEckoAccountsResponse {\n  status: EckoStatus;\n  message?: string;\n  wallet?: {\n    account: string;\n    publicKey: string;\n    connectedSites: string[];\n    balance: number;\n  };\n}\n"]}