import type { IEckoSignFunction } from './eckoTypes';
/**
 * Creates the quicksignWithEckoWallet function with interface {@link ISingleSignFunction}
 *
 * @public
 */
export declare function createQuicksignWithEckoWallet(): IEckoSignFunction;
/**
 * Creates the quicksignWithEckoWallet function with interface {@link ISingleSignFunction}
 *
 * @deprecated Use {@link createQuicksignWithEckoWallet} instead
 * @public
 */
export declare const createEckoWalletQuicksign: typeof createQuicksignWithEckoWallet;
//# sourceMappingURL=quicksignWithEckoWallet.d.ts.map