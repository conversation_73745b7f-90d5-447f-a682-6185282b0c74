{"version": 3, "file": "eckoCommon.js", "sourceRoot": "", "sources": ["../../../src/signing/eckoWallet/eckoCommon.ts"], "names": [], "mappings": ";;;AAKO,MAAM,WAAW,GAAwC,GAAG,EAAE;IACnE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;IAC1B,OAAO,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAC9D,CAAC,CAAC;AAHW,QAAA,WAAW,eAGtB;AAEK,MAAM,WAAW,GAAwC,KAAK,EACnE,SAAS,EACT,EAAE;;IACF,IAAI,CAAC,IAAA,mBAAW,GAAE,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,mBAAmB,GACvB,MAAM,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAA+B;QACzD,MAAM,EAAE,iBAAiB;QACzB,SAAS;KACV,CAAC,CAAA,CAAC;IAEL,OAAO,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,MAAK,SAAS,CAAC;AACnD,CAAC,CAAC;AAdW,QAAA,WAAW,eActB;AAEK,MAAM,OAAO,GAAoC,KAAK,EAAE,SAAS,EAAE,EAAE;;IAC1E,IAAI,CAAC,IAAA,mBAAW,GAAE,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,MAAM,IAAA,mBAAW,EAAC,SAAS,CAAC,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,eAAe,GACnB,MAAM,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAA+B;QACzD,MAAM,EAAE,aAAa;QACrB,SAAS;KACV,CAAC,CAAA,CAAC;IAEL,IAAI,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,MAAK,MAAM,EAAE,CAAC;QACvC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AApBW,QAAA,OAAO,WAoBlB;AAEK,MAAM,WAAW,GAAwC,KAAK,EACnE,SAAS,EACT,EAAE;;IACF,IAAI,CAAC,IAAA,mBAAW,GAAE,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,IAAA,eAAO,EAAC,SAAS,CAAC,CAAC;IAEzB,MAAM,mBAAmB,GACvB,MAAM,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,CAA+B;QACzD,MAAM,EAAE,iBAAiB;QACzB,SAAS;KACV,CAAC,CAAA,CAAC;IAEL,IAAI,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,MAAK,MAAM,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED,OAAO,mBAAmB,CAAC;AAC7B,CAAC,CAAC;AApBW,QAAA,WAAW,eAoBtB", "sourcesContent": ["import type {\n  ICommonEckoFunctions,\n  IEckoConnectOrStatusResponse,\n} from './eckoTypes';\n\nexport const isInstalled: ICommonEckoFunctions['isInstalled'] = () => {\n  const { kadena } = window;\n  return Boolean(kadena && kadena.isKadena && kadena.request);\n};\n\nexport const isConnected: ICommonEckoFunctions['isConnected'] = async (\n  networkId,\n) => {\n  if (!isInstalled()) {\n    return false;\n  }\n\n  const checkStatusResponse =\n    await window.kadena?.request<IEckoConnectOrStatusResponse>({\n      method: 'kda_checkStatus',\n      networkId,\n    });\n\n  return checkStatusResponse?.status === 'success';\n};\n\nexport const connect: ICommonEckoFunctions['connect'] = async (networkId) => {\n  if (!isInstalled()) {\n    throw new Error('Ecko Wallet is not installed');\n  }\n\n  if (await isConnected(networkId)) {\n    return true;\n  }\n\n  const connectResponse =\n    await window.kadena?.request<IEckoConnectOrStatusResponse>({\n      method: 'kda_connect',\n      networkId,\n    });\n\n  if (connectResponse?.status === 'fail') {\n    throw new Error('User declined connection');\n  }\n\n  return true;\n};\n\nexport const checkStatus: ICommonEckoFunctions['checkStatus'] = async (\n  networkId,\n) => {\n  if (!isInstalled()) {\n    throw new Error('Ecko Wallet is not installed');\n  }\n\n  await connect(networkId);\n\n  const checkstatusResponse =\n    await window.kadena?.request<IEckoConnectOrStatusResponse>({\n      method: 'kda_checkStatus',\n      networkId,\n    });\n\n  if (checkstatusResponse?.status === 'fail') {\n    throw new Error('Error getting status from Ecko Wallet');\n  }\n\n  return checkstatusResponse;\n};\n"]}