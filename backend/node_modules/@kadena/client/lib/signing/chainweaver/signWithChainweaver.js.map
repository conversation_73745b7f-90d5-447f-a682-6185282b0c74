{"version": 3, "file": "signWithChainweaver.js", "sourceRoot": "", "sources": ["../../../src/signing/chainweaver/signWithChainweaver.ts"], "names": [], "mappings": ";;;;;;AACA,8DAAgC;AAEhC,kDAA2B;AAO3B,0DAAuD;AACvD,8EAA2E;AAE3E,MAAM,KAAK,GAAa,IAAA,eAAM,EAAC,4BAA4B,CAAC,CAAC;AAE7D;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAAC,cAAsB,EAAE,EAAE,CACzD,CAAC,KAAK,EACJ,eAAsE,EACtE,EAAE;IACF,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;IAClE,MAAM,gBAAgB,GAA0B;QAC9C,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,iBAAiB,GAAG,IAAA,iDAAuB,EAAC,CAAC,CAAC,CAAC;YACrD,OAAO;gBACL,GAAG,EAAE,CAAC,CAAC,GAAG;gBACV,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;oBAChD,OAAO;wBACL,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,GAAG,EAAE,MAAA,MAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,0CAAE,GAAG,mCAAI,IAAI;qBAC5B,CAAC;gBACJ,CAAC,CAAC;aACH,CAAC;QACJ,CAAC,CAAC;KACH,CAAC;IAEF,MAAM,IAAI,GAAW,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;IAEtD,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAEjC,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAK,EAAC,GAAG,cAAc,eAAe,EAAE;QAC7D,MAAM,EAAE,MAAM;QACd,IAAI;QACJ,OAAO,EAAE,EAAE,cAAc,EAAE,gCAAgC,EAAE;KAC9D,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEvC,gEAAgE;IAChE,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAuB,CAAC;QAE1D,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;YACtB,IAAI,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAC1B,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;YACxC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAA,6BAAa,EAC7B,YAAY,CAAC,CAAC,CAAC,EACf,GAAG,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CACvD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CACb,yDAAyD;YACvD,sCAAsC,QAAQ,MAAM;YACpD,aAAa,QAAQ,CAAC,MAAM,IAAI;YAChC,aAAa,QAAQ,CAAC,UAAU,KAAK;YACrC,GAAG,KAAK,EAAE,CACb,CAAC;IACJ,CAAC;AACH,CAAC,CAAkB,CAAC;AAjET,QAAA,gBAAgB,oBAiEP;AAEtB;;;;;GAKG;AACU,QAAA,mBAAmB,GAAkB,IAAA,wBAAgB,EAChE,uBAAuB,CACxB,CAAC;AAEF;;;;;;;;GAQG;AACH,SAAgB,yBAAyB,CACvC,OAAO,GAAG,EAAE,IAAI,EAAE,uBAAuB,EAAE;IAE3C,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IACzB,MAAM,mBAAmB,GAAG,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAC;IAEnD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAPD,8DAOC;AAED,SAAS,SAAS,CAAC,MAAwB;IAIzC,OAAO,CACL,QAAQ,IAAI,MAAM;QAClB,KAAK,IAAI,MAAM;QACf,MAAM,CAAC,GAAG,KAAK,IAAI;QACnB,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CACzB,CAAC;AACJ,CAAC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\nimport fetch from 'cross-fetch';\nimport type { Debugger } from 'debug';\nimport _debug from 'debug';\nimport type {\n  IQuickSignRequestBody,\n  IQuicksignResponse,\n  IQuicksignSigner,\n} from '../../signing-api/v1/quicksign';\nimport type { ISignFunction } from '../ISignFunction';\nimport { addSignatures } from '../utils/addSignatures';\nimport { parseTransactionCommand } from '../utils/parseTransactionCommand';\n\nconst debug: Debugger = _debug('pactjs:signWithChainweaver');\n\n/**\n *\n * @internal\n *\n */\nexport const signTransactions = (chainweaverUrl: string) =>\n  (async (\n    transactionList: IUnsignedCommand | Array<IUnsignedCommand | ICommand>,\n  ) => {\n    if (transactionList === undefined) {\n      throw new Error('No transaction(s) to sign');\n    }\n\n    const isList = Array.isArray(transactionList);\n    const transactions = isList ? transactionList : [transactionList];\n    const quickSignRequest: IQuickSignRequestBody = {\n      cmdSigDatas: transactions.map((t) => {\n        const parsedTransaction = parseTransactionCommand(t);\n        return {\n          cmd: t.cmd,\n          sigs: parsedTransaction.signers.map((signer, i) => {\n            return {\n              pubKey: signer.pubKey,\n              sig: t.sigs[i]?.sig ?? null,\n            };\n          }),\n        };\n      }),\n    };\n\n    const body: string = JSON.stringify(quickSignRequest);\n\n    debug('calling sign api:', body);\n\n    const response = await fetch(`${chainweaverUrl}/v1/quicksign`, {\n      method: 'POST',\n      body,\n      headers: { 'Content-Type': 'application/json;charset=utf-8' },\n    });\n\n    const bodyText = await response.text();\n\n    // response is not JSON when not-ok, that's why we use try-catch\n    try {\n      const result = JSON.parse(bodyText) as IQuicksignResponse;\n\n      if ('error' in result) {\n        if ('msg' in result.error) {\n          console.log('error in result', result.error.msg);\n        }\n        throw new Error(JSON.stringify(result.error));\n      }\n\n      result.responses.map((signedCommand, i) => {\n        transactions[i] = addSignatures(\n          transactions[i],\n          ...signedCommand.commandSigData.sigs.filter(isASigner),\n        );\n      });\n\n      return isList ? transactions : transactions[0];\n    } catch (error) {\n      throw new Error(\n        'An error occurred when adding signatures to the command' +\n          `\\nResponse from v1/quicksign was \\`${bodyText}\\`. ` +\n          `\\nCode: \\`${response.status}\\`` +\n          `\\nText: \\`${response.statusText}\\` ` +\n          `${error}`,\n      );\n    }\n  }) as ISignFunction;\n\n/**\n * * Lets you sign with chainweaver according to {@link https://github.com/kadena-io/KIPs/blob/master/kip-0015.md | sign-v1 API}\n *\n * @deprecated Use {@link createSignWithChainweaver} instead\n * @public\n */\nexport const signWithChainweaver: ISignFunction = signTransactions(\n  'http://127.0.0.1:9467',\n);\n\n/**\n * Creates the signWithChainweaver function with interface {@link ISignFunction}\n * Lets you sign with Chainweaver according to {@link https://github.com/kadena-io/KIPs/blob/master/kip-0015.md | sign-v1 API}\n *\n * @param options - object to customize behaviour.\n *   - `host: string` - the host of the chainweaver instance to use. Defaults to `http://127.0.0.1:9467`\n * @returns - {@link ISignFunction}\n * @public\n */\nexport function createSignWithChainweaver(\n  options = { host: 'http://127.0.0.1:9467' },\n): ISignFunction {\n  const { host } = options;\n  const signWithChainweaver = signTransactions(host);\n\n  return signWithChainweaver;\n}\n\nfunction isASigner(signer: IQuicksignSigner): signer is {\n  pubKey: string;\n  sig: string;\n} {\n  return (\n    'pubKey' in signer &&\n    'sig' in signer &&\n    signer.sig !== null &&\n    signer.pubKey.length > 0\n  );\n}\n"]}