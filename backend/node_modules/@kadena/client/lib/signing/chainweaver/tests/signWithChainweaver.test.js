"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const msw_1 = require("msw");
const node_1 = require("msw/node");
const vitest_1 = require("vitest");
const index_1 = require("../../../index");
const pact_1 = require("../../../pact");
const signWithChainweaver_1 = require("../signWithChainweaver");
const coin = (0, pact_1.getModule)('coin');
const server = (0, node_1.setupServer)();
(0, vitest_1.beforeAll)(() => server.listen({ onUnhandledRequest: 'error' }));
(0, vitest_1.afterEach)(() => server.resetHandlers());
(0, vitest_1.afterAll)(() => server.close());
const post = (path, response, status = 200, wait) => msw_1.http.post(path, async () => {
    await (0, msw_1.delay)(wait !== null && wait !== void 0 ? wait : 0);
    return typeof response === 'string'
        ? new msw_1.HttpResponse(response, { status })
        : msw_1.HttpResponse.json(response, { status });
}, { once: true });
(0, vitest_1.describe)('signWithChainweaver', () => {
    // Using signWithChainweaver directly is deprecated, but we still want to test it
    ['createSignWithChainweaver', 'signWithChainweaver'].forEach((fn) => {
        let signFn = signWithChainweaver_1.signWithChainweaver;
        (0, vitest_1.beforeAll)(() => {
            if (fn === 'createSignWithChainweaver') {
                signFn = (0, signWithChainweaver_1.createSignWithChainweaver)();
            }
        });
        (0, vitest_1.describe)(fn, () => {
            (0, vitest_1.it)('throws an error when nothing is to be signed', async () => {
                try {
                    await signFn(undefined);
                }
                catch (e) {
                    (0, vitest_1.expect)(e).toBeTruthy();
                }
            });
            (0, vitest_1.it)('throws when an empty response is returned', async () => {
                server.resetHandlers(post('http://127.0.0.1:9467/v1/quicksign', { responses: [] }));
                try {
                    await signFn([]);
                }
                catch (e) {
                    (0, vitest_1.expect)(e).toBeTruthy();
                }
            });
            (0, vitest_1.it)('throws when an error is returned', async () => {
                server.resetHandlers(post('http://127.0.0.1:9467/v1/quicksign', {
                    error: { type: 'other', msg: 'Oops!' },
                }));
                try {
                    await signFn([]);
                }
                catch (e) {
                    (0, vitest_1.expect)(e).toBeTruthy();
                }
            });
            (0, vitest_1.it)('makes a call on 127.0.0.1:9467/v1/quicksign with transaction', async () => {
                const builder = index_1.Pact.builder
                    .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
                    .addSigner('signer-key', (withCap) => [withCap('coin.GAS')]);
                const command = builder.getCommand();
                const unsignedTransaction = builder.createTransaction();
                const sigs = command.signers.map((sig, i) => {
                    return {
                        pubKey: command.signers[i].pubKey,
                        sig: null,
                    };
                });
                server.resetHandlers(msw_1.http.post('http://127.0.0.1:9467/v1/quicksign', async ({ request }) => {
                    const body = await request.json();
                    (0, vitest_1.expect)(request.headers.get('content-type')).toEqual('application/json;charset=utf-8');
                    (0, vitest_1.expect)(body).toStrictEqual({
                        cmdSigDatas: [{ cmd: unsignedTransaction.cmd, sigs }],
                    });
                    return msw_1.HttpResponse.json({ responses: [] });
                }));
                await signFn(unsignedTransaction);
            });
            (0, vitest_1.it)('throws when call fails', async () => {
                server.resetHandlers(post('http://127.0.0.1:9467/v1/quicksign', 'A system error occured', 500));
                const unsignedTransaction = index_1.Pact.builder
                    .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
                    .addSigner('', (withCap) => [withCap('coin.GAS')])
                    .setMeta({
                    senderAccount: '',
                    chainId: '0',
                })
                    .createTransaction();
                // expected: throws an error
                await (0, vitest_1.expect)(signFn(unsignedTransaction)).rejects.toThrow();
            });
            (0, vitest_1.it)(`adds signatures in multisig fashion to the transactions ${signFn}`, async () => {
                const mockedResponse = {
                    responses: [
                        {
                            commandSigData: {
                                cmd: '',
                                sigs: [{ pubKey: 'gas-signer-pubkey', sig: 'gas-key-sig' }],
                            },
                            outcome: {
                                hash: '',
                                result: 'success',
                            },
                        },
                    ],
                };
                // set a new mock response for the second signature
                const mockedResponse2 = {
                    responses: [
                        {
                            commandSigData: {
                                cmd: '',
                                sigs: [
                                    { pubKey: 'transfer-signer-pubkey', sig: 'transfer-key-sig' },
                                ],
                            },
                            outcome: {
                                hash: '',
                                result: 'success',
                            },
                        },
                    ],
                };
                server.resetHandlers(post('http://127.0.0.1:9467/v1/quicksign', mockedResponse), post('http://127.0.0.1:9467/v1/quicksign', mockedResponse2));
                const unsignedTransaction = index_1.Pact.builder
                    .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
                    .addSigner('gas-signer-pubkey', (withCap) => [withCap('coin.GAS')])
                    .addSigner('transfer-signer-pubkey', (withCap) => [
                    withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),
                ])
                    .setMeta({
                    senderAccount: '',
                    chainId: '0',
                })
                    .createTransaction();
                const txWithOneSig = await signFn(unsignedTransaction);
                (0, vitest_1.expect)(txWithOneSig.sigs).toStrictEqual([
                    { sig: 'gas-key-sig', pubKey: 'gas-signer-pubkey' },
                    { pubKey: 'transfer-signer-pubkey' },
                ]);
                const signedTx = await signFn(txWithOneSig);
                (0, vitest_1.expect)(signedTx.sigs).toEqual([
                    { sig: 'gas-key-sig', pubKey: 'gas-signer-pubkey' },
                    { sig: 'transfer-key-sig', pubKey: 'transfer-signer-pubkey' },
                ]);
            });
            (0, vitest_1.it)('signs but does not have the signer key and returns sig null', async () => {
                const mockedResponse = {
                    responses: [
                        {
                            commandSigData: {
                                cmd: '',
                                sigs: [{ pubKey: 'gas-signer-pubkey', sig: null }],
                            },
                            outcome: { result: 'noSig' },
                        },
                    ],
                };
                server.resetHandlers(post('http://127.0.0.1:9467/v1/quicksign', mockedResponse));
                const unsignedTransaction = index_1.Pact.builder
                    .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
                    .addSigner('gas-signer-pubkey', (withCap) => [withCap('coin.GAS')])
                    .createTransaction();
                const signedTransaction = await signFn(unsignedTransaction);
                (0, vitest_1.expect)(signedTransaction.sigs).toEqual([
                    { pubKey: 'gas-signer-pubkey' },
                ]);
            });
        });
    });
    (0, vitest_1.it)('makes a call on my-host.kadena:9467/v1/quicksign when chainweaverUrl is passed', async () => {
        const unsignedTransaction = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .addSigner('pubkey', (withCap) => [withCap('coin.GAS')])
            .addSigner('pubkey', (withCap) => [
            withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),
        ])
            .setMeta({
            senderAccount: '',
            chainId: '0',
        })
            .createTransaction();
        const mockedResponse = {
            responses: [
                {
                    commandSigData: {
                        cmd: '',
                        sigs: [{ pubKey: 'pubkey', sig: 'sig' }],
                    },
                    outcome: {
                        hash: '',
                        result: 'success',
                    },
                },
            ],
        };
        server.resetHandlers(post('http://my-host.kadena:9467/v1/quicksign', mockedResponse));
        const signWithChainweaver = (0, signWithChainweaver_1.createSignWithChainweaver)({
            host: 'http://my-host.kadena:9467',
        });
        const signedTx = await signWithChainweaver(unsignedTransaction);
        (0, vitest_1.expect)(signedTx.sigs).toStrictEqual([{ sig: 'sig', pubKey: 'pubkey' }]);
    });
});
//# sourceMappingURL=signWithChainweaver.test.js.map