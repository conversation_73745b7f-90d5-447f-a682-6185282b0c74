{"version": 3, "file": "signWithChainweaver.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/chainweaver/tests/signWithChainweaver.test.ts"], "names": [], "mappings": ";;AAAA,6BAAgD;AAChD,mCAAuC;AACvC,mCAA8E;AAM9E,0CAAsC;AACtC,wCAA0C;AAC1C,gEAGgC;AAEhC,MAAM,IAAI,GAAU,IAAA,gBAAS,EAAC,MAAM,CAAC,CAAC;AAEtC,MAAM,MAAM,GAAG,IAAA,kBAAW,GAAE,CAAC;AAC7B,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChE,IAAA,kBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;AACxC,IAAA,iBAAQ,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAE/B,MAAM,IAAI,GAAG,CACX,IAAY,EACZ,QAAqC,EACrC,MAAM,GAAG,GAAG,EACZ,IAAa,EACiB,EAAE,CAChC,UAAI,CAAC,IAAI,CACP,IAAI,EACJ,KAAK,IAAI,EAAE;IACT,MAAM,IAAA,WAAK,EAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,CAAC,CAAC,CAAC;IACvB,OAAO,OAAO,QAAQ,KAAK,QAAQ;QACjC,CAAC,CAAC,IAAI,kBAAY,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC;QACxC,CAAC,CAAC,kBAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;AAC9C,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;AAEJ,IAAA,iBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,iFAAiF;IACjF,CAAC,2BAA2B,EAAE,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;QAClE,IAAI,MAAM,GAAG,yCAAmB,CAAC;QACjC,IAAA,kBAAS,EAAC,GAAG,EAAE;YACb,IAAI,EAAE,KAAK,2BAA2B,EAAE,CAAC;gBACvC,MAAM,GAAG,IAAA,+CAAyB,GAAE,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,iBAAQ,EAAC,EAAE,EAAE,GAAG,EAAE;YAChB,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;gBAC5D,IAAI,CAAC;oBACH,MAAO,MAA+B,CAAC,SAAS,CAAC,CAAC;gBACpD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAA,eAAM,EAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBACzB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,oCAAoC,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAC9D,CAAC;gBAEF,IAAI,CAAC;oBACH,MAAO,MAA+B,CAAC,EAAE,CAAC,CAAC;gBAC7C,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAA,eAAM,EAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBACzB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAA,WAAE,EAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;gBAChD,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,oCAAoC,EAAE;oBACzC,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;iBACvC,CAAC,CACH,CAAC;gBAEF,IAAI,CAAC;oBACH,MAAO,MAA+B,CAAC,EAAE,CAAC,CAAC;gBAC7C,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAA,eAAM,EAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBACzB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAA,WAAE,EAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;gBAC5E,MAAM,OAAO,GAAG,YAAI,CAAC,OAAO;qBACzB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;qBAC9D,SAAS,CAAC,YAAY,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAE/D,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrC,MAAM,mBAAmB,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAExD,MAAM,IAAI,GAAG,OAAO,CAAC,OAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;oBAC3C,OAAO;wBACL,MAAM,EAAE,OAAO,CAAC,OAAQ,CAAC,CAAC,CAAC,CAAC,MAAM;wBAClC,GAAG,EAAE,IAAI;qBACV,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,aAAa,CAClB,UAAI,CAAC,IAAI,CACP,oCAAoC,EACpC,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;oBACpB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;oBAElC,IAAA,eAAM,EAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CACjD,gCAAgC,CACjC,CAAC;oBAEF,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,aAAa,CAAC;wBACzB,WAAW,EAAE,CAAC,EAAE,GAAG,EAAE,mBAAmB,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;qBACtD,CAAC,CAAC;oBAEH,OAAO,kBAAY,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC9C,CAAC,CACF,CACF,CAAC;gBAEF,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAA,WAAE,EAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;gBACtC,MAAM,CAAC,aAAa,CAClB,IAAI,CACF,oCAAoC,EACpC,wBAAwB,EACxB,GAAG,CACJ,CACF,CAAC;gBAEF,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;qBACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;qBAC9D,SAAS,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;qBACjD,OAAO,CAAC;oBACP,aAAa,EAAE,EAAE;oBACjB,OAAO,EAAE,GAAG;iBACb,CAAC;qBACD,iBAAiB,EAAE,CAAC;gBAEvB,4BAA4B;gBAC5B,MAAM,IAAA,eAAM,EAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,IAAA,WAAE,EAAC,2DAA2D,MAAM,EAAE,EAAE,KAAK,IAAI,EAAE;gBACjF,MAAM,cAAc,GAA+B;oBACjD,SAAS,EAAE;wBACT;4BACE,cAAc,EAAE;gCACd,GAAG,EAAE,EAAE;gCACP,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,mBAAmB,EAAE,GAAG,EAAE,aAAa,EAAE,CAAC;6BAC5D;4BACD,OAAO,EAAE;gCACP,IAAI,EAAE,EAAE;gCACR,MAAM,EAAE,SAAS;6BAClB;yBACF;qBACF;iBACF,CAAC;gBAEF,mDAAmD;gBACnD,MAAM,eAAe,GAA+B;oBAClD,SAAS,EAAE;wBACT;4BACE,cAAc,EAAE;gCACd,GAAG,EAAE,EAAE;gCACP,IAAI,EAAE;oCACJ,EAAE,MAAM,EAAE,wBAAwB,EAAE,GAAG,EAAE,kBAAkB,EAAE;iCAC9D;6BACF;4BACD,OAAO,EAAE;gCACP,IAAI,EAAE,EAAE;gCACR,MAAM,EAAE,SAAS;6BAClB;yBACF;qBACF;iBACF,CAAC;gBAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,oCAAoC,EAAE,cAAc,CAAC,EAC1D,IAAI,CAAC,oCAAoC,EAAE,eAAe,CAAC,CAC5D,CAAC;gBAEF,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;qBACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;qBAC9D,SAAS,CAAC,mBAAmB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;qBAClE,SAAS,CAAC,wBAAwB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;oBAChD,OAAO,CAAC,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;iBACjE,CAAC;qBACD,OAAO,CAAC;oBACP,aAAa,EAAE,EAAE;oBACjB,OAAO,EAAE,GAAG;iBACb,CAAC;qBACD,iBAAiB,EAAE,CAAC;gBAEvB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;gBAEvD,IAAA,eAAM,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBACtC,EAAE,GAAG,EAAE,aAAa,EAAE,MAAM,EAAE,mBAAmB,EAAE;oBACnD,EAAE,MAAM,EAAE,wBAAwB,EAAE;iBACrC,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC5C,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,EAAE,GAAG,EAAE,aAAa,EAAE,MAAM,EAAE,mBAAmB,EAAE;oBACnD,EAAE,GAAG,EAAE,kBAAkB,EAAE,MAAM,EAAE,wBAAwB,EAAE;iBAC9D,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAA,WAAE,EAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;gBAC3E,MAAM,cAAc,GAA+B;oBACjD,SAAS,EAAE;wBACT;4BACE,cAAc,EAAE;gCACd,GAAG,EAAE,EAAE;gCACP,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;6BACnD;4BACD,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;yBAC7B;qBACF;iBACF,CAAC;gBAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,oCAAoC,EAAE,cAAc,CAAC,CAC3D,CAAC;gBAEF,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;qBACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;qBAC9D,SAAS,CAAC,mBAAmB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;qBAClE,iBAAiB,EAAE,CAAC;gBAEvB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;gBAE5D,IAAA,eAAM,EAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBACrC,EAAE,MAAM,EAAE,mBAAmB,EAAE;iBAChC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gFAAgF,EAAE,KAAK,IAAI,EAAE;QAC9F,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;aACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,SAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aACvD,SAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;SACjE,CAAC;aACD,OAAO,CAAC;YACP,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,GAAG;SACb,CAAC;aACD,iBAAiB,EAAE,CAAC;QAEvB,MAAM,cAAc,GAA+B;YACjD,SAAS,EAAE;gBACT;oBACE,cAAc,EAAE;wBACd,GAAG,EAAE,EAAE;wBACP,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;qBACzC;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,SAAS;qBAClB;iBACF;aACF;SACF,CAAC;QAEF,MAAM,CAAC,aAAa,CAClB,IAAI,CAAC,yCAAyC,EAAE,cAAc,CAAC,CAChE,CAAC;QAEF,MAAM,mBAAmB,GAAG,IAAA,+CAAyB,EAAC;YACpD,IAAI,EAAE,4BAA4B;SACnC,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;QAEhE,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { HttpResponse, delay, http } from 'msw';\nimport { setupServer } from 'msw/node';\nimport { afterAll, afterEach, beforeAll, describe, expect, it } from 'vitest';\nimport type { ICoin } from '../../../composePactCommand/test/coin-contract';\nimport type {\n  IQuicksignResponse,\n  IQuicksignResponseOutcomes,\n} from '../../../index';\nimport { Pact } from '../../../index';\nimport { getModule } from '../../../pact';\nimport {\n  createSignWithChainweaver,\n  signWithChainweaver,\n} from '../signWithChainweaver';\n\nconst coin: ICoin = getModule('coin');\n\nconst server = setupServer();\nbeforeAll(() => server.listen({ onUnhandledRequest: 'error' }));\nafterEach(() => server.resetHandlers());\nafterAll(() => server.close());\n\nconst post = (\n  path: string,\n  response: string | IQuicksignResponse,\n  status = 200,\n  wait?: number,\n): ReturnType<typeof http.post> =>\n  http.post(\n    path,\n    async () => {\n      await delay(wait ?? 0);\n      return typeof response === 'string'\n        ? new HttpResponse(response, { status })\n        : HttpResponse.json(response, { status });\n    },\n    { once: true },\n  );\n\ndescribe('signWithChainweaver', () => {\n  // Using signWithChainweaver directly is deprecated, but we still want to test it\n  ['createSignWithChainweaver', 'signWithChainweaver'].forEach((fn) => {\n    let signFn = signWithChainweaver;\n    beforeAll(() => {\n      if (fn === 'createSignWithChainweaver') {\n        signFn = createSignWithChainweaver();\n      }\n    });\n\n    describe(fn, () => {\n      it('throws an error when nothing is to be signed', async () => {\n        try {\n          await (signFn as (arg: unknown) => {})(undefined);\n        } catch (e) {\n          expect(e).toBeTruthy();\n        }\n      });\n\n      it('throws when an empty response is returned', async () => {\n        server.resetHandlers(\n          post('http://127.0.0.1:9467/v1/quicksign', { responses: [] }),\n        );\n\n        try {\n          await (signFn as (arg: unknown) => {})([]);\n        } catch (e) {\n          expect(e).toBeTruthy();\n        }\n      });\n\n      it('throws when an error is returned', async () => {\n        server.resetHandlers(\n          post('http://127.0.0.1:9467/v1/quicksign', {\n            error: { type: 'other', msg: 'Oops!' },\n          }),\n        );\n\n        try {\n          await (signFn as (arg: unknown) => {})([]);\n        } catch (e) {\n          expect(e).toBeTruthy();\n        }\n      });\n\n      it('makes a call on 127.0.0.1:9467/v1/quicksign with transaction', async () => {\n        const builder = Pact.builder\n          .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n          .addSigner('signer-key', (withCap) => [withCap('coin.GAS')]);\n\n        const command = builder.getCommand();\n        const unsignedTransaction = builder.createTransaction();\n\n        const sigs = command.signers!.map((sig, i) => {\n          return {\n            pubKey: command.signers![i].pubKey,\n            sig: null,\n          };\n        });\n\n        server.resetHandlers(\n          http.post(\n            'http://127.0.0.1:9467/v1/quicksign',\n            async ({ request }) => {\n              const body = await request.json();\n\n              expect(request.headers.get('content-type')).toEqual(\n                'application/json;charset=utf-8',\n              );\n\n              expect(body).toStrictEqual({\n                cmdSigDatas: [{ cmd: unsignedTransaction.cmd, sigs }],\n              });\n\n              return HttpResponse.json({ responses: [] });\n            },\n          ),\n        );\n\n        await signFn(unsignedTransaction);\n      });\n\n      it('throws when call fails', async () => {\n        server.resetHandlers(\n          post(\n            'http://127.0.0.1:9467/v1/quicksign',\n            'A system error occured',\n            500,\n          ),\n        );\n\n        const unsignedTransaction = Pact.builder\n          .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n          .addSigner('', (withCap) => [withCap('coin.GAS')])\n          .setMeta({\n            senderAccount: '',\n            chainId: '0',\n          })\n          .createTransaction();\n\n        // expected: throws an error\n        await expect(signFn(unsignedTransaction)).rejects.toThrow();\n      });\n\n      it(`adds signatures in multisig fashion to the transactions ${signFn}`, async () => {\n        const mockedResponse: IQuicksignResponseOutcomes = {\n          responses: [\n            {\n              commandSigData: {\n                cmd: '',\n                sigs: [{ pubKey: 'gas-signer-pubkey', sig: 'gas-key-sig' }],\n              },\n              outcome: {\n                hash: '',\n                result: 'success',\n              },\n            },\n          ],\n        };\n\n        // set a new mock response for the second signature\n        const mockedResponse2: IQuicksignResponseOutcomes = {\n          responses: [\n            {\n              commandSigData: {\n                cmd: '',\n                sigs: [\n                  { pubKey: 'transfer-signer-pubkey', sig: 'transfer-key-sig' },\n                ],\n              },\n              outcome: {\n                hash: '',\n                result: 'success',\n              },\n            },\n          ],\n        };\n\n        server.resetHandlers(\n          post('http://127.0.0.1:9467/v1/quicksign', mockedResponse),\n          post('http://127.0.0.1:9467/v1/quicksign', mockedResponse2),\n        );\n\n        const unsignedTransaction = Pact.builder\n          .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n          .addSigner('gas-signer-pubkey', (withCap) => [withCap('coin.GAS')])\n          .addSigner('transfer-signer-pubkey', (withCap) => [\n            withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),\n          ])\n          .setMeta({\n            senderAccount: '',\n            chainId: '0',\n          })\n          .createTransaction();\n\n        const txWithOneSig = await signFn(unsignedTransaction);\n\n        expect(txWithOneSig.sigs).toStrictEqual([\n          { sig: 'gas-key-sig', pubKey: 'gas-signer-pubkey' },\n          { pubKey: 'transfer-signer-pubkey' },\n        ]);\n\n        const signedTx = await signFn(txWithOneSig);\n        expect(signedTx.sigs).toEqual([\n          { sig: 'gas-key-sig', pubKey: 'gas-signer-pubkey' },\n          { sig: 'transfer-key-sig', pubKey: 'transfer-signer-pubkey' },\n        ]);\n      });\n\n      it('signs but does not have the signer key and returns sig null', async () => {\n        const mockedResponse: IQuicksignResponseOutcomes = {\n          responses: [\n            {\n              commandSigData: {\n                cmd: '',\n                sigs: [{ pubKey: 'gas-signer-pubkey', sig: null }],\n              },\n              outcome: { result: 'noSig' },\n            },\n          ],\n        };\n\n        server.resetHandlers(\n          post('http://127.0.0.1:9467/v1/quicksign', mockedResponse),\n        );\n\n        const unsignedTransaction = Pact.builder\n          .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n          .addSigner('gas-signer-pubkey', (withCap) => [withCap('coin.GAS')])\n          .createTransaction();\n\n        const signedTransaction = await signFn(unsignedTransaction);\n\n        expect(signedTransaction.sigs).toEqual([\n          { pubKey: 'gas-signer-pubkey' },\n        ]);\n      });\n    });\n  });\n\n  it('makes a call on my-host.kadena:9467/v1/quicksign when chainweaverUrl is passed', async () => {\n    const unsignedTransaction = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .addSigner('pubkey', (withCap) => [withCap('coin.GAS')])\n      .addSigner('pubkey', (withCap) => [\n        withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),\n      ])\n      .setMeta({\n        senderAccount: '',\n        chainId: '0',\n      })\n      .createTransaction();\n\n    const mockedResponse: IQuicksignResponseOutcomes = {\n      responses: [\n        {\n          commandSigData: {\n            cmd: '',\n            sigs: [{ pubKey: 'pubkey', sig: 'sig' }],\n          },\n          outcome: {\n            hash: '',\n            result: 'success',\n          },\n        },\n      ],\n    };\n\n    server.resetHandlers(\n      post('http://my-host.kadena:9467/v1/quicksign', mockedResponse),\n    );\n\n    const signWithChainweaver = createSignWithChainweaver({\n      host: 'http://my-host.kadena:9467',\n    });\n    const signedTx = await signWithChainweaver(unsignedTransaction);\n\n    expect(signedTx.sigs).toStrictEqual([{ sig: 'sig', pubKey: 'pubkey' }]);\n  });\n});\n"]}