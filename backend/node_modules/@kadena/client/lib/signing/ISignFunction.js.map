{"version": 3, "file": "ISignFunction.js", "sourceRoot": "", "sources": ["../../src/signing/ISignFunction.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\n\n/**\n * Interface to use when writing a signing function that accepts a single transaction\n * @public\n */\nexport interface ISingleSignFunction {\n  (transaction: IUnsignedCommand): Promise<ICommand | IUnsignedCommand>;\n}\n\n/**\n * Interface to use when writing a signing function that accepts multiple transactions\n * @public\n */\nexport interface ISignFunction extends ISingleSignFunction {\n  (\n    transactionList: IUnsignedCommand[],\n  ): Promise<(ICommand | IUnsignedCommand)[]>;\n}\n"]}