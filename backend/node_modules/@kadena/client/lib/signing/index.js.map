{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/signing/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAWA,wDAAsC;AACtC,8DAA4C;AAE5C,oEAAkD;AAClD,uEAAqD;AACrD,kEAAgD;AAChD,kEAAgD;AAChD,6EAA2D;AAC3D,wEAAsD", "sourcesContent": ["export { IUnsignedCommand } from '@kadena/types';\nexport {\n  EckoStatus,\n  ICommonEckoFunctions,\n  IEckoConnectOrStatusResponse,\n  IEckoSignFunction,\n  IEckoSignSingleFunction,\n} from './eckoWallet/eckoTypes';\nexport { ISignFunction, ISingleSignFunction } from './ISignFunction';\nexport { TWalletConnectChainId } from './walletconnect/walletConnectTypes';\n\nexport * from './utils/addSignatures';\nexport * from './utils/isSignedTransaction';\n\nexport * from './chainweaver/signWithChainweaver';\nexport * from './eckoWallet/quicksignWithEckoWallet';\nexport * from './eckoWallet/signWithEckoWallet';\nexport * from './keypair/createSignWithKeypair';\nexport * from './walletconnect/quicksignWithWalletConnect';\nexport * from './walletconnect/signWithWalletConnect';\n"]}