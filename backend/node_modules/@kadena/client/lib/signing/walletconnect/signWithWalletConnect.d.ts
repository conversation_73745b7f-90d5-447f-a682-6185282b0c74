import type Client from '@walletconnect/sign-client';
import type { SessionTypes } from '@walletconnect/types';
import type { ISingleSignFunction } from '../ISignFunction';
import type { TWalletConnectChainId } from './walletConnectTypes';
/**
 * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithWalletConnect} function
 *
 * @public
 */
export declare function createSignWithWalletConnect(client: Client, session: SessionTypes.Struct, walletConnectChainId: TWalletConnectChainId): ISingleSignFunction;
/**
 * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithWalletConnect} function
 *
 * @deprecated Use {@link createSignWithWalletConnect} instead
 * @public
 */
export declare const createWalletConnectSign: typeof createSignWithWalletConnect;
//# sourceMappingURL=signWithWalletConnect.d.ts.map