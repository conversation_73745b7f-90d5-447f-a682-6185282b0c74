import type Client from '@walletconnect/sign-client';
import type { SessionTypes } from '@walletconnect/types';
import type { ISignFunction } from '../ISignFunction';
import type { TWalletConnectChainId } from './walletConnectTypes';
/**
 * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @public
 */
export declare function createQuicksignWithWalletConnect(client: Client, session: SessionTypes.Struct, walletConnectChainId: TWalletConnectChainId): ISignFunction;
/**
 * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 *
 * @deprecated Use {@link createQuicksignWithWalletConnect} instead
 * @public
 */
export declare const createWalletConnectQuicksign: typeof createQuicksignWithWalletConnect;
//# sourceMappingURL=quicksignWithWalletConnect.d.ts.map