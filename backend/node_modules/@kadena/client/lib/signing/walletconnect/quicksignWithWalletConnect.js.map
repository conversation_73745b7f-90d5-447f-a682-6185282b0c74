{"version": 3, "file": "quicksignWithWalletConnect.js", "sourceRoot": "", "sources": ["../../../src/signing/walletconnect/quicksignWithWalletConnect.ts"], "names": [], "mappings": ";;;AAKA,0DAAuD;AACvD,8EAA2E;AAG3E;;;;GAIG;AACH,SAAgB,gCAAgC,CAC9C,MAAc,EACd,OAA4B,EAC5B,oBAA2C;IAE3C,MAAM,0BAA0B,GAAkB,CAAC,KAAK,EACtD,eAAsE,EACtE,EAAE;QACF,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAElE,MAAM,iBAAiB,GAAa,EAAE,CAAC;QAEvC,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACvD,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;YAClC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,iDAAuB,EAAC,WAAW,CAAC,CAAC;YACzD,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7B,OAAO;gBACL,GAAG;gBACH,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;oBAAC,OAAA,CAAC;wBAChC,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,GAAG,EAAE,MAAA,MAAA,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,0CAAE,GAAG,mCAAI,IAAI;qBACtC,CAAC,CAAA;iBAAA,CAAC;aACJ,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG;YACvB,eAAe;SAChB,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,EAAE,EAAE,CAAC;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,gBAAgB;SACzB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAqB;YACxD,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,oBAAoB;YAC7B,OAAO,EAAE,kBAAkB;SAC5B,CAAC,CAAC;QAEH,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,WAAW,IAAI,QAAQ,EAAE,CAAC;YAC5B,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC/C,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,KAAK,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;wBACxD,MAAM,IAAI,KAAK,CACb,0EAA0E,iBAAiB,CAAC,CAAC,CAAC,kBAAkB,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,CAC7I,CAAC;oBACJ,CAAC;oBAED,MAAM,IAAI,GAAG,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CACnD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,CACW,CAAC;oBAEvC,8EAA8E;oBAC9E,YAAY,CAAC,CAAC,CAAC,GAAG,IAAA,6BAAa,EAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,CAAkB,CAAC;IAEpB,OAAO,0BAA0B,CAAC;AACpC,CAAC;AA5ED,4EA4EC;AAED;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,gCAAgC,CAAC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\nimport type Client from '@walletconnect/sign-client';\nimport type { SessionTypes } from '@walletconnect/types';\nimport type { IQuicksignResponse } from '../../signing-api/v1/quicksign';\nimport type { ISignFunction } from '../ISignFunction';\nimport { addSignatures } from '../utils/addSignatures';\nimport { parseTransactionCommand } from '../utils/parseTransactionCommand';\nimport type { TWalletConnectChainId } from './walletConnectTypes';\n\n/**\n * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}\n *\n * @public\n */\nexport function createQuicksignWithWalletConnect(\n  client: Client,\n  session: SessionTypes.Struct,\n  walletConnectChainId: TWalletConnectChainId,\n): ISignFunction {\n  const quicksignWithWalletConnect: ISignFunction = (async (\n    transactionList: IUnsignedCommand | Array<IUnsignedCommand | ICommand>,\n  ) => {\n    if (transactionList === undefined) {\n      throw new Error('No transaction(s) to sign');\n    }\n    const isList = Array.isArray(transactionList);\n    const transactions = isList ? transactionList : [transactionList];\n\n    const transactionHashes: string[] = [];\n\n    const commandSigDatas = transactions.map((pactCommand) => {\n      const { cmd, hash } = pactCommand;\n      const { signers } = parseTransactionCommand(pactCommand);\n      transactionHashes.push(hash);\n\n      return {\n        cmd,\n        sigs: signers.map((signer, i) => ({\n          pubKey: signer.pubKey,\n          sig: pactCommand.sigs[i]?.sig ?? null,\n        })),\n      };\n    });\n\n    const quickSignRequest = {\n      commandSigDatas,\n    };\n\n    const transactionRequest = {\n      id: 1,\n      jsonrpc: '2.0',\n      method: 'kadena_quicksign_v1',\n      params: quickSignRequest,\n    };\n\n    const response = await client.request<IQuicksignResponse>({\n      topic: session.topic,\n      chainId: walletConnectChainId,\n      request: transactionRequest,\n    });\n\n    if (response === undefined) {\n      throw new Error('Error signing transaction');\n    }\n\n    if ('responses' in response) {\n      response.responses.map((signedCommand, i) => {\n        if (signedCommand.outcome.result === 'success') {\n          if (signedCommand.outcome.hash !== transactionHashes[i]) {\n            throw new Error(\n              `Hash of the transaction signed by the wallet does not match. Our hash: ${transactionHashes[i]}, wallet hash: ${signedCommand.outcome.hash}`,\n            );\n          }\n\n          const sigs = signedCommand.commandSigData.sigs.filter(\n            (sig) => sig.sig !== null,\n          ) as { pubKey: string; sig: string }[];\n\n          // Add the signature(s) that we received from the wallet to the PactCommand(s)\n          transactions[i] = addSignatures(transactions[i], ...sigs);\n        }\n      });\n    } else {\n      throw new Error('Error signing transaction');\n    }\n\n    return isList ? transactions : transactions[0];\n  }) as ISignFunction;\n\n  return quicksignWithWalletConnect;\n}\n\n/**\n * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}\n *\n *\n * @deprecated Use {@link createQuicksignWithWalletConnect} instead\n * @public\n */\nexport const createWalletConnectQuicksign = createQuicksignWithWalletConnect;\n"]}