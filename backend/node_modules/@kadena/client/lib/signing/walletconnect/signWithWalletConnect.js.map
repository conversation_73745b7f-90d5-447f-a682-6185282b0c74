{"version": 3, "file": "signWithWalletConnect.js", "sourceRoot": "", "sources": ["../../../src/signing/walletconnect/signWithWalletConnect.ts"], "names": [], "mappings": ";;;AAIA,kEAA+D;AAE/D,8EAA2E;AAO3E;;;;;;;GAOG;AACH,SAAgB,2BAA2B,CACzC,MAAc,EACd,OAA4B,EAC5B,oBAA2C;IAE3C,MAAM,qBAAqB,GAAwB,KAAK,EAAE,WAAW,EAAE,EAAE;;QACvE,MAAM,iBAAiB,GAAG,IAAA,iDAAuB,EAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAA,6BAAa,EAAC,iBAAiB,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,cAAc,GAAoB;YACtC,IAAI,EAAE,MAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,mCAAI,EAAE;YAC/C,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAkC;YACvE,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACjD,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;oBAC/B,OAAO,EAAE,CAAC;gBACZ,CAAC;gBACD,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;oBACzC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAEhC,OAAO;wBACL,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;wBACjC,WAAW,EAAE,mBAAmB,IAAI,EAAE;wBACtC,GAAG,EAAE;4BACH,IAAI;4BACJ,IAAI;yBACL;qBACF,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YACF,KAAK,EAAE,iBAAiB,CAAC,KAAK;YAC9B,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC,OAAO;YACvC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ;YACzC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ;YACzC,MAAM,EAAE,iBAAiB,CAAC,IAAI,CAAC,MAAM;YACrC,GAAG,EAAE,iBAAiB,CAAC,IAAI,CAAC,GAAG;SAChC,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,EAAE,EAAE,CAAC;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,cAAc;SACvB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAmB;YACtD,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,oBAAoB;YAC7B,OAAO,EAAE,kBAAkB;SAC5B,CAAC,CAAC;QAEH,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,SAAS,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC;IAEF,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AA5DD,kEA4DC;AAED;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,2BAA2B,CAAC", "sourcesContent": ["import type { ICommand, IUnsignedCommand } from '@kadena/types';\nimport type Client from '@walletconnect/sign-client';\nimport type { SessionTypes } from '@walletconnect/types';\nimport type { ISigningRequest } from '../../interfaces/ISigningRequest';\nimport { isExecCommand } from '../../interfaces/isExecCommand';\nimport type { ISingleSignFunction } from '../ISignFunction';\nimport { parseTransactionCommand } from '../utils/parseTransactionCommand';\nimport type { TWalletConnectChainId } from './walletConnectTypes';\n\ninterface ISigningResponse {\n  body: ICommand | IUnsignedCommand;\n}\n\n/**\n * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}\n *\n * @remarks\n * It is preferred to use the {@link createQuicksignWithWalletConnect} function\n *\n * @public\n */\nexport function createSignWithWalletConnect(\n  client: Client,\n  session: SessionTypes.Struct,\n  walletConnectChainId: TWalletConnectChainId,\n): ISingleSignFunction {\n  const signWithWalletConnect: ISingleSignFunction = async (transaction) => {\n    const parsedTransaction = parseTransactionCommand(transaction);\n    if (!isExecCommand(parsedTransaction)) {\n      throw new Error('`cont` transactions are not supported');\n    }\n\n    const signingRequest: ISigningRequest = {\n      code: parsedTransaction.payload.exec.code ?? '',\n      data: parsedTransaction.payload.exec.data as { [key: string]: unknown },\n      caps: parsedTransaction.signers.flatMap((signer) => {\n        if (signer.clist === undefined) {\n          return [];\n        }\n        return signer.clist.map(({ name, args }) => {\n          const nameArr = name.split('.');\n\n          return {\n            role: nameArr[nameArr.length - 1],\n            description: `Description for ${name}`,\n            cap: {\n              name,\n              args,\n            },\n          };\n        });\n      }),\n      nonce: parsedTransaction.nonce,\n      chainId: parsedTransaction.meta.chainId,\n      gasLimit: parsedTransaction.meta.gasLimit,\n      gasPrice: parsedTransaction.meta.gasPrice,\n      sender: parsedTransaction.meta.sender,\n      ttl: parsedTransaction.meta.ttl,\n    };\n\n    const transactionRequest = {\n      id: 1,\n      jsonrpc: '2.0',\n      method: 'kadena_sign_v1',\n      params: signingRequest,\n    };\n\n    const response = await client.request<ISigningResponse>({\n      topic: session.topic,\n      chainId: walletConnectChainId,\n      request: transactionRequest,\n    });\n\n    if (response?.body === undefined) {\n      throw new Error('Error signing transaction');\n    }\n\n    return response.body;\n  };\n\n  return signWithWalletConnect;\n}\n\n/**\n * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}\n *\n * @remarks\n * It is preferred to use the {@link createQuicksignWithWalletConnect} function\n *\n * @deprecated Use {@link createSignWithWalletConnect} instead\n * @public\n */\nexport const createWalletConnectSign = createSignWithWalletConnect;\n"]}