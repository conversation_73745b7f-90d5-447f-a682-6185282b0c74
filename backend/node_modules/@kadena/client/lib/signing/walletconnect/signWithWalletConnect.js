"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWalletConnectSign = exports.createSignWithWalletConnect = void 0;
const isExecCommand_1 = require("../../interfaces/isExecCommand");
const parseTransactionCommand_1 = require("../utils/parseTransactionCommand");
/**
 * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithWalletConnect} function
 *
 * @public
 */
function createSignWithWalletConnect(client, session, walletConnectChainId) {
    const signWithWalletConnect = async (transaction) => {
        var _a;
        const parsedTransaction = (0, parseTransactionCommand_1.parseTransactionCommand)(transaction);
        if (!(0, isExecCommand_1.isExecCommand)(parsedTransaction)) {
            throw new Error('`cont` transactions are not supported');
        }
        const signingRequest = {
            code: (_a = parsedTransaction.payload.exec.code) !== null && _a !== void 0 ? _a : '',
            data: parsedTransaction.payload.exec.data,
            caps: parsedTransaction.signers.flatMap((signer) => {
                if (signer.clist === undefined) {
                    return [];
                }
                return signer.clist.map(({ name, args }) => {
                    const nameArr = name.split('.');
                    return {
                        role: nameArr[nameArr.length - 1],
                        description: `Description for ${name}`,
                        cap: {
                            name,
                            args,
                        },
                    };
                });
            }),
            nonce: parsedTransaction.nonce,
            chainId: parsedTransaction.meta.chainId,
            gasLimit: parsedTransaction.meta.gasLimit,
            gasPrice: parsedTransaction.meta.gasPrice,
            sender: parsedTransaction.meta.sender,
            ttl: parsedTransaction.meta.ttl,
        };
        const transactionRequest = {
            id: 1,
            jsonrpc: '2.0',
            method: 'kadena_sign_v1',
            params: signingRequest,
        };
        const response = await client.request({
            topic: session.topic,
            chainId: walletConnectChainId,
            request: transactionRequest,
        });
        if ((response === null || response === void 0 ? void 0 : response.body) === undefined) {
            throw new Error('Error signing transaction');
        }
        return response.body;
    };
    return signWithWalletConnect;
}
exports.createSignWithWalletConnect = createSignWithWalletConnect;
/**
 * Creates the signWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @remarks
 * It is preferred to use the {@link createQuicksignWithWalletConnect} function
 *
 * @deprecated Use {@link createSignWithWalletConnect} instead
 * @public
 */
exports.createWalletConnectSign = createSignWithWalletConnect;
//# sourceMappingURL=signWithWalletConnect.js.map