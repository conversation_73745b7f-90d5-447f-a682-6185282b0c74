{"version": 3, "file": "signWithWalletConnect.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/walletconnect/tests/signWithWalletConnect.test.ts"], "names": [], "mappings": ";;AAAA,mCAAkD;AAKlD,wEAAqE;AACrE,oEAAuE;AAQvE,IAAA,iBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAc;QAC7C,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,IAAI,EAAE,oCAAoC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;SACF;QACD,IAAI,EAAE;YACJ,OAAO,EAAE,GAAG;YACZ,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,UAAU;YACpB,MAAM,EAAE,aAAa;YACrB,GAAG,EAAE,IAAI;YACT,YAAY,EAAE,SAAS;SACxB;QACD,OAAO,EAAE;YACP;gBACE,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,mBAAmB;wBACzB,IAAI,EAAE,CAAC,cAAc,CAAC;qBACvB;iBACF;aACF;SACF;QACD,SAAS,EAAE,iBAAiB;QAC5B,KAAK,EAAE,UAAU;KAClB,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,YAAY,EAAoC,CAAC;IAC1E,MAAM,oBAAoB,GAA0B,kBAAkB,CAAC;IAEvE,IAAA,WAAE,EAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACnC,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,IAAI,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE;gBACtD,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAA,mDAA2B,EACvD,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,iBAAiB,GAAG,MAAM,qBAAqB,CACnD,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAC/B,CAAC;QAEF,IAAA,eAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;YAC1C,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,oBAAoB;YAC7B,OAAO,EAAE;gBACP,EAAE,EAAE,CAAC;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE;oBACN,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,eAAe;4BACrB,WAAW,EAAE,mCAAmC;4BAChD,GAAG,EAAE;gCACH,IAAI,EAAE,mBAAmB;gCACzB,IAAI,EAAE,CAAC,cAAc,CAAC;6BACvB;yBACF;qBACF;oBACD,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO;oBACjC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM;oBAC/B,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG;iBAC1B;aACF;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAA,eAAM,EAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;YAChC,GAAG,EAAE,UAAU;YACf,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;SAC5B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAA,mDAA2B,EACvD,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,EAAE,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;QACxC,aAAa;QACb,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAEvB,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,qBAAqB,CAAC,IAAA,qCAAiB,EAAC,EAAE,CAAC,CAAC,CAC7C,CAAC,OAAO,CAAC,YAAY,CAAC,uCAAuC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;QAClE,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,IAAI,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE;gBACtD,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAA,mDAA2B,EACvD,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,EAAE,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;QACxC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAE3B,MAAM,qBAAqB,CAAC,IAAA,qCAAiB,EAAC,EAAE,CAAC,CAAC,CAAC;QAEnD,IAAA,eAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;YAC1C,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,oBAAoB;YAC7B,OAAO,EAAE;gBACP,EAAE,EAAE,CAAC;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE;oBACN,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;oBACnC,IAAI,EAAE,EAAE;oBACR,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO;oBACjC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;oBACnC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM;oBAC/B,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG;iBAC1B;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAA,mDAA2B,EACvD,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,qBAAqB,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CACtD,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it, vi } from 'vitest';\nimport type {\n  IExecutionPayloadObject,\n  IPactCommand,\n} from '../../../interfaces/IPactCommand';\nimport { createTransaction } from '../../../utils/createTransaction';\nimport { createSignWithWalletConnect } from '../signWithWalletConnect';\nimport type { TWalletConnectChainId } from '../walletConnectTypes';\n\nimport type Client from '@walletconnect/sign-client';\nimport type { SessionTypes } from '@walletconnect/types';\n\ntype Transaction = IPactCommand & { payload: IExecutionPayloadObject };\n\ndescribe('signWithWalletConnect', () => {\n  const transaction = Object.freeze<Transaction>({\n    payload: {\n      exec: {\n        code: '(coin.transfer \"bonnie\" \"clyde\" 1)',\n        data: {\n          test: 'test-data',\n        },\n      },\n    },\n    meta: {\n      chainId: '0',\n      gasLimit: 2300,\n      gasPrice: 0.00000001,\n      sender: 'test-sender',\n      ttl: 3600,\n      creationTime: 123456789,\n    },\n    signers: [\n      {\n        pubKey: '',\n        clist: [\n          {\n            name: 'cap.test-cap-name',\n            args: ['test-cap-arg'],\n          },\n        ],\n      },\n    ],\n    networkId: 'test-network-id',\n    nonce: 'kjs-test',\n  });\n\n  const session = { topic: 'test-topic' } as unknown as SessionTypes.Struct;\n  const walletConnectChainId: TWalletConnectChainId = 'kadena:testnet04';\n\n  it('signs a transaction', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          body: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    const signWithWalletConnect = createSignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    const signedTransaction = await signWithWalletConnect(\n      createTransaction(transaction),\n    );\n\n    expect(client.request).toHaveBeenCalledWith({\n      topic: session.topic,\n      chainId: walletConnectChainId,\n      request: {\n        id: 1,\n        jsonrpc: '2.0',\n        method: 'kadena_sign_v1',\n        params: {\n          code: transaction.payload.exec.code,\n          data: transaction.payload.exec.data,\n          caps: [\n            {\n              role: 'test-cap-name',\n              description: 'Description for cap.test-cap-name',\n              cap: {\n                name: 'cap.test-cap-name',\n                args: ['test-cap-arg'],\n              },\n            },\n          ],\n          nonce: transaction.nonce,\n          chainId: transaction.meta.chainId,\n          gasLimit: transaction.meta.gasLimit,\n          gasPrice: transaction.meta.gasPrice,\n          sender: transaction.meta.sender,\n          ttl: transaction.meta.ttl,\n        },\n      },\n    });\n\n    expect(signedTransaction.cmd).toBe('test-cmd');\n\n    expect(signedTransaction).toEqual({\n      cmd: 'test-cmd',\n      sigs: [{ sig: 'test-sig' }],\n    });\n  });\n\n  it('throws when there is no signing response', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    const signWithWalletConnect = createSignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    const tx = structuredClone(transaction);\n    // @ts-ignore\n    delete tx.payload.exec;\n\n    await expect(() =>\n      signWithWalletConnect(createTransaction(tx)),\n    ).rejects.toThrowError('`cont` transactions are not supported');\n  });\n\n  it('adds an empty clist when signer.clist is undefined', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          body: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    const signWithWalletConnect = createSignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    const tx = structuredClone(transaction);\n    delete tx.signers[0].clist;\n\n    await signWithWalletConnect(createTransaction(tx));\n\n    expect(client.request).toHaveBeenCalledWith({\n      topic: session.topic,\n      chainId: walletConnectChainId,\n      request: {\n        id: 1,\n        jsonrpc: '2.0',\n        method: 'kadena_sign_v1',\n        params: {\n          code: transaction.payload.exec.code,\n          data: transaction.payload.exec.data,\n          caps: [],\n          nonce: transaction.nonce,\n          chainId: transaction.meta.chainId,\n          gasLimit: transaction.meta.gasLimit,\n          gasPrice: transaction.meta.gasPrice,\n          sender: transaction.meta.sender,\n          ttl: transaction.meta.ttl,\n        },\n      },\n    });\n  });\n\n  it('throws when signing cont command', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    const signWithWalletConnect = createSignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    await expect(() =>\n      signWithWalletConnect(createTransaction(transaction)),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n});\n"]}