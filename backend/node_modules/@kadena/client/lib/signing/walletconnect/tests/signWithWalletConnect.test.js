"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createTransaction_1 = require("../../../utils/createTransaction");
const signWithWalletConnect_1 = require("../signWithWalletConnect");
(0, vitest_1.describe)('signWithWalletConnect', () => {
    const transaction = Object.freeze({
        payload: {
            exec: {
                code: '(coin.transfer "bonnie" "clyde" 1)',
                data: {
                    test: 'test-data',
                },
            },
        },
        meta: {
            chainId: '0',
            gasLimit: 2300,
            gasPrice: 0.00000001,
            sender: 'test-sender',
            ttl: 3600,
            creationTime: 123456789,
        },
        signers: [
            {
                pubKey: '',
                clist: [
                    {
                        name: 'cap.test-cap-name',
                        args: ['test-cap-arg'],
                    },
                ],
            },
        ],
        networkId: 'test-network-id',
        nonce: 'kjs-test',
    });
    const session = { topic: 'test-topic' };
    const walletConnectChainId = 'kadena:testnet04';
    (0, vitest_1.it)('signs a transaction', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                body: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },
                catch: vitest_1.vi.fn(),
            })),
        };
        const signWithWalletConnect = (0, signWithWalletConnect_1.createSignWithWalletConnect)(client, session, walletConnectChainId);
        const signedTransaction = await signWithWalletConnect((0, createTransaction_1.createTransaction)(transaction));
        (0, vitest_1.expect)(client.request).toHaveBeenCalledWith({
            topic: session.topic,
            chainId: walletConnectChainId,
            request: {
                id: 1,
                jsonrpc: '2.0',
                method: 'kadena_sign_v1',
                params: {
                    code: transaction.payload.exec.code,
                    data: transaction.payload.exec.data,
                    caps: [
                        {
                            role: 'test-cap-name',
                            description: 'Description for cap.test-cap-name',
                            cap: {
                                name: 'cap.test-cap-name',
                                args: ['test-cap-arg'],
                            },
                        },
                    ],
                    nonce: transaction.nonce,
                    chainId: transaction.meta.chainId,
                    gasLimit: transaction.meta.gasLimit,
                    gasPrice: transaction.meta.gasPrice,
                    sender: transaction.meta.sender,
                    ttl: transaction.meta.ttl,
                },
            },
        });
        (0, vitest_1.expect)(signedTransaction.cmd).toBe('test-cmd');
        (0, vitest_1.expect)(signedTransaction).toEqual({
            cmd: 'test-cmd',
            sigs: [{ sig: 'test-sig' }],
        });
    });
    (0, vitest_1.it)('throws when there is no signing response', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                catch: vitest_1.vi.fn(),
            })),
        };
        const signWithWalletConnect = (0, signWithWalletConnect_1.createSignWithWalletConnect)(client, session, walletConnectChainId);
        const tx = structuredClone(transaction);
        // @ts-ignore
        delete tx.payload.exec;
        await (0, vitest_1.expect)(() => signWithWalletConnect((0, createTransaction_1.createTransaction)(tx))).rejects.toThrowError('`cont` transactions are not supported');
    });
    (0, vitest_1.it)('adds an empty clist when signer.clist is undefined', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                body: { cmd: 'test-cmd', sigs: [{ sig: 'test-sig' }] },
                catch: vitest_1.vi.fn(),
            })),
        };
        const signWithWalletConnect = (0, signWithWalletConnect_1.createSignWithWalletConnect)(client, session, walletConnectChainId);
        const tx = structuredClone(transaction);
        delete tx.signers[0].clist;
        await signWithWalletConnect((0, createTransaction_1.createTransaction)(tx));
        (0, vitest_1.expect)(client.request).toHaveBeenCalledWith({
            topic: session.topic,
            chainId: walletConnectChainId,
            request: {
                id: 1,
                jsonrpc: '2.0',
                method: 'kadena_sign_v1',
                params: {
                    code: transaction.payload.exec.code,
                    data: transaction.payload.exec.data,
                    caps: [],
                    nonce: transaction.nonce,
                    chainId: transaction.meta.chainId,
                    gasLimit: transaction.meta.gasLimit,
                    gasPrice: transaction.meta.gasPrice,
                    sender: transaction.meta.sender,
                    ttl: transaction.meta.ttl,
                },
            },
        });
    });
    (0, vitest_1.it)('throws when signing cont command', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                catch: vitest_1.vi.fn(),
            })),
        };
        const signWithWalletConnect = (0, signWithWalletConnect_1.createSignWithWalletConnect)(client, session, walletConnectChainId);
        await (0, vitest_1.expect)(() => signWithWalletConnect((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Error signing transaction');
    });
});
//# sourceMappingURL=signWithWalletConnect.test.js.map