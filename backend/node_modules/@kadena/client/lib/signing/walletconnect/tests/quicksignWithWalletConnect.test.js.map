{"version": 3, "file": "quicksignWithWalletConnect.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/walletconnect/tests/quicksignWithWalletConnect.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8D;AAE9D,wEAAqE;AAErE,8EAAiF;AAMjF,WAAE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAEtD,IAAA,iBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,WAAyB,CAAC;IAC9B,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,YAAY,EAAoC,CAAC;IAC1E,MAAM,oBAAoB,GAA0B,kBAAkB,CAAC;IACvE,IAAI,0BAAyC,CAAC;IAE9C,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,WAAW,GAAG;YACZ,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE;iBACnC;aACF;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,aAAa;gBACrB,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC,mCAAmC;gBAC/C,YAAY,EAAE,IAAI;aACnB;YACD,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,eAAe;4BACrB,IAAI,EAAE,CAAC,cAAc,CAAC;yBACvB;qBACF;oBACD,MAAM,EAAE,cAAc;iBACvB;aACF;YACD,SAAS,EAAE,YAAY;YACvB,KAAK,EAAE,EAAE;SACV,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACtD,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,MAAM,0BAA0B,GAC9B,IAAA,6DAAgC,EAC9B,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEJ,sDAAsD;QACtD,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAAC,0BAA0B,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CACnE,2BAA2B,CAC5B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACnC,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,SAAS,EAAE;oBACT;wBACE,OAAO,EAAE;4BACP,MAAM,EAAE,SAAS;4BACjB,IAAI,EAAE,WAAW;yBAClB;wBACD,cAAc,EAAE;4BACd,GAAG,EAAE,UAAU;4BACf,IAAI,EAAE;gCACJ;oCACE,IAAI,EAAE;wCACJ;4CACE,IAAI,EAAE,CAAC,cAAc,CAAC;4CACtB,IAAI,EAAE,eAAe;yCACtB;qCACF;oCACD,MAAM,EAAE,cAAc;oCACtB,GAAG,EAAE,UAAU;iCAChB;6BACF;yBACF;qBACF;iBACF;gBACD,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,0BAA0B,GAAG,IAAA,6DAAgC,EAC3D,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,mBAAmB,GAAG,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC;QAC3D,mBAAmB,CAAC,IAAI,GAAG,WAAW,CAAC;QACvC,MAAM,MAAM,GAAG,MAAM,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;QAErE,IAAA,eAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;YAC1C,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,oBAAoB;YAC7B,OAAO,EAAE;gBACP,EAAE,EAAE,CAAC;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,qBAAqB;gBAC7B,MAAM,EAAE;oBACN,eAAe,EAAE;wBACf;4BACE,GAAG,EAAE,+VAA+V;4BACpW,IAAI,EAAE;gCACJ;oCACE,MAAM,EAAE,cAAc;oCACtB,GAAG,EAAE,IAAI;iCACV;6BACF;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,GAAG,EAAE,+VAA+V;YACpW,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;SACpD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;SACxC,CAAC;QAEF,0BAA0B,GAAG,IAAA,6DAAgC,EAC3D,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,0BAA0B,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CAC3D,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QAClD,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,0BAA0B,GAAG,IAAA,6DAAgC,EAC3D,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,0BAA0B,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CAC3D,CAAC,OAAO,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;QACxF,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,WAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAClB,OAAO,CAAC,OAAO,CAAC;gBACd,SAAS,EAAE;oBACT;wBACE,OAAO,EAAE;4BACP,MAAM,EAAE,SAAS;4BACjB,IAAI,EAAE,qBAAqB;yBAC5B;wBACD,cAAc,EAAE;4BACd,GAAG,EAAE,UAAU;4BACf,IAAI,EAAE;gCACJ;oCACE,IAAI,EAAE;wCACJ;4CACE,IAAI,EAAE,CAAC,cAAc,CAAC;4CACtB,IAAI,EAAE,eAAe;yCACtB;qCACF;oCACD,MAAM,EAAE,cAAc;oCACtB,GAAG,EAAE,UAAU;iCAChB;6BACF;yBACF;qBACF;iBACF;gBACD,KAAK,EAAE,WAAE,CAAC,EAAE,EAAE;aACf,CAAC,CACH;SACF,CAAC;QAEF,0BAA0B,GAAG,IAAA,6DAAgC,EAC3D,MAA2B,EAC3B,OAAO,EACP,oBAAoB,CACrB,CAAC;QAEF,MAAM,IAAA,eAAM,EAAC,GAAG,EAAE,CAChB,0BAA0B,CAAC,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC,CAC3D,CAAC,OAAO,CAAC,YAAY,CACpB,uEAAuE,CACxE,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { beforeEach, describe, expect, it, vi } from 'vitest';\nimport type { IPactCommand } from '../../../interfaces/IPactCommand';\nimport { createTransaction } from '../../../utils/createTransaction';\nimport type { ISignFunction } from '../../ISignFunction';\nimport { createQuicksignWithWalletConnect } from '../quicksignWithWalletConnect';\nimport type { TWalletConnectChainId } from '../walletConnectTypes';\n\nimport type Client from '@walletconnect/sign-client';\nimport type { SessionTypes } from '@walletconnect/types';\n\nvi.spyOn(console, 'log').mockImplementation(() => {});\n\ndescribe('quicksignWithWalletConnect', () => {\n  let transaction: IPactCommand;\n  const session = { topic: 'test-topic' } as unknown as SessionTypes.Struct;\n  const walletConnectChainId: TWalletConnectChainId = 'kadena:testnet04';\n  let quicksignWithWalletConnect: ISignFunction;\n\n  beforeEach(() => {\n    transaction = {\n      payload: {\n        exec: {\n          code: '(coin.transfer \"bonnie\" \"clyde\" 1)',\n          data: { 'test-data': 'test-data' },\n        },\n      },\n      meta: {\n        chainId: '1',\n        gasLimit: 10000,\n        gasPrice: 1e-8,\n        sender: 'test-sender',\n        ttl: 30 * 6 /* time for 6 blocks to be mined */,\n        creationTime: 1234,\n      },\n      signers: [\n        {\n          clist: [\n            {\n              name: 'test-cap-name',\n              args: ['test-cap-arg'],\n            },\n          ],\n          pubKey: 'test-pub-key',\n        },\n      ],\n      networkId: 'testnet-id',\n      nonce: '',\n    };\n  });\n\n  it('throws when no transactions are passed', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    const quicksignWithWalletConnect: ISignFunction =\n      createQuicksignWithWalletConnect(\n        client as unknown as Client,\n        session,\n        walletConnectChainId,\n      );\n\n    // @ts-expect-error - Expected 1 arguments, but got 0.\n    await expect(() => quicksignWithWalletConnect()).rejects.toThrowError(\n      'No transaction(s) to sign',\n    );\n  });\n\n  it('signs a transaction', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          responses: [\n            {\n              outcome: {\n                result: 'success',\n                hash: 'test-hash',\n              },\n              commandSigData: {\n                cmd: 'test-cmd',\n                sigs: [\n                  {\n                    caps: [\n                      {\n                        args: ['test-cap-arg'],\n                        name: 'test-cap-name',\n                      },\n                    ],\n                    pubKey: 'test-pub-key',\n                    sig: 'test-sig',\n                  },\n                ],\n              },\n            },\n          ],\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    quicksignWithWalletConnect = createQuicksignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    const unsignedTransaction = createTransaction(transaction);\n    unsignedTransaction.hash = 'test-hash';\n    const result = await quicksignWithWalletConnect(unsignedTransaction);\n\n    expect(client.request).toHaveBeenCalledWith({\n      topic: session.topic,\n      chainId: walletConnectChainId,\n      request: {\n        id: 1,\n        jsonrpc: '2.0',\n        method: 'kadena_quicksign_v1',\n        params: {\n          commandSigDatas: [\n            {\n              cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n              sigs: [\n                {\n                  pubKey: 'test-pub-key',\n                  sig: null,\n                },\n              ],\n            },\n          ],\n        },\n      },\n    });\n\n    expect(result).toEqual({\n      cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bonnie\\\\\" \\\\\"clyde\\\\\" 1)\",\"data\":{\"test-data\":\"test-data\"}}},\"meta\":{\"chainId\":\"1\",\"gasLimit\":10000,\"gasPrice\":1e-8,\"sender\":\"test-sender\",\"ttl\":180,\"creationTime\":1234},\"signers\":[{\"clist\":[{\"name\":\"test-cap-name\",\"args\":[\"test-cap-arg\"]}],\"pubKey\":\"test-pub-key\"}],\"networkId\":\"testnet-id\",\"nonce\":\"\"}',\n      hash: 'test-hash',\n      sigs: [{ sig: 'test-sig', pubKey: 'test-pub-key' }],\n    });\n  });\n\n  it('throws when there is no signing response', async () => {\n    const client = {\n      request: vi.fn(() => Promise.resolve()),\n    };\n\n    quicksignWithWalletConnect = createQuicksignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    await expect(() =>\n      quicksignWithWalletConnect(createTransaction(transaction)),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n\n  it('throws when there are no responses', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    quicksignWithWalletConnect = createQuicksignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    await expect(() =>\n      quicksignWithWalletConnect(createTransaction(transaction)),\n    ).rejects.toThrowError('Error signing transaction');\n  });\n\n  it('throws when the hash of the unsigned and signed transaction do not match', async () => {\n    const client = {\n      request: vi.fn(() =>\n        Promise.resolve({\n          responses: [\n            {\n              outcome: {\n                result: 'success',\n                hash: 'test-hash-different',\n              },\n              commandSigData: {\n                cmd: 'test-cmd',\n                sigs: [\n                  {\n                    caps: [\n                      {\n                        args: ['test-cap-arg'],\n                        name: 'test-cap-name',\n                      },\n                    ],\n                    pubKey: 'test-pub-key',\n                    sig: 'test-sig',\n                  },\n                ],\n              },\n            },\n          ],\n          catch: vi.fn(),\n        }),\n      ),\n    };\n\n    quicksignWithWalletConnect = createQuicksignWithWalletConnect(\n      client as unknown as Client,\n      session,\n      walletConnectChainId,\n    );\n\n    await expect(() =>\n      quicksignWithWalletConnect(createTransaction(transaction)),\n    ).rejects.toThrowError(\n      'Hash of the transaction signed by the wallet does not match. Our hash',\n    );\n  });\n});\n"]}