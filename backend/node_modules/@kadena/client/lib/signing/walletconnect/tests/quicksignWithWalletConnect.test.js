"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const createTransaction_1 = require("../../../utils/createTransaction");
const quicksignWithWalletConnect_1 = require("../quicksignWithWalletConnect");
vitest_1.vi.spyOn(console, 'log').mockImplementation(() => { });
(0, vitest_1.describe)('quicksignWithWalletConnect', () => {
    let transaction;
    const session = { topic: 'test-topic' };
    const walletConnectChainId = 'kadena:testnet04';
    let quicksignWithWalletConnect;
    (0, vitest_1.beforeEach)(() => {
        transaction = {
            payload: {
                exec: {
                    code: '(coin.transfer "bonnie" "clyde" 1)',
                    data: { 'test-data': 'test-data' },
                },
            },
            meta: {
                chainId: '1',
                gasLimit: 10000,
                gasPrice: 1e-8,
                sender: 'test-sender',
                ttl: 30 * 6 /* time for 6 blocks to be mined */,
                creationTime: 1234,
            },
            signers: [
                {
                    clist: [
                        {
                            name: 'test-cap-name',
                            args: ['test-cap-arg'],
                        },
                    ],
                    pubKey: 'test-pub-key',
                },
            ],
            networkId: 'testnet-id',
            nonce: '',
        };
    });
    (0, vitest_1.it)('throws when no transactions are passed', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                catch: vitest_1.vi.fn(),
            })),
        };
        const quicksignWithWalletConnect = (0, quicksignWithWalletConnect_1.createQuicksignWithWalletConnect)(client, session, walletConnectChainId);
        // @ts-expect-error - Expected 1 arguments, but got 0.
        await (0, vitest_1.expect)(() => quicksignWithWalletConnect()).rejects.toThrowError('No transaction(s) to sign');
    });
    (0, vitest_1.it)('signs a transaction', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                responses: [
                    {
                        outcome: {
                            result: 'success',
                            hash: 'test-hash',
                        },
                        commandSigData: {
                            cmd: 'test-cmd',
                            sigs: [
                                {
                                    caps: [
                                        {
                                            args: ['test-cap-arg'],
                                            name: 'test-cap-name',
                                        },
                                    ],
                                    pubKey: 'test-pub-key',
                                    sig: 'test-sig',
                                },
                            ],
                        },
                    },
                ],
                catch: vitest_1.vi.fn(),
            })),
        };
        quicksignWithWalletConnect = (0, quicksignWithWalletConnect_1.createQuicksignWithWalletConnect)(client, session, walletConnectChainId);
        const unsignedTransaction = (0, createTransaction_1.createTransaction)(transaction);
        unsignedTransaction.hash = 'test-hash';
        const result = await quicksignWithWalletConnect(unsignedTransaction);
        (0, vitest_1.expect)(client.request).toHaveBeenCalledWith({
            topic: session.topic,
            chainId: walletConnectChainId,
            request: {
                id: 1,
                jsonrpc: '2.0',
                method: 'kadena_quicksign_v1',
                params: {
                    commandSigDatas: [
                        {
                            cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key"}],"networkId":"testnet-id","nonce":""}',
                            sigs: [
                                {
                                    pubKey: 'test-pub-key',
                                    sig: null,
                                },
                            ],
                        },
                    ],
                },
            },
        });
        (0, vitest_1.expect)(result).toEqual({
            cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bonnie\\" \\"clyde\\" 1)","data":{"test-data":"test-data"}}},"meta":{"chainId":"1","gasLimit":10000,"gasPrice":1e-8,"sender":"test-sender","ttl":180,"creationTime":1234},"signers":[{"clist":[{"name":"test-cap-name","args":["test-cap-arg"]}],"pubKey":"test-pub-key"}],"networkId":"testnet-id","nonce":""}',
            hash: 'test-hash',
            sigs: [{ sig: 'test-sig', pubKey: 'test-pub-key' }],
        });
    });
    (0, vitest_1.it)('throws when there is no signing response', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve()),
        };
        quicksignWithWalletConnect = (0, quicksignWithWalletConnect_1.createQuicksignWithWalletConnect)(client, session, walletConnectChainId);
        await (0, vitest_1.expect)(() => quicksignWithWalletConnect((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Error signing transaction');
    });
    (0, vitest_1.it)('throws when there are no responses', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                catch: vitest_1.vi.fn(),
            })),
        };
        quicksignWithWalletConnect = (0, quicksignWithWalletConnect_1.createQuicksignWithWalletConnect)(client, session, walletConnectChainId);
        await (0, vitest_1.expect)(() => quicksignWithWalletConnect((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Error signing transaction');
    });
    (0, vitest_1.it)('throws when the hash of the unsigned and signed transaction do not match', async () => {
        const client = {
            request: vitest_1.vi.fn(() => Promise.resolve({
                responses: [
                    {
                        outcome: {
                            result: 'success',
                            hash: 'test-hash-different',
                        },
                        commandSigData: {
                            cmd: 'test-cmd',
                            sigs: [
                                {
                                    caps: [
                                        {
                                            args: ['test-cap-arg'],
                                            name: 'test-cap-name',
                                        },
                                    ],
                                    pubKey: 'test-pub-key',
                                    sig: 'test-sig',
                                },
                            ],
                        },
                    },
                ],
                catch: vitest_1.vi.fn(),
            })),
        };
        quicksignWithWalletConnect = (0, quicksignWithWalletConnect_1.createQuicksignWithWalletConnect)(client, session, walletConnectChainId);
        await (0, vitest_1.expect)(() => quicksignWithWalletConnect((0, createTransaction_1.createTransaction)(transaction))).rejects.toThrowError('Hash of the transaction signed by the wallet does not match. Our hash');
    });
});
//# sourceMappingURL=quicksignWithWalletConnect.test.js.map