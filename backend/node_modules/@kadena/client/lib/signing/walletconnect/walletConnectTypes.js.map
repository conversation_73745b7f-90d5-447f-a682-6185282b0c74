{"version": 3, "file": "walletConnectTypes.js", "sourceRoot": "", "sources": ["../../../src/signing/walletconnect/walletConnectTypes.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ChainwebChainId } from '@kadena/chainweb-node-client';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\n\n/**\n * @internal\n */\nexport interface IWalletConnectAccount {\n  account: string;\n  contracts?: string[];\n  kadenaAccounts: [\n    {\n      name: string;\n      chains: ChainwebChainId[];\n      contract: string;\n    },\n  ];\n}\n\n/**\n * The Blockchain that's used in a WalletConnect context\n *\n * @remarks\n * For kadena it is `kadena:<networkId>`\n *\n * @see Reference {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md#pairing-with-walletconnect | KIP-0017 WalletConnect Specification }\n * @public\n */\nexport type TWalletConnectChainId = `kadena:${IPactCommand['networkId']}`; //kadena:mainnet01, kadena:testnet04, kadena:development\n"]}