"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWalletConnectQuicksign = exports.createQuicksignWithWalletConnect = void 0;
const addSignatures_1 = require("../utils/addSignatures");
const parseTransactionCommand_1 = require("../utils/parseTransactionCommand");
/**
 * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 * @public
 */
function createQuicksignWithWalletConnect(client, session, walletConnectChainId) {
    const quicksignWithWalletConnect = (async (transactionList) => {
        if (transactionList === undefined) {
            throw new Error('No transaction(s) to sign');
        }
        const isList = Array.isArray(transactionList);
        const transactions = isList ? transactionList : [transactionList];
        const transactionHashes = [];
        const commandSigDatas = transactions.map((pactCommand) => {
            const { cmd, hash } = pactCommand;
            const { signers } = (0, parseTransactionCommand_1.parseTransactionCommand)(pactCommand);
            transactionHashes.push(hash);
            return {
                cmd,
                sigs: signers.map((signer, i) => {
                    var _a, _b;
                    return ({
                        pubKey: signer.pubKey,
                        sig: (_b = (_a = pactCommand.sigs[i]) === null || _a === void 0 ? void 0 : _a.sig) !== null && _b !== void 0 ? _b : null,
                    });
                }),
            };
        });
        const quickSignRequest = {
            commandSigDatas,
        };
        const transactionRequest = {
            id: 1,
            jsonrpc: '2.0',
            method: 'kadena_quicksign_v1',
            params: quickSignRequest,
        };
        const response = await client.request({
            topic: session.topic,
            chainId: walletConnectChainId,
            request: transactionRequest,
        });
        if (response === undefined) {
            throw new Error('Error signing transaction');
        }
        if ('responses' in response) {
            response.responses.map((signedCommand, i) => {
                if (signedCommand.outcome.result === 'success') {
                    if (signedCommand.outcome.hash !== transactionHashes[i]) {
                        throw new Error(`Hash of the transaction signed by the wallet does not match. Our hash: ${transactionHashes[i]}, wallet hash: ${signedCommand.outcome.hash}`);
                    }
                    const sigs = signedCommand.commandSigData.sigs.filter((sig) => sig.sig !== null);
                    // Add the signature(s) that we received from the wallet to the PactCommand(s)
                    transactions[i] = (0, addSignatures_1.addSignatures)(transactions[i], ...sigs);
                }
            });
        }
        else {
            throw new Error('Error signing transaction');
        }
        return isList ? transactions : transactions[0];
    });
    return quicksignWithWalletConnect;
}
exports.createQuicksignWithWalletConnect = createQuicksignWithWalletConnect;
/**
 * Creates the quicksignWithWalletConnect function with interface {@link ISingleSignFunction}
 *
 *
 * @deprecated Use {@link createQuicksignWithWalletConnect} instead
 * @public
 */
exports.createWalletConnectQuicksign = createQuicksignWithWalletConnect;
//# sourceMappingURL=quicksignWithWalletConnect.js.map