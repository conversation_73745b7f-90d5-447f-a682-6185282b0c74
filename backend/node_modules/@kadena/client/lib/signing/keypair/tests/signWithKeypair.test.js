"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const index_1 = require("../../../index");
const pact_1 = require("../../../pact");
const createSignWithKeypair_1 = require("../createSignWithKeypair");
const keyPair = {
    publicKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',
    secretKey: '76e1cabaa58a33321982e434f355dc7a4cfbee092a4ac1c7aac26302ba80d992',
};
const coin = (0, pact_1.getModule)('coin');
(0, vitest_1.describe)('createSignWithKeypair', () => {
    let signWithKeypair;
    (0, vitest_1.beforeAll)(() => {
        signWithKeypair = (0, createSignWithKeypair_1.createSignWithKeypair)(keyPair);
    });
    (0, vitest_1.it)('returns a function to sign with', async () => {
        (0, vitest_1.expect)(typeof signWithKeypair).toBe('function');
    });
});
(0, vitest_1.describe)('signWithKeypair', () => {
    let signWithKeypair;
    (0, vitest_1.beforeAll)(() => {
        signWithKeypair = (0, createSignWithKeypair_1.createSignWithKeypair)(keyPair);
    });
    (0, vitest_1.it)('throws an error when nothing is to be signed', async () => {
        try {
            await signWithKeypair();
        }
        catch (e) {
            (0, vitest_1.expect)(e.message).toContain('No transaction(s) to sign');
        }
    });
    (0, vitest_1.it)('throws when an error is returned', async () => {
        try {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            await createSignWithKeypair_1.createSignWithKeypair(undefined);
        }
        catch (e) {
            (0, vitest_1.expect)(e).toBeTruthy();
        }
    });
    (0, vitest_1.it)('adds signatures in multisig fashion to the transactions', async () => {
        const keyPair2 = {
            publicKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            secretKey: '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',
        };
        const signWithKeypair2 = (0, createSignWithKeypair_1.createSignWithKeypair)(keyPair2);
        const unsignedTransaction = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])
            .addSigner(keyPair2.publicKey, (withCap) => [
            withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),
        ])
            .setMeta({
            senderAccount: '',
            chainId: '0',
            creationTime: 0,
        })
            .setNonce('my-nonce')
            .createTransaction();
        const txWithOneSig = await signWithKeypair(unsignedTransaction);
        (0, vitest_1.expect)(txWithOneSig.sigs).toStrictEqual([
            {
                pubKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',
                sig: '5bdcceab829c628afc2afe28d18e5efdc724f0ebc18e1aa10b03c07123f23bdd698a197aba39fc035c0a71c5c821f2f8e12fd6fb138dc33e6d2323b3bbd1b40e',
            },
            {
                pubKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            },
        ]);
        const signedTx = await signWithKeypair2(txWithOneSig);
        (0, vitest_1.expect)(signedTx.sigs).toEqual([
            {
                pubKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',
                sig: '5bdcceab829c628afc2afe28d18e5efdc724f0ebc18e1aa10b03c07123f23bdd698a197aba39fc035c0a71c5c821f2f8e12fd6fb138dc33e6d2323b3bbd1b40e',
            },
            {
                pubKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
                sig: 'b921a16a15e2ffa36bda28fb1dcb98ef75e83d94ac2f2736013981d73759ead51945b1df21de254429772cdd6bfe8ea3c63001dfd06a2e0b0322abd3de2c7600',
            },
        ]);
    });
    (0, vitest_1.it)('tries to sign with the wrong keypair', async () => {
        const keyPair2 = {
            publicKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            secretKey: '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',
        };
        const signWithKeypair2 = (0, createSignWithKeypair_1.createSignWithKeypair)(keyPair2);
        const unsignedTransaction = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])
            .createTransaction();
        try {
            await signWithKeypair2(unsignedTransaction);
        }
        catch (error) {
            (0, vitest_1.expect)(error.message).toContain('The keypair(s) provided are not relevant to the transaction');
            return;
        }
        // should not end up here
        (0, vitest_1.expect)(true).toBe(false);
    });
    (0, vitest_1.it)('signs with two keypairs when one is required', async () => {
        const keyPair2 = {
            publicKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            secretKey: '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',
        };
        const unsignedTransaction = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])
            .setMeta({
            senderAccount: '',
            chainId: '0',
            creationTime: 0,
        })
            .setNonce('my-nonce')
            .createTransaction();
        const signWithKeystore = (0, createSignWithKeypair_1.createSignWithKeypair)([keyPair, keyPair2]);
        const signedTx = await signWithKeystore(unsignedTransaction);
        (0, vitest_1.expect)(signedTx.sigs).toEqual([
            {
                pubKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',
                sig: 'fbc3819d977cf8f770c5fc6b5a75a3099969a7945cec6a5d2bd1e7dd0130a68935d52f1cfce62046db885370511904e00a9a9d2fee84f653f5d324dce9a82a00',
            },
        ]);
    });
    (0, vitest_1.it)('signs with two keypairs', async () => {
        const keyPair2 = {
            publicKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            secretKey: '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',
        };
        const unsignedTransaction = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])
            .addSigner(keyPair2.publicKey, (withCap) => [
            withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),
        ])
            .setMeta({
            senderAccount: '',
            chainId: '0',
            creationTime: 0,
        })
            .setNonce('my-nonce')
            .createTransaction();
        const signWithKeystore = (0, createSignWithKeypair_1.createSignWithKeypair)([keyPair, keyPair2]);
        const signedTx = await signWithKeystore(unsignedTransaction);
        (0, vitest_1.expect)(signedTx.sigs).toEqual([
            {
                pubKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',
                sig: '5bdcceab829c628afc2afe28d18e5efdc724f0ebc18e1aa10b03c07123f23bdd698a197aba39fc035c0a71c5c821f2f8e12fd6fb138dc33e6d2323b3bbd1b40e',
            },
            {
                pubKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
                sig: 'b921a16a15e2ffa36bda28fb1dcb98ef75e83d94ac2f2736013981d73759ead51945b1df21de254429772cdd6bfe8ea3c63001dfd06a2e0b0322abd3de2c7600',
            },
        ]);
    });
    (0, vitest_1.it)('signs multiple transactions with multiple keys passed to `createSignWithKeypair`', async () => {
        const keyPair2 = {
            publicKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            secretKey: '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',
        };
        const tx1 = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .setMeta({
            senderAccount: '',
            chainId: '0',
            creationTime: 0,
        })
            .setNonce('my-nonce')
            .addSigner(keyPair2.publicKey, (withCap) => [
            withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),
        ])
            .createTransaction();
        const tx2 = index_1.Pact.builder
            .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))
            .setMeta({
            senderAccount: '',
            chainId: '0',
            creationTime: 0,
        })
            .setNonce('my-nonce')
            .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])
            .createTransaction();
        const signWithKeystore = (0, createSignWithKeypair_1.createSignWithKeypair)([keyPair, keyPair2]);
        const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);
        (0, vitest_1.expect)(signedTx1.sigs).toEqual([
            {
                sig: '522264b48dfefacd06ac6edc4b72e37629f946dbb464396b069b035d7c11e0b4852ea0c9ef4da8aeafb964ed348c1d0a416d9736af87e8d936bbb2c1bc7d5c0e',
                pubKey: '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',
            },
        ]);
        (0, vitest_1.expect)(signedTx2.sigs).toEqual([
            {
                sig: 'fbc3819d977cf8f770c5fc6b5a75a3099969a7945cec6a5d2bd1e7dd0130a68935d52f1cfce62046db885370511904e00a9a9d2fee84f653f5d324dce9a82a00',
                pubKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',
            },
        ]);
    });
});
//# sourceMappingURL=signWithKeypair.test.js.map