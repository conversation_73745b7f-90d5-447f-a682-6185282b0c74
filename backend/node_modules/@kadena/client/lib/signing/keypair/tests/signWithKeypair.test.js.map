{"version": 3, "file": "signWithKeypair.test.js", "sourceRoot": "", "sources": ["../../../../src/signing/keypair/tests/signWithKeypair.test.ts"], "names": [], "mappings": ";;AACA,mCAAyD;AAGzD,0CAAsC;AACtC,wCAA0C;AAC1C,oEAAiE;AAEjE,MAAM,OAAO,GAAa;IACxB,SAAS,EAAE,kEAAkE;IAC7E,SAAS,EAAE,kEAAkE;CAC9E,CAAC;AAEF,MAAM,IAAI,GAAU,IAAA,gBAAS,EAAC,MAAM,CAAC,CAAC;AAEtC,IAAA,iBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,eAA8B,CAAC;IACnC,IAAA,kBAAS,EAAC,GAAG,EAAE;QACb,eAAe,GAAG,IAAA,6CAAqB,EAAC,OAAO,CAAkB,CAAC;IACpE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,IAAA,eAAM,EAAC,OAAO,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,eAA8B,CAAC;IACnC,IAAA,kBAAS,EAAC,GAAG,EAAE;QACb,eAAe,GAAG,IAAA,6CAAqB,EAAC,OAAO,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;QAC5D,IAAI,CAAC;YACH,MAAO,eAAuC,EAAE,CAAC;QACnD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAA,eAAM,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAChD,IAAI,CAAC;YACH,8DAA8D;YAC9D,MAAO,6CAA6B,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAA,eAAM,EAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;QACvE,MAAM,QAAQ,GAAa;YACzB,SAAS,EACP,kEAAkE;YACpE,SAAS,EACP,kEAAkE;SACrE,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAA,6CAAqB,EAAC,QAAQ,CAAC,CAAC;QAEzD,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;aACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;SACjE,CAAC;aACD,OAAO,CAAC;YACP,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,GAAG;YACZ,YAAY,EAAE,CAAC;SAChB,CAAC;aACD,QAAQ,CAAC,UAAU,CAAC;aACpB,iBAAiB,EAAE,CAAC;QAEvB,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAEhE,IAAA,eAAM,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;YACtC;gBACE,MAAM,EACJ,kEAAkE;gBACpE,GAAG,EAAE,kIAAkI;aACxI;YACD;gBACE,MAAM,EACJ,kEAAkE;aACrE;SACF,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACtD,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAC5B;gBACE,MAAM,EACJ,kEAAkE;gBACpE,GAAG,EAAE,kIAAkI;aACxI;YACD;gBACE,MAAM,EACJ,kEAAkE;gBACpE,GAAG,EAAE,kIAAkI;aACxI;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,QAAQ,GAAa;YACzB,SAAS,EACP,kEAAkE;YACpE,SAAS,EACP,kEAAkE;SACrE,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAA,6CAAqB,EAAC,QAAQ,CAAC,CAAC;QAEzD,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;aACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE,iBAAiB,EAAE,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,eAAM,EAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,CAC7B,6DAA6D,CAC9D,CAAC;YACF,OAAO;QACT,CAAC;QACD,yBAAyB;QACzB,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;QAC5D,MAAM,QAAQ,GAAa;YACzB,SAAS,EACP,kEAAkE;YACpE,SAAS,EACP,kEAAkE;SACrE,CAAC;QAEF,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;aACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE,OAAO,CAAC;YACP,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,GAAG;YACZ,YAAY,EAAE,CAAC;SAChB,CAAC;aACD,QAAQ,CAAC,UAAU,CAAC;aACpB,iBAAiB,EAAE,CAAC;QAEvB,MAAM,gBAAgB,GAAG,IAAA,6CAAqB,EAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QAE7D,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAC5B;gBACE,MAAM,EACJ,kEAAkE;gBACpE,GAAG,EAAE,kIAAkI;aACxI;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QACvC,MAAM,QAAQ,GAAa;YACzB,SAAS,EACP,kEAAkE;YACpE,SAAS,EACP,kEAAkE;SACrE,CAAC;QAEF,MAAM,mBAAmB,GAAG,YAAI,CAAC,OAAO;aACrC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;SACjE,CAAC;aACD,OAAO,CAAC;YACP,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,GAAG;YACZ,YAAY,EAAE,CAAC;SAChB,CAAC;aACD,QAAQ,CAAC,UAAU,CAAC;aACpB,iBAAiB,EAAE,CAAC;QAEvB,MAAM,gBAAgB,GAAG,IAAA,6CAAqB,EAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QAE7D,IAAA,eAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAC5B;gBACE,MAAM,EACJ,kEAAkE;gBACpE,GAAG,EAAE,kIAAkI;aACxI;YACD;gBACE,MAAM,EACJ,kEAAkE;gBACpE,GAAG,EAAE,kIAAkI;aACxI;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kFAAkF,EAAE,KAAK,IAAI,EAAE;QAChG,MAAM,QAAQ,GAAa;YACzB,SAAS,EACP,kEAAkE;YACpE,SAAS,EACP,kEAAkE;SACrE,CAAC;QAEF,MAAM,GAAG,GAAG,YAAI,CAAC,OAAO;aACrB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,OAAO,CAAC;YACP,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,GAAG;YACZ,YAAY,EAAE,CAAC;SAChB,CAAC;aACD,QAAQ,CAAC,UAAU,CAAC;aACpB,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;SACjE,CAAC;aACD,iBAAiB,EAAE,CAAC;QAEvB,MAAM,GAAG,GAAG,YAAI,CAAC,OAAO;aACrB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D,OAAO,CAAC;YACP,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,GAAG;YACZ,YAAY,EAAE,CAAC;SAChB,CAAC;aACD,QAAQ,CAAC,UAAU,CAAC;aACpB,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE,iBAAiB,EAAE,CAAC;QAEvB,MAAM,gBAAgB,GAAG,IAAA,6CAAqB,EAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpE,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,MAAM,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAElE,IAAA,eAAM,EAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAC7B;gBACE,GAAG,EAAE,kIAAkI;gBACvI,MAAM,EACJ,kEAAkE;aACrE;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAC7B;gBACE,GAAG,EAAE,kIAAkI;gBACvI,MAAM,EACJ,kEAAkE;aACrE;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import type { <PERSON><PERSON>ey<PERSON><PERSON> } from '@kadena/types';\nimport { beforeAll, describe, expect, it } from 'vitest';\nimport type { ICoin } from '../../../composePactCommand/test/coin-contract';\nimport type { ISignFunction } from '../../../index';\nimport { Pact } from '../../../index';\nimport { getModule } from '../../../pact';\nimport { createSignWithKeypair } from '../createSignWithKeypair';\n\nconst keyPair: IKeyPair = {\n  publicKey: '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',\n  secretKey: '76e1cabaa58a33321982e434f355dc7a4cfbee092a4ac1c7aac26302ba80d992',\n};\n\nconst coin: ICoin = getModule('coin');\n\ndescribe('createSignWithKeypair', () => {\n  let signWithKeypair: ISignFunction;\n  beforeAll(() => {\n    signWithKeypair = createSignWithKeypair(keyPair) as ISignFunction;\n  });\n\n  it('returns a function to sign with', async () => {\n    expect(typeof signWithKeypair).toBe('function');\n  });\n});\n\ndescribe('signWithKeypair', () => {\n  let signWithKeypair: ISignFunction;\n  beforeAll(() => {\n    signWithKeypair = createSignWithKeypair(keyPair);\n  });\n\n  it('throws an error when nothing is to be signed', async () => {\n    try {\n      await (signWithKeypair as unknown as () => {})();\n    } catch (e) {\n      expect(e.message).toContain('No transaction(s) to sign');\n    }\n  });\n\n  it('throws when an error is returned', async () => {\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      await (createSignWithKeypair as any)(undefined);\n    } catch (e) {\n      expect(e).toBeTruthy();\n    }\n  });\n\n  it('adds signatures in multisig fashion to the transactions', async () => {\n    const keyPair2: IKeyPair = {\n      publicKey:\n        '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      secretKey:\n        '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',\n    };\n    const signWithKeypair2 = createSignWithKeypair(keyPair2);\n\n    const unsignedTransaction = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])\n      .addSigner(keyPair2.publicKey, (withCap) => [\n        withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),\n      ])\n      .setMeta({\n        senderAccount: '',\n        chainId: '0',\n        creationTime: 0,\n      })\n      .setNonce('my-nonce')\n      .createTransaction();\n\n    const txWithOneSig = await signWithKeypair(unsignedTransaction);\n\n    expect(txWithOneSig.sigs).toStrictEqual([\n      {\n        pubKey:\n          '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',\n        sig: '5bdcceab829c628afc2afe28d18e5efdc724f0ebc18e1aa10b03c07123f23bdd698a197aba39fc035c0a71c5c821f2f8e12fd6fb138dc33e6d2323b3bbd1b40e',\n      },\n      {\n        pubKey:\n          '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      },\n    ]);\n\n    const signedTx = await signWithKeypair2(txWithOneSig);\n    expect(signedTx.sigs).toEqual([\n      {\n        pubKey:\n          '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',\n        sig: '5bdcceab829c628afc2afe28d18e5efdc724f0ebc18e1aa10b03c07123f23bdd698a197aba39fc035c0a71c5c821f2f8e12fd6fb138dc33e6d2323b3bbd1b40e',\n      },\n      {\n        pubKey:\n          '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n        sig: 'b921a16a15e2ffa36bda28fb1dcb98ef75e83d94ac2f2736013981d73759ead51945b1df21de254429772cdd6bfe8ea3c63001dfd06a2e0b0322abd3de2c7600',\n      },\n    ]);\n  });\n\n  it('tries to sign with the wrong keypair', async () => {\n    const keyPair2: IKeyPair = {\n      publicKey:\n        '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      secretKey:\n        '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',\n    };\n    const signWithKeypair2 = createSignWithKeypair(keyPair2);\n\n    const unsignedTransaction = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])\n      .createTransaction();\n\n    try {\n      await signWithKeypair2(unsignedTransaction);\n    } catch (error) {\n      expect(error.message).toContain(\n        'The keypair(s) provided are not relevant to the transaction',\n      );\n      return;\n    }\n    // should not end up here\n    expect(true).toBe(false);\n  });\n\n  it('signs with two keypairs when one is required', async () => {\n    const keyPair2: IKeyPair = {\n      publicKey:\n        '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      secretKey:\n        '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',\n    };\n\n    const unsignedTransaction = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])\n      .setMeta({\n        senderAccount: '',\n        chainId: '0',\n        creationTime: 0,\n      })\n      .setNonce('my-nonce')\n      .createTransaction();\n\n    const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);\n    const signedTx = await signWithKeystore(unsignedTransaction);\n\n    expect(signedTx.sigs).toEqual([\n      {\n        pubKey:\n          '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',\n        sig: 'fbc3819d977cf8f770c5fc6b5a75a3099969a7945cec6a5d2bd1e7dd0130a68935d52f1cfce62046db885370511904e00a9a9d2fee84f653f5d324dce9a82a00',\n      },\n    ]);\n  });\n\n  it('signs with two keypairs', async () => {\n    const keyPair2: IKeyPair = {\n      publicKey:\n        '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      secretKey:\n        '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',\n    };\n\n    const unsignedTransaction = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])\n      .addSigner(keyPair2.publicKey, (withCap) => [\n        withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),\n      ])\n      .setMeta({\n        senderAccount: '',\n        chainId: '0',\n        creationTime: 0,\n      })\n      .setNonce('my-nonce')\n      .createTransaction();\n\n    const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);\n    const signedTx = await signWithKeystore(unsignedTransaction);\n\n    expect(signedTx.sigs).toEqual([\n      {\n        pubKey:\n          '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',\n        sig: '5bdcceab829c628afc2afe28d18e5efdc724f0ebc18e1aa10b03c07123f23bdd698a197aba39fc035c0a71c5c821f2f8e12fd6fb138dc33e6d2323b3bbd1b40e',\n      },\n      {\n        pubKey:\n          '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n        sig: 'b921a16a15e2ffa36bda28fb1dcb98ef75e83d94ac2f2736013981d73759ead51945b1df21de254429772cdd6bfe8ea3c63001dfd06a2e0b0322abd3de2c7600',\n      },\n    ]);\n  });\n\n  it('signs multiple transactions with multiple keys passed to `createSignWithKeypair`', async () => {\n    const keyPair2: IKeyPair = {\n      publicKey:\n        '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      secretKey:\n        '63568bda80b8ff430a5816b0292b456362c8d426ddda08a249e0cb9c005d2502',\n    };\n\n    const tx1 = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .setMeta({\n        senderAccount: '',\n        chainId: '0',\n        creationTime: 0,\n      })\n      .setNonce('my-nonce')\n      .addSigner(keyPair2.publicKey, (withCap) => [\n        withCap('coin.TRANSFER', 'k:from', 'k:to', { decimal: '1.234' }),\n      ])\n      .createTransaction();\n\n    const tx2 = Pact.builder\n      .execution(coin.transfer('k:from', 'k:to', { decimal: '1.0' }))\n      .setMeta({\n        senderAccount: '',\n        chainId: '0',\n        creationTime: 0,\n      })\n      .setNonce('my-nonce')\n      .addSigner(keyPair.publicKey, (withCap) => [withCap('coin.GAS')])\n      .createTransaction();\n\n    const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);\n    const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);\n\n    expect(signedTx1.sigs).toEqual([\n      {\n        sig: '522264b48dfefacd06ac6edc4b72e37629f946dbb464396b069b035d7c11e0b4852ea0c9ef4da8aeafb964ed348c1d0a416d9736af87e8d936bbb2c1bc7d5c0e',\n        pubKey:\n          '815224b7316e0053635a91fea90f1f5bb474831b257be1aaaf2129ff989824d8',\n      },\n    ]);\n\n    expect(signedTx2.sigs).toEqual([\n      {\n        sig: 'fbc3819d977cf8f770c5fc6b5a75a3099969a7945cec6a5d2bd1e7dd0130a68935d52f1cfce62046db885370511904e00a9a9d2fee84f653f5d324dce9a82a00',\n        pubKey:\n          '09e82da78d531e2d16852a923e9fe0f80f3b67a9b8d92c7f05e4782222252e12',\n      },\n    ]);\n  });\n});\n"]}