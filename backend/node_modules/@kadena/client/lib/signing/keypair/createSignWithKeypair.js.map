{"version": 3, "file": "createSignWithKeypair.js", "sourceRoot": "", "sources": ["../../../src/signing/keypair/createSignWithKeypair.ts"], "names": [], "mappings": ";;;;;;AAAA,mEAAsD;AAGtD,kDAA2B;AAG3B,0DAAuD;AACvD,8EAA2E;AAE3E,MAAM,KAAK,GAAa,IAAA,eAAM,EAAC,wBAAwB,CAAC,CAAC;AAuCzD;;;;;;;;;;;;;;;GAeG;AACI,MAAM,qBAAqB,GAA2B,CAAC,SAAS,EAAE,EAAE;IACzE,MAAM,QAAQ,GAAe,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;QACnD,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAChB,OAAO,KAAK,UAAU,eAAe,CAAC,eAAe;QACnD,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAElE,MAAM,kBAAkB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;YACjD,KAAK,CAAC,2BAA2B,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,IAAA,iDAAuB,EAAC,EAAE,CAAC,CAAC;YACtD,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAE1E,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;YACJ,CAAC;YAED,OAAO,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAkB,CAAC;AACrB,CAAC,CAAC;AA5BW,QAAA,qBAAqB,yBA4BhC;AAEF,SAAS,mBAAmB,CAC1B,EAAgB,EAChB,QAAoB;IAEpB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CACnD,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,CAC9D,CAAC;IACF,KAAK,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;IAC7C,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,gBAAgB,CACvB,EAAoB,EACpB,gBAA4B;IAE5B,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE;QAC7C,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,IAAA,6BAAQ,EAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnD,KAAK,CAAC,0CAA0C,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACrE,OAAO,IAAA,6BAAa,EAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAI,EAAE,MAAM,EAAE,CAAC,CAAC;IAClD,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC", "sourcesContent": ["import { signHash } from '@kadena/cryptography-utils';\nimport type { IKeyPair, IUnsignedCommand } from '@kadena/types';\nimport type { Debugger } from 'debug';\nimport _debug from 'debug';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\nimport type { ISignFunction } from '../ISignFunction';\nimport { addSignatures } from '../utils/addSignatures';\nimport { parseTransactionCommand } from '../utils/parseTransactionCommand';\n\nconst debug: Debugger = _debug('pactjs:signWithKeypair');\n\n/**\n * interface for the `createSignWithKeypair` function {@link createSignWithKeypair}\n *\n * @public\n */\nexport interface ICreateSignWithKeypair {\n  /**\n   * @param key - provide the key to sign with\n   * @returns a function to sign with\n   *\n   * @example\n   * ```ts\n   * const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);\n   * const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);\n   * const signedTx3 = await signWithKeystore(tx3);\n   * ```\n   *\n   * @public\n   */\n  (key: IKeyPair): ISignFunction;\n  /**\n   * @param keys - provide the keys to sign with\n   * @returns a function to sign with\n   *\n   *\n   * @example\n   * ```ts\n   * const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);\n   * const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);\n   * const signedTx3 = await signWithKeystore(tx3);\n   * ```\n   *\n   * @public\n   */\n  (keys: IKeyPair[]): ISignFunction;\n}\n\n/**\n * function to create a `signWithKeypair` function\n * This allows you to sign subsequent transactions with the same keypair(s)\n *\n * @param keyOrKeys - provide the key or multiple keys to sign with\n * @returns a function to sign with\n *\n * @example\n * ```ts\n * const signWithKeystore = createSignWithKeypair([keyPair, keyPair2]);\n * const [signedTx1, signedTx2] = await signWithKeystore([tx1, tx2]);\n * const signedTx3 = await signWithKeystore(tx3);\n * ```\n *\n * @public\n */\nexport const createSignWithKeypair: ICreateSignWithKeypair = (keyOrKeys) => {\n  const keypairs: IKeyPair[] = Array.isArray(keyOrKeys)\n    ? keyOrKeys\n    : [keyOrKeys];\n  return async function signWithKeypair(transactionList) {\n    if (transactionList === undefined) {\n      throw new Error('No transaction(s) to sign');\n    }\n\n    const isList = Array.isArray(transactionList);\n    const transactions = isList ? transactionList : [transactionList];\n\n    const signedTransactions = transactions.map((tx) => {\n      debug(`signing transaction(s): ${JSON.stringify(tx)}`);\n      const parsedTransaction = parseTransactionCommand(tx);\n      const relevantKeypairs = getRelevantKeypairs(parsedTransaction, keypairs);\n\n      if (relevantKeypairs.length === 0) {\n        throw new Error(\n          'The keypair(s) provided are not relevant to the transaction',\n        );\n      }\n\n      return signWithKeypairs(tx, relevantKeypairs);\n    });\n\n    return isList ? signedTransactions : signedTransactions[0];\n  } as ISignFunction;\n};\n\nfunction getRelevantKeypairs(\n  tx: IPactCommand,\n  keypairs: IKeyPair[],\n): IKeyPair[] {\n  const relevantKeypairs = keypairs.filter((keypair) =>\n    tx.signers.some(({ pubKey }) => pubKey === keypair.publicKey),\n  );\n  debug('relevant keypairs', relevantKeypairs);\n  return relevantKeypairs;\n}\n\nfunction signWithKeypairs(\n  tx: IUnsignedCommand,\n  relevantKeypairs: IKeyPair[],\n): IUnsignedCommand {\n  return relevantKeypairs.reduce((tx, keypair) => {\n    const { sig, pubKey } = signHash(tx.hash, keypair);\n\n    debug(`adding signature from keypair: pubkey: ${keypair.publicKey}`);\n    return addSignatures(tx, { sig: sig!, pubKey });\n  }, tx);\n}\n"]}