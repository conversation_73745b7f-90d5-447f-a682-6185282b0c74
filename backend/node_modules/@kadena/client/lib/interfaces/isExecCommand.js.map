{"version": 3, "file": "isExecCommand.js", "sourceRoot": "", "sources": ["../../src/interfaces/isExecCommand.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,SAAgB,aAAa,CAC3B,iBAA+B;IAE/B,OAAO,MAAM,IAAI,iBAAiB,CAAC,OAAO,CAAC;AAC7C,CAAC;AAJD,sCAIC", "sourcesContent": ["import type { IExecutionPayloadObject, IPactCommand } from './IPactCommand';\n\n/**\n * @internal\n */\nexport function isExecCommand(\n  parsedTransaction: IPactCommand,\n): parsedTransaction is IPactCommand & { payload: IExecutionPayloadObject } {\n  return 'exec' in parsedTransaction.payload;\n}\n"]}