{"version": 3, "file": "ISigningRequest.js", "sourceRoot": "", "sources": ["../../src/interfaces/ISigningRequest.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ISigningCap } from '@kadena/types';\nimport type { IPactCommand } from './IPactCommand';\n\n/**\n * @alpha\n */\nexport interface ISigningRequest {\n  code: string;\n  data?: Record<string, unknown>;\n  caps: ISigningCap[];\n  nonce?: string;\n  chainId?: IPactCommand['meta']['chainId'];\n  gasLimit?: number;\n  gasPrice?: number;\n  ttl?: number;\n  sender?: string;\n  extraSigners?: string[];\n}\n"]}