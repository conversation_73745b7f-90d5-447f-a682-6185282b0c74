{"version": 3, "file": "IPactCommand.js", "sourceRoot": "", "sources": ["../../src/interfaces/IPactCommand.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ChainId, ICap, PactValue } from '@kadena/types';\nimport type { AllPartial } from './type-utilities';\n\n/**\n * The payload of a Execution transaction\n * @public\n */\nexport interface IExecutionPayloadObject {\n  // executable pact code\n  exec: {\n    code: string;\n    data: Record<string, unknown>;\n  };\n}\n\n/**\n * The payload of a Continuation transaction\n * @public\n */\nexport interface IContinuationPayloadObject {\n  cont: {\n    pactId: string;\n    step: number;\n    rollback: boolean;\n    data?: Record<string, unknown>;\n    // for none cross-chain tx, proof is null\n    // eslint-disable-next-line @rushstack/no-new-null\n    proof?: string | null;\n  };\n}\n\n/**\n * @beta\n * @deprecated Use {@link @kadena/types#ICap} instead\n */\nexport type ICapabilityItem = ICap;\n\n/**\n * @public\n */\nexport type SignerScheme = 'ED25519' | 'ETH' | 'WebAuthn';\n\n// TODO: update filed types based on @Kadena/types\n/**\n * The non-serialized transaction payload\n * @public\n */\nexport interface IPactCommand {\n  payload: IExecutionPayloadObject | IContinuationPayloadObject;\n  // the builder will add all default values\n  meta: {\n    chainId: ChainId;\n    sender?: string;\n    gasLimit?: number;\n    gasPrice?: number;\n    ttl?: number;\n    creationTime?: number;\n  };\n  signers: Array<{\n    pubKey: string;\n    address?: string;\n    scheme?: SignerScheme;\n    clist?: ICap[];\n  }>;\n  verifiers?: Array<{\n    name: string;\n    proof: PactValue;\n    clist?: ICap[];\n  }>;\n  networkId: string;\n  nonce: string;\n}\n\n/**\n * The the Partial type of {@link IPactCommand}\n * @public\n */\nexport interface IPartialPactCommand extends AllPartial<IPactCommand> {\n  payload?:\n    | { exec: Partial<IExecutionPayloadObject['exec']> }\n    | { cont: Partial<IContinuationPayloadObject['cont']> };\n}\n\n/**\n * @public\n */\nexport type BuiltInPredicate = 'keys-all' | 'keys-any' | 'keys-2';\n\n/**\n * @public\n */\nexport type ISigner =\n  | string\n  | {\n      pubKey: string;\n      scheme?: SignerScheme;\n      address?: string;\n    };\n"]}