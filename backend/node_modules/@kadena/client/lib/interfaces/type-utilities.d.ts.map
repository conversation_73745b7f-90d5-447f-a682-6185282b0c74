{"version": 3, "file": "type-utilities.d.ts", "sourceRoot": "", "sources": ["../../src/interfaces/type-utilities.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAC1C,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAE5C;;GAEG;AACH,MAAM,MAAM,mBAAmB,CAAC,CAAC,IAAI,CACnC,CAAC,SAAS,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAC3C,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,GAC1B,CAAC,GACD,KAAK,CAAC;AAEV;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IACzC,CAAC,IAAI,EAAE,UAAU,GAAG,IAAI,CAAC;CAC1B;AAED;;;;GAIG;AACH,MAAM,MAAM,cAAc,CAAC,KAAK,SAAS,MAAM,GAAG;IAAE,UAAU,EAAE,OAAO,CAAA;CAAE,IACvE,qBAAqB,CAAC;IAAE,OAAO,EAAE;QAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAA;KAAE,CAAA;CAAE,CAAC,CAAC;AAExD;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAAC,QAAQ,IAAI,QAAQ,SAAS;IAC7D,OAAO,EAAE,MAAM,QAAQ,CAAC;CACzB,GACG,QAAQ,SAAS;IAAE,IAAI,EAAE,MAAM,UAAU,CAAA;CAAE,GACzC,UAAU,SAAS,KAAK,CAAC,MAAM,SAAS,CAAC,GACvC,mBAAmB,CAAC,SAAS,CAAC,SAAS;IAAE,UAAU,EAAE,MAAM,WAAW,CAAA;CAAE,GACtE,WAAW,GACX,kBAAkB,GACpB,kBAAkB,GACpB,kBAAkB,GACpB,kBAAkB,CAAC;AAEvB,MAAM,MAAM,QAAQ,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;KACvD,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACrB,GAAG,EAAE,CAAC;AAEP,MAAM,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,QAAQ,CACvD,CAAC,GAAG;KAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAAE,CACzB,CAAC;AAEF,MAAM,MAAM,UAAU,CAAC,CAAC,IAAI;KACzB,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC3D,GAAG,EAAE,CAAC;AACP,KAAK,MAAM,GAAG,EAAE,GAAG,IAAI,MAAM,EAAE,CAAC;AAEhC,KAAK,iBAAiB,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,MAAM,YAAY,GACvD,CAAC,SAAS,MAAM,YAAY,CAAC,CAAC,CAAC,GAC7B,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,GACpD,CAAC,GACD,KAAK,GACP,KAAK,GACP,KAAK,CAAC;AAEV,KAAK,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,UAAU,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GACzE,IAAI,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,MAAM,EAAE,GAC/C,KAAK,GACL,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,GACrC,KAAK,CAAC;AAEV,KAAK,mBAAmB,CAAC,CAAC,IACxB,CAAC,SAAS,IAAI,MAAM,aAAa,IAAI,MAAM,UAAU,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,GAC3E,IAAI,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,MAAM,EAAE,GAC/C,KAAK,GACL,iBAAiB,CAAC,GAAG,aAAa,IAAI,UAAU,EAAE,EAAE,IAAI,CAAC,GAC3D,KAAK,CAAC;AAEZ,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAC3B,UAAU,CAAC,CAAC,CAAC,GACb,mBAAmB,CAAC,CAAC,CAAC,SAAS,KAAK,GACpC,MAAM,GACN,UAAU,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC"}