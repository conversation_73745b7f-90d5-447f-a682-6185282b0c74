{"version": 3, "file": "type-utilities.js", "sourceRoot": "", "sources": ["../../src/interfaces/type-utilities.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ICap } from '@kadena/types';\nimport type { IPactModules } from '../pact';\n\n/**\n * @internal\n */\nexport type UnionToIntersection<T> = (\n  T extends unknown ? (k: T) => void : never\n) extends (k: infer I) => void\n  ? I\n  : never;\n\n/**\n * @internal\n */\nexport interface IGeneralCapability {\n  (name: string, ...args: unknown[]): ICap;\n  (name: 'coin.GAS'): ICap;\n}\n\n/**\n * create withCapability type from a Pact.modules function\n *\n * @public\n */\nexport type WithCapability<TCode extends string & { capability: unknown }> =\n  ExtractCapabilityType<{ payload: { funs: [TCode] } }>;\n\n/**\n * @internal\n */\nexport type ExtractCapabilityType<TCommand> = TCommand extends {\n  payload: infer TPayload;\n}\n  ? TPayload extends { funs: infer TFunctions }\n    ? TFunctions extends Array<infer TFunction>\n      ? UnionToIntersection<TFunction> extends { capability: infer TCapability }\n        ? TCapability\n        : IGeneralCapability\n      : IGeneralCapability\n    : IGeneralCapability\n  : IGeneralCapability;\n\nexport type Prettify<T extends Record<string, unknown>> = {\n  [K in keyof T]: T[K];\n} & {};\n\nexport type WithRequired<T, K extends keyof T> = Prettify<\n  T & { [P in K]-?: T[P] }\n>;\n\nexport type AllPartial<T> = {\n  [P in keyof T]?: T[P] extends {} ? AllPartial<T[P]> : T[P];\n} & {};\ntype FnRest = '' | ` ${string}`;\n\ntype GetFuncReturnType<M, F> = M extends keyof IPactModules\n  ? F extends keyof IPactModules[M]\n    ? IPactModules[M][F] extends (...args: any[]) => infer R\n      ? R\n      : never\n    : never\n  : never;\n\ntype RootModule<T> = T extends `(${infer moduleType}.${infer func}${FnRest})`\n  ? func extends `${string} ` | `${string} ${string}`\n    ? never\n    : GetFuncReturnType<moduleType, func>\n  : never;\n\ntype ModuleWithNamespace<T> =\n  T extends `(${infer namespaceType}.${infer moduleType}.${infer func}${FnRest})`\n    ? func extends `${string} ` | `${string} ${string}`\n      ? never\n      : GetFuncReturnType<`${namespaceType}.${moduleType}`, func>\n    : never;\n\nexport type ExtractPactModule<T> =\n  | RootModule<T>\n  | ModuleWithNamespace<T> extends never\n  ? string\n  : RootModule<T> | ModuleWithNamespace<T>;\n"]}