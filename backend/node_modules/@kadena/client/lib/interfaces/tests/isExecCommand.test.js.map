{"version": 3, "file": "isExecCommand.test.js", "sourceRoot": "", "sources": ["../../../src/interfaces/tests/isExecCommand.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAE9C,oDAAiD;AAEjD,IAAA,iBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,IAAA,WAAE,EAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAA,eAAM,EACJ,IAAA,6BAAa,EAAC,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAkB,CAAC,CACvE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;QAC9C,IAAA,eAAM,EACJ,IAAA,6BAAa,EAAC,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAkB,CAAC,CACtE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport type { IPactCommand } from '../IPactCommand';\nimport { isExecCommand } from '../isExecCommand';\n\ndescribe('isExecCommand', () => {\n  it('returns true if payload is exec', () => {\n    expect(\n      isExecCommand({ payload: { exec: { code: 'test' } } } as IPactCommand),\n    ).toBe(true);\n  });\n  it('returns false if payload is not exec', () => {\n    expect(\n      isExecCommand({ payload: { cont: { pactId: '1' } } } as IPactCommand),\n    ).toBe(false);\n  });\n});\n"]}