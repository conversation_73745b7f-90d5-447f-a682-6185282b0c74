"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const isExecCommand_1 = require("../isExecCommand");
(0, vitest_1.describe)('isExecCommand', () => {
    (0, vitest_1.it)('returns true if payload is exec', () => {
        (0, vitest_1.expect)((0, isExecCommand_1.isExecCommand)({ payload: { exec: { code: 'test' } } })).toBe(true);
    });
    (0, vitest_1.it)('returns false if payload is not exec', () => {
        (0, vitest_1.expect)((0, isExecCommand_1.isExecCommand)({ payload: { cont: { pactId: '1' } } })).toBe(false);
    });
});
//# sourceMappingURL=isExecCommand.test.js.map