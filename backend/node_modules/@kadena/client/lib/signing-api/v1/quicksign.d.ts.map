{"version": 3, "file": "quicksign.d.ts", "sourceRoot": "", "sources": ["../../../src/signing-api/v1/quicksign.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,MAAM,WAAW,qBAAqB;IACpC,WAAW,EAAE,6BAA6B,EAAE,CAAC;CAC9C;AAED;;;GAGG;AACH,MAAM,WAAW,6BAA6B;IAC5C,IAAI,EAAE,gBAAgB,EAAE,CAAC;IACzB,GAAG,EAAE,MAAM,CAAC;CACb;AAED;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAC/B,MAAM,EAAE,MAAM,CAAC;IACf,GAAG,EAAE,aAAa,CAAC;CACpB;AAED;;;GAGG;AAEH,MAAM,MAAM,aAAa,GAAG,MAAM,GAAG,IAAI,CAAC;AAE1C;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAC1B,uBAAuB,GACvB,0BAA0B,CAAC;AAE/B;;;GAGG;AACH,MAAM,WAAW,0BAA0B;IACzC,SAAS,EAAE;QACT,cAAc,EAAE,yBAAyB,CAAC;QAC1C,OAAO,EACH;YACE,IAAI,EAAE,MAAM,CAAC;YACb,MAAM,EAAE,SAAS,CAAC;SACnB,GACD;YACE,GAAG,EAAE,MAAM,CAAC;YACZ,MAAM,EAAE,SAAS,CAAC;SACnB,GACD;YACE,MAAM,EAAE,OAAO,CAAC;SACjB,CAAC;KACP,EAAE,CAAC;CACL;AAED;;;GAGG;AACH,MAAM,WAAW,uBAAuB;IACtC,KAAK,EACD;QACE,IAAI,EAAE,QAAQ,CAAC;KAChB,GACD;QACE,IAAI,EAAE,WAAW,CAAC;KACnB,GACD;QACE,IAAI,EAAE,OAAO,CAAC;QACd,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;CACP;AAED;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACxC,IAAI,EAAE,gBAAgB,EAAE,CAAC;IACzB,GAAG,EAAE,MAAM,CAAC;CACb"}