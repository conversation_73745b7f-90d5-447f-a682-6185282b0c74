{"version": 3, "file": "quicksign.js", "sourceRoot": "", "sources": ["../../../src/signing-api/v1/quicksign.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Interface for the {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\nexport interface IQuickSignRequestBody {\n  cmdSigDatas: IUnsignedQuicksignTransaction[];\n}\n\n/**\n * `cmdSigData` in {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\nexport interface IUnsignedQuicksignTransaction {\n  sigs: IQuicksignSigner[];\n  cmd: string;\n}\n\n/**\n * `sigs` in {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\nexport interface IQuicksignSigner {\n  pubKey: string;\n  sig: IQuicksignSig;\n}\n\n/**\n * `sig` in {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\n// eslint-disable-next-line @rushstack/no-new-null\nexport type IQuicksignSig = string | null;\n\n/**\n * Response from {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\nexport type IQuicksignResponse =\n  | IQuicksignResponseError\n  | IQuicksignResponseOutcomes;\n\n/**\n * Succesful result from {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\nexport interface IQuicksignResponseOutcomes {\n  responses: {\n    commandSigData: IQuicksignResponseCommand;\n    outcome:\n      | {\n          hash: string;\n          result: 'success';\n        }\n      | {\n          msg: string;\n          result: 'failure';\n        }\n      | {\n          result: 'noSig';\n        };\n  }[];\n}\n\n/**\n * Error response from {@link https://github.com/kadena-io/KIPs/blob/master/kip-0017.md | quicksign API}\n * @public\n */\nexport interface IQuicksignResponseError {\n  error:\n    | {\n        type: 'reject';\n      }\n    | {\n        type: 'emptyList';\n      }\n    | {\n        type: 'other';\n        msg: string;\n      };\n}\n\n/**\n * response `commandSigData` in {@link IQuicksignResponseOutcomes}\n * @public\n */\nexport interface IQuicksignResponseCommand {\n  sigs: IQuicksignSigner[];\n  cmd: string;\n}\n"]}