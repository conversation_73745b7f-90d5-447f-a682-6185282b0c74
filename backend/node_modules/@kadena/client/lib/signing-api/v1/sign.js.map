{"version": 3, "file": "sign.js", "sourceRoot": "", "sources": ["../../../src/signing-api/v1/sign.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ICap } from '@kadena/types';\n\n/**\n * Interface for the {@link https://github.com/kadena-io/KIPs/blob/master/kip-0015.md | `sign v1` API}\n * @public\n */\nexport interface ISignBody {\n  code: string;\n  caps: {\n    role: string;\n    description: string;\n    cap: ICap;\n  }[];\n  data: Record<string, unknown>;\n  sender: string;\n  chainId: string;\n  gasLimit: number;\n  gasPrice: number;\n  ttl: number;\n  signingPubKey: string;\n  networkId: string;\n}\n"]}