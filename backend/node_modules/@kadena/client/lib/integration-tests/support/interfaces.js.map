{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../../../src/integration-tests/support/interfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ChainId } from '@kadena/types';\n\nexport interface IAccount {\n  account: string;\n  publicKey: string;\n  chainId: ChainId;\n  guard: string;\n}\n\n// this is here only for testing purposes. in a real world scenario, the secret key should never be exposed\nexport interface IAccountWithSecretKey extends IAccount {\n  secretKey: string;\n}\n"]}