{"version": 3, "file": "fp-helpers.js", "sourceRoot": "", "sources": ["../../../src/integration-tests/helpers/fp-helpers.ts"], "names": [], "mappings": ";;;AAGA,uBAAuB;AAChB,MAAM,SAAS,GACpB,CAAC,GAAG,IAA8B,EAAiC,EAAE,CACrE,CAAC,IAAS,EAAgB,EAAE,CAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAHzD,QAAA,SAAS,aAGgD;AAEtE,8DAA8D;AACvD,MAAM,IAAI,GAAG,CAAC,IAAW,EAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAArC,QAAA,IAAI,QAAiC;AAE3C,MAAM,OAAO,GAClB,CAAC,GAAW,EAAuC,EAAE;AACrD,8DAA8D;AAC9D,CAAgB,IAAO,EAAK,EAAE;IAC5B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACvB,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AANS,QAAA,OAAO,WAMhB", "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-explicit-any\ntype all = any;\n\n// pipe async functions\nexport const asyncPipe =\n  (...args: Array<(arg: all) => all>): ((init: all) => Promise<all>) =>\n  (init: all): Promise<all> =>\n    args.reduce((chain, fn) => chain.then(fn), Promise.resolve(init));\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const head = (args: all[]): any => args[0];\n\nexport const inspect =\n  (tag: string): (<T extends unknown>(data: T) => T) =>\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  <T extends any>(data: T): T => {\n    console.log(tag, data);\n    return data;\n  };\n"]}