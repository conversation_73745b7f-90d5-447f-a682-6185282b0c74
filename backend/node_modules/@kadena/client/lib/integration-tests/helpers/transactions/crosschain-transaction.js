"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.executeCrossChainTransfer = void 0;
const index_1 = require("../../../index");
const enums_1 = require("../../support/enums");
const keyFromAccount_1 = require("../account/keyFromAccount");
const client_1 = require("../client");
const sign_transaction_1 = require("./sign-transaction");
function startCrossChainTransfer(from, to, amount) {
    return (index_1.Pact.builder
        .execution(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    index_1.Pact.modules.coin.defpact['transfer-crosschain'](from.account, to.account, (0, index_1.readKeyset)('receiver-guard'), to.chainId, {
        decimal: amount.toString(),
    }))
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .addSigner(from.publicKey, (withCapability) => [
        withCapability('coin.GAS'),
        withCapability('coin.TRANSFER_XCHAIN', from.account, to.account, {
            decimal: amount,
        }, to.chainId),
    ])
        .addKeyset('receiver-guard', 'keys-all', to.publicKey)
        .setMeta({ chainId: from.chainId, senderAccount: from.account })
        .setNetworkId(enums_1.NetworkId.fast_development)
        .createTransaction());
}
function finishInTheTargetChain(continuation, targetChainId, gasPayer) {
    const builder = index_1.Pact.builder
        .continuation(continuation)
        .setNetworkId(enums_1.NetworkId.fast_development)
        // uncomment this if you want to pay gas yourself
        .addSigner((0, keyFromAccount_1.keyFromAccount)(gasPayer), (withCapability) => [
        withCapability('coin.GAS'),
    ])
        .setMeta({
        chainId: targetChainId,
        senderAccount: gasPayer,
        // this need to be less than or equal to 850 if you want to use gas-station, otherwise the gas-station does not pay the gas
        gasLimit: 850,
    });
    return builder.createTransaction();
}
async function executeCrossChainTransfer(from, to, amount) {
    return (Promise.resolve(startCrossChainTransfer(from, to, amount))
        //    .then(inspect('command'))
        .then((command) => (0, sign_transaction_1.signByKeyPair)(command, from))
        //     .then(inspect('command'))
        .then((command) => (0, index_1.isSignedTransaction)(command)
        ? command
        : Promise.reject('CMD_NOT_SIGNED'))
        // inspect is only for development you can remove them
        //    .then(inspect('EXEC_SIGNED'))
        .then((cmd) => (0, client_1.submit)(cmd))
        //  .then(inspect('SUBMIT_RESULT'))
        .then(client_1.listen)
        //  .then(inspect('LISTEN_RESULT'))
        .then((status) => status.result.status === 'failure'
        ? Promise.reject(new Error('DEBIT REJECTED'))
        : status)
        .then((status) => Promise.all([
        status,
        (0, client_1.pollCreateSpv)({
            requestKey: status.reqKey,
            networkId: enums_1.NetworkId.fast_development,
            chainId: from.chainId,
        }, to.chainId),
    ]))
        .then(([status, proof]) => finishInTheTargetChain({
        pactId: status.continuation.pactId,
        proof,
        rollback: false,
        step: 1,
    }, to.chainId, to.account))
        .then((command) => (0, sign_transaction_1.signByKeyPair)(command, from))
        .then((command) => (0, index_1.isSignedTransaction)(command)
        ? command
        : Promise.reject('CMD_NOT_SIGNED'))
        .then((cmd) => (0, client_1.submit)(cmd))
        .then(client_1.pollStatus));
}
exports.executeCrossChainTransfer = executeCrossChainTransfer;
//# sourceMappingURL=crosschain-transaction.js.map