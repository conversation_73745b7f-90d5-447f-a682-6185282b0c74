"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.signByKeyPair = void 0;
const cryptography_utils_1 = require("@kadena/cryptography-utils");
function signByKeyPair(transaction, keyPair) {
    const { sig } = (0, cryptography_utils_1.sign)(transaction.cmd, keyPair);
    if (sig === undefined) {
        throw new Error('SIG_IS_UNDEFINED');
    }
    transaction.sigs = [{ sig }];
    return transaction;
}
exports.signByKeyPair = signByKeyPair;
//# sourceMappingURL=sign-transaction.js.map