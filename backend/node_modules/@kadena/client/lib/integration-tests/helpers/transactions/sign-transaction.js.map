{"version": 3, "file": "sign-transaction.js", "sourceRoot": "", "sources": ["../../../../src/integration-tests/helpers/transactions/sign-transaction.ts"], "names": [], "mappings": ";;;AAAA,mEAAkD;AAGlD,SAAgB,aAAa,CAC3B,WAA6B,EAC7B,OAAiD;IAEjD,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,yBAAI,EAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC/C,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IACD,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7B,OAAO,WAAuB,CAAC;AACjC,CAAC;AAVD,sCAUC", "sourcesContent": ["import { sign } from '@kadena/cryptography-utils';\nimport type { ICommand, IUnsignedCommand } from '@kadena/types';\n\nexport function signByKeyPair(\n  transaction: IUnsignedCommand,\n  keyPair: { publicKey: string; secretKey: string },\n): ICommand {\n  const { sig } = sign(transaction.cmd, keyPair);\n  if (sig === undefined) {\n    throw new Error('SIG_IS_UNDEFINED');\n  }\n  transaction.sigs = [{ sig }];\n  return transaction as ICommand;\n}\n"]}