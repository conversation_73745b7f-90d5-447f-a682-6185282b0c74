{"version": 3, "file": "crosschain-transaction.js", "sourceRoot": "", "sources": ["../../../../src/integration-tests/helpers/transactions/crosschain-transaction.ts"], "names": [], "mappings": ";;;AAGA,0CAAuE;AACvE,+CAAgD;AAEhD,8DAA2D;AAC3D,sCAAsE;AACtE,yDAAmD;AAEnD,SAAS,uBAAuB,CAC9B,IAA2B,EAC3B,EAAY,EACZ,MAAc;IAEd,OAAO,CACL,YAAI,CAAC,OAAO;SACT,SAAS;IACR,8DAA8D;IAC7D,YAAI,CAAC,OAAe,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CACvD,IAAI,CAAC,OAAO,EACZ,EAAE,CAAC,OAAO,EACV,IAAA,kBAAU,EAAC,gBAAgB,CAAC,EAC5B,EAAE,CAAC,OAAO,EACV;QACE,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE;KAC3B,CACF,CACF;QACD,8DAA8D;SAC7D,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,cAAmB,EAAE,EAAE,CAAC;QAClD,cAAc,CAAC,UAAU,CAAC;QAC1B,cAAc,CACZ,sBAAsB,EACtB,IAAI,CAAC,OAAO,EACZ,EAAE,CAAC,OAAO,EACV;YACE,OAAO,EAAE,MAAM;SAChB,EACD,EAAE,CAAC,OAAO,CACX;KACF,CAAC;SACD,SAAS,CAAC,gBAAgB,EAAE,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC;SACrD,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;SAC/D,YAAY,CAAC,iBAAS,CAAC,gBAAgB,CAAC;SACxC,iBAAiB,EAAE,CACvB,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAC7B,YAAgD,EAChD,aAAsB,EACtB,QAAgB;IAEhB,MAAM,OAAO,GAAG,YAAI,CAAC,OAAO;SACzB,YAAY,CAAC,YAAY,CAAC;SAC1B,YAAY,CAAC,iBAAS,CAAC,gBAAgB,CAAC;QACzC,iDAAiD;SAChD,SAAS,CAAC,IAAA,+BAAc,EAAC,QAAQ,CAAC,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;QACvD,cAAc,CAAC,UAAU,CAAC;KAC3B,CAAC;SACD,OAAO,CAAC;QACP,OAAO,EAAE,aAAa;QACtB,aAAa,EAAE,QAAQ;QACvB,2HAA2H;QAC3H,QAAQ,EAAE,GAAG;KACd,CAAC,CAAC;IAEL,OAAO,OAAO,CAAC,iBAAiB,EAAE,CAAC;AACrC,CAAC;AAEM,KAAK,UAAU,yBAAyB,CAC7C,IAAsC,EACtC,EAAY,EACZ,MAAc;IAEd,OAAO,CACL,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACxD,+BAA+B;SAC9B,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAA,gCAAa,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChD,gCAAgC;SAC/B,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAChB,IAAA,2BAAmB,EAAC,OAAO,CAAC;QAC1B,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CACrC;QACD,sDAAsD;QACtD,mCAAmC;SAClC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,eAAM,EAAC,GAAG,CAAC,CAAC;QAC3B,mCAAmC;SAClC,IAAI,CAAC,eAAM,CAAC;QACb,mCAAmC;SAClC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACf,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS;QAChC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC7C,CAAC,CAAC,MAAM,CACX;SACA,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACf,OAAO,CAAC,GAAG,CAAC;QACV,MAAM;QACN,IAAA,sBAAa,EACX;YACE,UAAU,EAAE,MAAM,CAAC,MAAM;YACzB,SAAS,EAAE,iBAAS,CAAC,gBAAgB;YACrC,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,EACD,EAAE,CAAC,OAAO,CACX;KACF,CAAC,CACH;SACA,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,CACxB,sBAAsB,CACpB;QACE,MAAM,EAAE,MAAM,CAAC,YAAa,CAAC,MAAM;QACnC,KAAK;QACL,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC;KACR,EACD,EAAE,CAAC,OAAO,EACV,EAAE,CAAC,OAAO,CACX,CACF;SACA,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAA,gCAAa,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC/C,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAChB,IAAA,2BAAmB,EAAC,OAAO,CAAC;QAC1B,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CACrC;SACA,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,eAAM,EAAC,GAAG,CAAC,CAAC;SAC1B,IAAI,CAAC,mBAAU,CAAC,CACpB,CAAC;AACJ,CAAC;AA5DD,8DA4DC", "sourcesContent": ["import type { ICommandResult } from '@kadena/chainweb-node-client';\nimport type { ChainId, IUnsignedCommand } from '@kadena/types';\nimport type { IContinuationPayloadObject } from '../../../index';\nimport { Pact, isSignedTransaction, readKeyset } from '../../../index';\nimport { NetworkId } from '../../support/enums';\nimport type { IAccount, IAccountWithSecretKey } from '../../support/interfaces';\nimport { keyFromAccount } from '../account/keyFromAccount';\nimport { listen, pollCreateSpv, pollStatus, submit } from '../client';\nimport { signByKeyPair } from './sign-transaction';\n\nfunction startCrossChainTransfer(\n  from: IAccountWithSecretKey,\n  to: IAccount,\n  amount: string,\n): IUnsignedCommand {\n  return (\n    Pact.builder\n      .execution(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (Pact.modules as any).coin.defpact['transfer-crosschain'](\n          from.account,\n          to.account,\n          readKeyset('receiver-guard'),\n          to.chainId,\n          {\n            decimal: amount.toString(),\n          },\n        ),\n      )\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .addSigner(from.publicKey, (withCapability: any) => [\n        withCapability('coin.GAS'),\n        withCapability(\n          'coin.TRANSFER_XCHAIN',\n          from.account,\n          to.account,\n          {\n            decimal: amount,\n          },\n          to.chainId,\n        ),\n      ])\n      .addKeyset('receiver-guard', 'keys-all', to.publicKey)\n      .setMeta({ chainId: from.chainId, senderAccount: from.account })\n      .setNetworkId(NetworkId.fast_development)\n      .createTransaction()\n  );\n}\n\nfunction finishInTheTargetChain(\n  continuation: IContinuationPayloadObject['cont'],\n  targetChainId: ChainId,\n  gasPayer: string,\n): IUnsignedCommand {\n  const builder = Pact.builder\n    .continuation(continuation)\n    .setNetworkId(NetworkId.fast_development)\n    // uncomment this if you want to pay gas yourself\n    .addSigner(keyFromAccount(gasPayer), (withCapability) => [\n      withCapability('coin.GAS'),\n    ])\n    .setMeta({\n      chainId: targetChainId,\n      senderAccount: gasPayer,\n      // this need to be less than or equal to 850 if you want to use gas-station, otherwise the gas-station does not pay the gas\n      gasLimit: 850,\n    });\n\n  return builder.createTransaction();\n}\n\nexport async function executeCrossChainTransfer(\n  from: IAccount & { secretKey: string },\n  to: IAccount,\n  amount: string,\n): Promise<Record<string, ICommandResult>> {\n  return (\n    Promise.resolve(startCrossChainTransfer(from, to, amount))\n      //    .then(inspect('command'))\n      .then((command) => signByKeyPair(command, from))\n      //     .then(inspect('command'))\n      .then((command) =>\n        isSignedTransaction(command)\n          ? command\n          : Promise.reject('CMD_NOT_SIGNED'),\n      )\n      // inspect is only for development you can remove them\n      //    .then(inspect('EXEC_SIGNED'))\n      .then((cmd) => submit(cmd))\n      //  .then(inspect('SUBMIT_RESULT'))\n      .then(listen)\n      //  .then(inspect('LISTEN_RESULT'))\n      .then((status) =>\n        status.result.status === 'failure'\n          ? Promise.reject(new Error('DEBIT REJECTED'))\n          : status,\n      )\n      .then((status) =>\n        Promise.all([\n          status,\n          pollCreateSpv(\n            {\n              requestKey: status.reqKey,\n              networkId: NetworkId.fast_development,\n              chainId: from.chainId,\n            },\n            to.chainId,\n          ),\n        ]),\n      )\n      .then(([status, proof]) =>\n        finishInTheTargetChain(\n          {\n            pactId: status.continuation!.pactId,\n            proof,\n            rollback: false,\n            step: 1,\n          },\n          to.chainId,\n          to.account,\n        ),\n      )\n      .then((command) => signByKeyPair(command, from))\n      .then((command) =>\n        isSignedTransaction(command)\n          ? command\n          : Promise.reject('CMD_NOT_SIGNED'),\n      )\n      .then((cmd) => submit(cmd))\n      .then(pollStatus)\n  );\n}\n"]}