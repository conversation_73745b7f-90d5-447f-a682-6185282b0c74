{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/integration-tests/helpers/client.ts"], "names": [], "mappings": ";;;;AACA,uCAA2C;AAEpC,MAAM,gBAAgB,GAAG,CAAC,EAC/B,SAAS,EACT,OAAO,GAIR,EAAU,EAAE;IACX,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,aAAa;YAChB,OAAO,sCAAsC,SAAS,UACpD,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,GACb,OAAO,CAAC;QACV;YACE,OAAO,sCAAsC,SAAS,UACpD,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,GACb,OAAO,CAAC;IACZ,CAAC;AACH,CAAC,CAAC;AAjBW,QAAA,gBAAgB,oBAiB3B;AAEF,gDAAgD;AACnC,KAST,IAAA,oBAAY,EAAC,wBAAgB,CAAC,EARhC,cAAM,cACN,cAAM,cACN,iBAAS,iBACT,iBAAS,iBACT,qBAAa,qBACb,kBAAU,kBACV,iBAAS,iBACT,iBAAS,gBACwB", "sourcesContent": ["import type { ChainId, NetworkId } from '@kadena/types';\nimport { createClient } from '../../index';\n\nexport const apiHostGenerator = ({\n  networkId,\n  chainId,\n}: {\n  networkId: NetworkId;\n  chainId: ChainId;\n}): string => {\n  switch (networkId) {\n    case 'development':\n      return `http://127.0.0.1:8080/chainweb/0.0/${networkId}/chain/${\n        chainId ?? '1'\n      }/pact`;\n    default:\n      return `http://127.0.0.1:8080/chainweb/0.0/${networkId}/chain/${\n        chainId ?? '1'\n      }/pact`;\n  }\n};\n\n// configure the client and export the functions\nexport const {\n  listen,\n  submit,\n  preflight,\n  dirtyRead,\n  pollCreateSpv,\n  pollStatus,\n  getStatus,\n  createSpv,\n} = createClient(apiHostGenerator);\n"]}