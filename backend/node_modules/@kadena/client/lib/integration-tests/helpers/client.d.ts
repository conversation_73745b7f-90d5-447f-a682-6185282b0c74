import type { ChainId, NetworkId } from '@kadena/types';
export declare const apiHostGenerator: ({ networkId, chainId, }: {
    networkId: NetworkId;
    chainId: ChainId;
}) => string;
export declare const listen: (transactionDescriptor: import("../../index").ITransactionDescriptor, options?: import("@kadena/chainweb-node-client").ClientRequestInit | undefined) => Promise<import("@kadena/chainweb-node-client").ICommandResult>, submit: import("../../index").ISubmit, preflight: (transaction: import("@kadena/types").IUnsignedCommand | import("@kadena/types").ICommand, options?: import("@kadena/chainweb-node-client").ClientRequestInit | undefined) => Promise<import("@kadena/chainweb-node-client").ILocalCommandResult>, dirtyRead: (transaction: import("@kadena/types").IUnsignedCommand, options?: import("@kadena/chainweb-node-client").ClientRequestInit | undefined) => Promise<import("@kadena/chainweb-node-client").ICommandResult>, pollCreateSpv: (transactionDescriptor: import("../../index").ITransactionDescriptor, targetChainId: ChainId, options?: import("../../index").IPollOptions | undefined) => Promise<string>, pollStatus: (transactionDescriptors: import("../../index").ITransactionDescriptor | import("../../index").ITransactionDescriptor[], options?: import("../../index").IPollOptions | undefined) => import("../../index").IPollRequestPromise<import("@kadena/chainweb-node-client").ICommandResult>, getStatus: (transactionDescriptors: import("../../index").ITransactionDescriptor | import("../../index").ITransactionDescriptor[], options?: import("@kadena/chainweb-node-client").ClientRequestInit | undefined) => Promise<import("@kadena/chainweb-node-client").IPollResponse>, createSpv: (transactionDescriptor: import("../../index").ITransactionDescriptor, targetChainId: ChainId, options?: import("@kadena/chainweb-node-client").ClientRequestInit | undefined) => Promise<string>;
//# sourceMappingURL=client.d.ts.map