"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.inspect = exports.head = exports.asyncPipe = void 0;
// pipe async functions
const asyncPipe = (...args) => (init) => args.reduce((chain, fn) => chain.then(fn), Promise.resolve(init));
exports.asyncPipe = asyncPipe;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const head = (args) => args[0];
exports.head = head;
const inspect = (tag) => 
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(data) => {
    console.log(tag, data);
    return data;
};
exports.inspect = inspect;
//# sourceMappingURL=fp-helpers.js.map