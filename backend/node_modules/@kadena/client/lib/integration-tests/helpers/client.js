"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSpv = exports.getStatus = exports.pollStatus = exports.pollCreateSpv = exports.dirtyRead = exports.preflight = exports.submit = exports.listen = exports.apiHostGenerator = void 0;
const index_1 = require("../../index");
const apiHostGenerator = ({ networkId, chainId, }) => {
    switch (networkId) {
        case 'development':
            return `http://127.0.0.1:8080/chainweb/0.0/${networkId}/chain/${chainId !== null && chainId !== void 0 ? chainId : '1'}/pact`;
        default:
            return `http://127.0.0.1:8080/chainweb/0.0/${networkId}/chain/${chainId !== null && chainId !== void 0 ? chainId : '1'}/pact`;
    }
};
exports.apiHostGenerator = apiHostGenerator;
// configure the client and export the functions
_a = (0, index_1.createClient)(exports.apiHostGenerator), exports.listen = _a.listen, exports.submit = _a.submit, exports.preflight = _a.preflight, exports.dirtyRead = _a.dirtyRead, exports.pollCreateSpv = _a.pollCreateSpv, exports.pollStatus = _a.pollStatus, exports.getStatus = _a.getStatus, exports.createSpv = _a.createSpv;
//# sourceMappingURL=client.js.map