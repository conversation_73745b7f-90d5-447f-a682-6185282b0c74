{"version": 3, "file": "fund-account.js", "sourceRoot": "", "sources": ["../../../../src/integration-tests/helpers/account/fund-account.ts"], "names": [], "mappings": ";;;AACA,0CAAkD;AAClD,+CAAgD;AAChD,uDAA2D;AAC3D,sCAAsD;AACtD,uEAAiE;AAE1D,KAAK,UAAU,WAAW,CAC/B,QAAgB,EAChB,WAAmB,EACnB,MAAoB,EACpB,KAAc;IAEd,MAAM,WAAW,GAAG,YAAI,CAAC,OAAO;SAC7B,SAAS;IACR,8DAA8D;IAC7D,YAAI,CAAC,OAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAC3C,0BAAe,CAAC,OAAO,EACvB,QAAQ,EACR,IAAA,kBAAU,EAAC,IAAI,CAAC,EAChB,MAAM,CACP,CACF;QACD,8DAA8D;SAC7D,SAAS,CAAC,0BAAe,CAAC,SAAS,EAAE,CAAC,cAAmB,EAAE,EAAE,CAAC;QAC7D,cAAc,CAAC,UAAU,CAAC;QAC1B,cAAc,CACZ,eAAe,EACf,0BAAe,CAAC,OAAO,EACvB,QAAQ,EACR,MAAM,CACP;KACF,CAAC;SACD,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC;SACxC,OAAO,CAAC;QACP,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,0BAAe,CAAC,OAAO;KACvC,CAAC;SACD,YAAY,CAAC,iBAAS,CAAC,gBAAgB,CAAC;SACxC,iBAAiB,EAAE,CAAC;IAEvB,MAAM,QAAQ,GAAG,IAAA,gCAAa,EAAC,WAAW,EAAE,0BAAe,CAAC,CAAC;IAE7D,MAAM,eAAe,GAAG,MAAM,IAAA,kBAAS,EAAC,QAAQ,CAAC,CAAC;IAClD,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAChD,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,MAAM,UAAU,GAAG,MAAM,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC;IAC1C,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAM,EAAC,UAAU,CAAC,CAAC;IAC1C,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;AAChC,CAAC;AA/CD,kCA+CC", "sourcesContent": ["import type { ChainId, IPactDecimal } from '@kadena/types';\nimport { Pact, readKeyset } from '../../../index';\nimport { NetworkId } from '../../support/enums';\nimport { sender00Account } from '../../test-data/accounts';\nimport { listen, preflight, submit } from '../client';\nimport { signByKeyPair } from '../transactions/sign-transaction';\n\nexport async function fundAccount(\n  receiver: string,\n  receiverKey: string,\n  amount: IPactDecimal,\n  chain: ChainId,\n): Promise<string | undefined> {\n  const transaction = Pact.builder\n    .execution(\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (Pact.modules as any).coin['transfer-create'](\n        sender00Account.account,\n        receiver,\n        readKeyset('ks'),\n        amount,\n      ),\n    )\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .addSigner(sender00Account.publicKey, (withCapability: any) => [\n      withCapability('coin.GAS'),\n      withCapability(\n        'coin.TRANSFER',\n        sender00Account.account,\n        receiver,\n        amount,\n      ),\n    ])\n    .addKeyset('ks', 'keys-all', receiverKey)\n    .setMeta({\n      chainId: chain,\n      gasLimit: 1000,\n      gasPrice: 1.0e-6,\n      senderAccount: sender00Account.account,\n    })\n    .setNetworkId(NetworkId.fast_development)\n    .createTransaction();\n\n  const signedTx = signByKeyPair(transaction, sender00Account);\n\n  const preflightResult = await preflight(signedTx);\n  if (preflightResult.result.status === 'failure') {\n    console.error(preflightResult.result.error);\n    return 'Preflight failed';\n  }\n\n  const requestKey = await submit(signedTx);\n  const response = await listen(requestKey);\n  return response.result.status;\n}\n"]}