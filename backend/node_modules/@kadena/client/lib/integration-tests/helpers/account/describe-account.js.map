{"version": 3, "file": "describe-account.js", "sourceRoot": "", "sources": ["../../../../src/integration-tests/helpers/account/describe-account.ts"], "names": [], "mappings": ";;;AACA,wCAAqC;AACrC,+CAAgD;AAChD,sCAAsC;AAE/B,KAAK,UAAU,UAAU,CAC9B,OAAe,EACf,OAAgB;IAEhB,MAAM,EAAE,GAAG,WAAI,CAAC,OAAO;QACrB,8DAA8D;SAC7D,SAAS,CAAE,WAAI,CAAC,OAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC;SAC7D,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;SAC7B,YAAY,CAAC,iBAAS,CAAC,gBAAgB,CAAC;SACxC,iBAAiB,EAAE,CAAC;IAEvB,MAAM,QAAQ,GAAG,MAAM,IAAA,kBAAS,EAAC,EAAE,CAAC,CAAC;IACrC,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACzC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;IACD,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAc,CAAC;AACxC,CAAC;AAjBD,gCAiBC", "sourcesContent": ["import type { ChainId } from '@kadena/types';\nimport { Pact } from '../../../pact';\nimport { NetworkId } from '../../support/enums';\nimport { dirtyRead } from '../client';\n\nexport async function getBalance(\n  account: string,\n  chainId: ChainId,\n): Promise<number> {\n  const tr = Pact.builder\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .execution((Pact.modules as any).coin['get-balance'](account))\n    .setMeta({ chainId: chainId })\n    .setNetworkId(NetworkId.fast_development)\n    .createTransaction();\n\n  const response = await dirtyRead(tr);\n  if (response.result.status === 'failure') {\n    console.error(response);\n    throw new Error('Unable to retrieve balance');\n  }\n  return response.result.data as number;\n}\n"]}