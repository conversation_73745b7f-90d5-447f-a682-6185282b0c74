{"version": 3, "file": "keyFromAccount.js", "sourceRoot": "", "sources": ["../../../../src/integration-tests/helpers/account/keyFromAccount.ts"], "names": [], "mappings": ";;;AACA,SAAgB,cAAc,CAAC,OAAgB;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,kCAAkC,OAAO,GAAG,CAAC,CAAC;IAChE,CAAC;IACD,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AALD,wCAKC", "sourcesContent": ["export type Account = `${'k' | 'w'}:${string}` | string;\nexport function keyFromAccount(account: Account): string {\n  if (!account.includes('k:')) {\n    throw new Error(`Not able to retrieve key from '${account}'`);\n  }\n  return account.split(':')[1];\n}\n"]}