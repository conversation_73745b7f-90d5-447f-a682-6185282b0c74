"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBalance = void 0;
const pact_1 = require("../../../pact");
const enums_1 = require("../../support/enums");
const client_1 = require("../client");
async function getBalance(account, chainId) {
    const tr = pact_1.Pact.builder
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .execution(pact_1.Pact.modules.coin['get-balance'](account))
        .setMeta({ chainId: chainId })
        .setNetworkId(enums_1.NetworkId.fast_development)
        .createTransaction();
    const response = await (0, client_1.dirtyRead)(tr);
    if (response.result.status === 'failure') {
        console.error(response);
        throw new Error('Unable to retrieve balance');
    }
    return response.result.data;
}
exports.getBalance = getBalance;
//# sourceMappingURL=describe-account.js.map