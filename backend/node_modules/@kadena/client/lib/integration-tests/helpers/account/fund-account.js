"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fundAccount = void 0;
const index_1 = require("../../../index");
const enums_1 = require("../../support/enums");
const accounts_1 = require("../../test-data/accounts");
const client_1 = require("../client");
const sign_transaction_1 = require("../transactions/sign-transaction");
async function fundAccount(receiver, receiverKey, amount, chain) {
    const transaction = index_1.Pact.builder
        .execution(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    index_1.Pact.modules.coin['transfer-create'](accounts_1.sender00Account.account, receiver, (0, index_1.readKeyset)('ks'), amount))
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .addSigner(accounts_1.sender00Account.publicKey, (withCapability) => [
        withCapability('coin.GAS'),
        withCapability('coin.TRANSFER', accounts_1.sender00Account.account, receiver, amount),
    ])
        .addKeyset('ks', 'keys-all', receiverKey)
        .setMeta({
        chainId: chain,
        gasLimit: 1000,
        gasPrice: 1.0e-6,
        senderAccount: accounts_1.sender00Account.account,
    })
        .setNetworkId(enums_1.NetworkId.fast_development)
        .createTransaction();
    const signedTx = (0, sign_transaction_1.signByKeyPair)(transaction, accounts_1.sender00Account);
    const preflightResult = await (0, client_1.preflight)(signedTx);
    if (preflightResult.result.status === 'failure') {
        console.error(preflightResult.result.error);
        return 'Preflight failed';
    }
    const requestKey = await (0, client_1.submit)(signedTx);
    const response = await (0, client_1.listen)(requestKey);
    return response.result.status;
}
exports.fundAccount = fundAccount;
//# sourceMappingURL=fund-account.js.map