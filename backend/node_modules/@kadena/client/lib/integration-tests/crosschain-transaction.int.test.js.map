{"version": 3, "file": "crosschain-transaction.int.test.js", "sourceRoot": "", "sources": ["../../src/integration-tests/crosschain-transaction.int.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,yEAAgE;AAChE,iEAA6D;AAC7D,0FAA0F;AAC1F,mDAAoE;AAEpE,IAAI,oBAA4B,CAAC;AACjC,IAAI,oBAA4B,CAAC;AAEjC,IAAA,iBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;QACzD,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAW,EAC9B,wBAAa,CAAC,OAAO,EACrB,wBAAa,CAAC,SAAS,EACvB,EAAE,OAAO,EAAE,KAAK,EAAE,EAClB,wBAAa,CAAC,OAAO,CACtB,CAAC;QACF,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/B,oBAAoB,GAAG,MAAM,IAAA,6BAAU,EAAC,wBAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpE,IAAA,eAAM,EAAC,oBAAoB,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,iEAAiE;IACjE,kEAAkE;IAClE,IAAA,WAAE,EAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;QACzD,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAW,EAC9B,wBAAa,CAAC,OAAO,EACrB,wBAAa,CAAC,SAAS,EACvB,EAAE,OAAO,EAAE,KAAK,EAAE;QAClB,qDAAqD;QACrD,wBAAa,CAAC,OAAO,CACtB,CAAC;QACF,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/B,oBAAoB,GAAG,MAAM,IAAA,6BAAU,EAAC,wBAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpE,IAAA,eAAM,EAAC,oBAAoB,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;QAChE,MAAM,IAAA,kDAAyB,EAAC,wBAAa,EAAE,wBAAa,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;QACrE,MAAM,UAAU,GAAG,MAAM,IAAA,6BAAU,EACjC,wBAAa,CAAC,OAAO,EACrB,wBAAa,CAAC,OAAO,CACtB,CAAC;QACF,IAAA,eAAM,EAAC,UAAU,CAAC,CAAC,YAAY,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;QAChE,oBAAoB,GAAG,MAAM,IAAA,6BAAU,EACrC,wBAAa,CAAC,OAAO,EACrB,wBAAa,CAAC,OAAO,CACtB,CAAC;QACF,2DAA2D;QAC3D,IAAA,eAAM,EAAC,oBAAoB,CAAC,CAAC,sBAAsB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { getBalance } from './helpers/account/describe-account';\nimport { fundAccount } from './helpers/account/fund-account';\nimport { executeCrossChainTransfer } from './helpers/transactions/crosschain-transaction';\nimport { sourceAccount, targetAccount } from './test-data/accounts';\n\nlet initialSourceBalance: number;\nlet initialTargetBalance: number;\n\ndescribe('Cross Chain Transfer', () => {\n  it('should fund the source account on chain 0', async () => {\n    const result = await fundAccount(\n      sourceAccount.account,\n      sourceAccount.publicKey,\n      { decimal: '100' },\n      sourceAccount.chainId,\n    );\n    expect(result).toBe('success');\n    initialSourceBalance = await getBalance(sourceAccount.account, '0');\n    expect(initialSourceBalance).toBeGreaterThanOrEqual(100);\n  });\n\n  // this is required as the source account is used to pay for gas;\n  // in mainnet this is not required as the gas station pays for gas\n  it('should fund the source account on chain 1', async () => {\n    const result = await fundAccount(\n      sourceAccount.account,\n      sourceAccount.publicKey,\n      { decimal: '100' },\n      // because we need to pay gas fee on the target chain\n      targetAccount.chainId,\n    );\n    expect(result).toBe('success');\n    initialTargetBalance = await getBalance(sourceAccount.account, '1');\n    expect(initialTargetBalance).toBeGreaterThanOrEqual(100);\n  });\n\n  it('should be able to perform a cross chain transfer', async () => {\n    await executeCrossChainTransfer(sourceAccount, targetAccount, '5');\n  });\n\n  it('Should have deducted balance from the source account ', async () => {\n    const newBalance = await getBalance(\n      sourceAccount.account,\n      sourceAccount.chainId,\n    );\n    expect(newBalance).toBeLessThan(initialSourceBalance - 5);\n  });\n\n  it('Should have added balance to the target account ', async () => {\n    initialTargetBalance = await getBalance(\n      targetAccount.account,\n      targetAccount.chainId,\n    );\n    //Even though we transferred 5 KDA, we have to pay for gas.\n    expect(initialTargetBalance).toBeGreaterThanOrEqual(100 + 4.9);\n  });\n});\n"]}