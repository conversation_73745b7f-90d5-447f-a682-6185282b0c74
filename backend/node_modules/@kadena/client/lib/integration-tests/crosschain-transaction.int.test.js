"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const describe_account_1 = require("./helpers/account/describe-account");
const fund_account_1 = require("./helpers/account/fund-account");
const crosschain_transaction_1 = require("./helpers/transactions/crosschain-transaction");
const accounts_1 = require("./test-data/accounts");
let initialSourceBalance;
let initialTargetBalance;
(0, vitest_1.describe)('Cross Chain Transfer', () => {
    (0, vitest_1.it)('should fund the source account on chain 0', async () => {
        const result = await (0, fund_account_1.fundAccount)(accounts_1.sourceAccount.account, accounts_1.sourceAccount.publicKey, { decimal: '100' }, accounts_1.sourceAccount.chainId);
        (0, vitest_1.expect)(result).toBe('success');
        initialSourceBalance = await (0, describe_account_1.getBalance)(accounts_1.sourceAccount.account, '0');
        (0, vitest_1.expect)(initialSourceBalance).toBeGreaterThanOrEqual(100);
    });
    // this is required as the source account is used to pay for gas;
    // in mainnet this is not required as the gas station pays for gas
    (0, vitest_1.it)('should fund the source account on chain 1', async () => {
        const result = await (0, fund_account_1.fundAccount)(accounts_1.sourceAccount.account, accounts_1.sourceAccount.publicKey, { decimal: '100' }, 
        // because we need to pay gas fee on the target chain
        accounts_1.targetAccount.chainId);
        (0, vitest_1.expect)(result).toBe('success');
        initialTargetBalance = await (0, describe_account_1.getBalance)(accounts_1.sourceAccount.account, '1');
        (0, vitest_1.expect)(initialTargetBalance).toBeGreaterThanOrEqual(100);
    });
    (0, vitest_1.it)('should be able to perform a cross chain transfer', async () => {
        await (0, crosschain_transaction_1.executeCrossChainTransfer)(accounts_1.sourceAccount, accounts_1.targetAccount, '5');
    });
    (0, vitest_1.it)('Should have deducted balance from the source account ', async () => {
        const newBalance = await (0, describe_account_1.getBalance)(accounts_1.sourceAccount.account, accounts_1.sourceAccount.chainId);
        (0, vitest_1.expect)(newBalance).toBeLessThan(initialSourceBalance - 5);
    });
    (0, vitest_1.it)('Should have added balance to the target account ', async () => {
        initialTargetBalance = await (0, describe_account_1.getBalance)(accounts_1.targetAccount.account, accounts_1.targetAccount.chainId);
        //Even though we transferred 5 KDA, we have to pay for gas.
        (0, vitest_1.expect)(initialTargetBalance).toBeGreaterThanOrEqual(100 + 4.9);
    });
});
//# sourceMappingURL=crosschain-transaction.int.test.js.map