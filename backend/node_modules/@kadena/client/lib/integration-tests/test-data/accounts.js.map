{"version": 3, "file": "accounts.js", "sourceRoot": "", "sources": ["../../../src/integration-tests/test-data/accounts.ts"], "names": [], "mappings": ";;;AAEA,kEAAkE;AACrD,QAAA,eAAe,GAA0B;IACpD,OAAO,EAAE,UAAU;IACnB,SAAS,EAAE,kEAAkE;IAC7E,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,kEAAkE;IACzE,2GAA2G;IAC3G,SAAS,EAAE,kEAAkE;CAC9E,CAAC;AAEW,QAAA,aAAa,GAA0B;IAClD,OAAO,EAAE,oEAAoE;IAC7E,SAAS,EAAE,kEAAkE;IAC7E,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,kEAAkE;IACzE,2GAA2G;IAC3G,SAAS,EAAE,kEAAkE;CAC9E,CAAC;AAEW,QAAA,aAAa,GAAa;IACrC,OAAO,EAAE,oEAAoE;IAC7E,SAAS,EAAE,kEAAkE;IAC7E,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,kEAAkE;CAC1E,CAAC", "sourcesContent": ["import type { IAccount, IAccountWithSecretKey } from '../support/interfaces';\n\n// the pre-funded account that will be used to fund other accounts\nexport const sender00Account: IAccountWithSecretKey = {\n  account: 'sender00',\n  publicKey: '368820f80c324bbc7c2b0610688a7da43e39f91d118732671cd9c7500ff43cca',\n  chainId: '0',\n  guard: '368820f80c324bbc7c2b0610688a7da43e39f91d118732671cd9c7500ff43cca',\n  // this is here only for testing purposes. in a real world scenario, the secret key should never be exposed\n  secretKey: '251a920c403ae8c8f65f59142316af3c82b631fba46ddea92ee8c95035bd2898',\n};\n\nexport const sourceAccount: IAccountWithSecretKey = {\n  account: 'k:5a2afbc4564b76b2c27ce5a644cab643c43663835ea0be22433b209d3351f937',\n  publicKey: '5a2afbc4564b76b2c27ce5a644cab643c43663835ea0be22433b209d3351f937',\n  chainId: '0',\n  guard: '5a2afbc4564b76b2c27ce5a644cab643c43663835ea0be22433b209d3351f937',\n  // this is here only for testing purposes. in a real world scenario, the secret key should never be exposed\n  secretKey: 'e97b30547784bf05eb71a765b1d45127ed89d9b3c0cf21b71a107efb170eed33',\n};\n\nexport const targetAccount: IAccount = {\n  account: 'k:5a2afbc4564b76b2c27ce5a644cab643c43663835ea0be22433b209d3351f937',\n  publicKey: '5a2afbc4564b76b2c27ce5a644cab643c43663835ea0be22433b209d3351f937',\n  chainId: '1',\n  guard: '5a2afbc4564b76b2c27ce5a644cab643c43663835ea0be22433b209d3351f937',\n};\n"]}