{"version": 3, "file": "createTransaction.js", "sourceRoot": "", "sources": ["../../src/utils/createTransaction.ts"], "names": [], "mappings": ";;;AAAA,mEAA+D;AAI/D;;;GAGG;AACI,MAAM,iBAAiB,GAEN,CAAC,WAAW,EAAE,EAAE;;IACtC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IACxC,MAAM,IAAI,GAAG,IAAA,yBAAS,EAAC,GAAG,CAAC,CAAC;IAC5B,OAAO;QACL,GAAG;QACH,IAAI;QACJ,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAA,MAAA,WAAW,CAAC,OAAO,0CAAE,MAAM,mCAAI,CAAC,CAAC,CAAC;KAC1D,CAAC;AACJ,CAAC,CAAC;AAVW,QAAA,iBAAiB,qBAU5B", "sourcesContent": ["import { hash as blakeHash } from '@kadena/cryptography-utils';\nimport type { IUnsignedCommand } from '@kadena/types';\nimport type { IPartialPactCommand } from '../interfaces/IPactCommand';\n\n/**\n * Prepare a transaction object. Creates an object with hash, cmd and sigs ({@link @kadena/types#IUnsignedCommand})\n * @public\n */\nexport const createTransaction: (\n  pactCommand: IPartialPactCommand,\n) => IUnsignedCommand = (pactCommand) => {\n  const cmd = JSON.stringify(pactCommand);\n  const hash = blakeHash(cmd);\n  return {\n    cmd,\n    hash,\n    sigs: Array.from(Array(pactCommand.signers?.length ?? 0)),\n  };\n};\n"]}