{"version": 3, "file": "getPactErrorCode.js", "sourceRoot": "", "sources": ["../../src/utils/getPactErrorCode.ts"], "names": [], "mappings": ";;;AAWA;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAgB,gBAAgB,CAC9B,KAAkD;IAElD,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,IACE,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EACxE,CAAC;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QACD,IACE,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CACrE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CACtB,EACD,CAAC;YACD,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,IACE,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CACpD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CACtB,EACD,CAAC;YACD,OAAO,uBAAuB,CAAC;QACjC,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC9C,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AA/BD,4CA+BC", "sourcesContent": ["/**\n * Pact error codes\n * @public\n */\nexport type PactErrorCode =\n  | 'RECORD_NOT_FOUND'\n  | 'DEFPACT_COMPLETED'\n  | 'CANNOT_RESOLVE_MODULE'\n  | 'EMPTY_CODE'\n  | 'ERROR';\n\n/**\n * Parses an error message to extract the Pact error code.\n *\n * This function is compatible with both Pact 4 and Pact 5 error formats.\n *\n * @param  error - The error returned by Pact.\n * @returns {@link PactErrorCode} - The extracted Pact error ('ERROR' if the error code could not be extracted).\n *\n * @example\n * ```ts\n * const client = createClient();\n * const response = await client.local(tx);\n * if (response.result.status === 'failure') {\n *   if (getPactErrorCode(response.result.error) === 'RECORD_NOT_FOUND') {\n *     // handle record not found error\n *   }\n * }\n * ```\n * @public\n */\nexport function getPactErrorCode(\n  error: { message: string | undefined } | undefined,\n): PactErrorCode {\n  const message = error ? error.message : '';\n  if (typeof message === 'string') {\n    if (\n      ['row not found', 'No value found'].some((str) => message.includes(str))\n    ) {\n      return 'RECORD_NOT_FOUND';\n    }\n    if (\n      ['pact completed', 'defpact execution already completed'].some((str) =>\n        message.includes(str),\n      )\n    ) {\n      return 'DEFPACT_COMPLETED';\n    }\n\n    if (\n      ['Cannot resolve', 'has no such member'].some((str) =>\n        message.includes(str),\n      )\n    ) {\n      return 'CANNOT_RESOLVE_MODULE';\n    }\n\n    if (message.includes('Failed reading: mzero')) {\n      return 'EMPTY_CODE';\n    }\n  }\n  return 'ERROR';\n}\n"]}