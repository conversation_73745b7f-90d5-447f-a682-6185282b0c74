{"version": 3, "file": "pact-helpers.js", "sourceRoot": "", "sources": ["../../src/utils/pact-helpers.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACI,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAA7D,QAAA,UAAU,cAAmD;AAE1E;;;GAGG;AACH,MAAa,OAAO;IAGlB,YAAmB,KAAa;QAC9B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,MAAM;QACX,OAAO,WAAW,IAAI,CAAC,MAAM,GAAG,CAAC;IACnC,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;CACF;AAlBD,0BAkBC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACI,MAAM,OAAO,GAAG,CAAC,KAAa,EAAW,EAAE;IAChD,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;AAFW,QAAA,OAAO,WAElB;AAEF,MAAM,YAAY,GAAW,yBAAyB,CAAC;AACvD;;;GAGG;AACH,SAAgB,cAAc,CAAC,KAAa;IAC1C,gFAAgF;IAChF,oHAAoH;IACpH,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;AAC/D,CAAC;AAJD,wCAIC", "sourcesContent": ["/**\n * Helper function that returns `(read-keyset \"key\")` Pact expression\n * @public\n */\nexport const readKeyset = (key: string) => () => `(read-keyset \"${key}\")`;\n\n/**\n * the class for adding values to the final pact object without adding quotes to strings\n * @public\n */\nexport class Literal {\n  private _value: string;\n\n  public constructor(value: string) {\n    this._value = value;\n  }\n\n  public getValue(): string {\n    return this._value;\n  }\n\n  public toJSON(): string {\n    return `Literal(${this._value})`;\n  }\n\n  public toString(): string {\n    return this.getValue();\n  }\n}\n\n/**\n * Will create a literal pact expression without surrounding quotes `\"`\n * @example\n * ```\n * // use literal as function input\n * Pact.module.[\"free.crowdfund\"][\"create-project\"](\n *   \"project_id\",\n *   \"project_name\",\n *   // this is a reference to another module and should not have quotes in the created expression\n *   literal(\"coin\"),\n *   ...\n * )\n *\n * // use literal as a property of a json in the input\n * Pact.module.[\"my-contract\"][\"set-details\"](\n *   \"name\",\n *   \"data\" : {\n *      age : 35,\n *      tokens : [literal(\"coin\"), literal(\"kdx\")]\n *   }\n * )\n * ```\n * @public\n */\nexport const literal = (value: string): Literal => {\n  return new Literal(value);\n};\n\nconst literalRegex: RegExp = /\"Literal\\(([^\\)]*)\\)\"/gi;\n/**\n * unpack all of the Literal(string) to string\n * @internal\n */\nexport function unpackLiterals(value: string): string {\n  // literal object is already unpacked if they are direct argument of a function.\n  // but if they are inside a json object, they are not unpacked since the toJSON method packs them as Literal(string)\n  return value.replace(literalRegex, (__, literal) => literal);\n}\n\n/**\n * General type for reference values\n * @public\n */\nexport type PactReference = Literal | (() => string);\n\n/**\n * @public\n */\nexport type PactReturnType<T extends (...args: any[]) => any> = T extends (\n  ...args: any[]\n) => infer R\n  ? R extends { returnType: infer RR }\n    ? RR\n    : any\n  : any;\n"]}