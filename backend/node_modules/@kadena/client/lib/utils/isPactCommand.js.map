{"version": 3, "file": "isPactCommand.js", "sourceRoot": "", "sources": ["../../src/utils/isPactCommand.ts"], "names": [], "mappings": ";;;AAEA;;;GAGG;AACI,MAAM,aAAa,GAAG,CAC3B,OAA8B,EACL,EAAE;IAC3B,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAEhD,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAClD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAC9C,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAC7C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IACrD,IAAI,OAAO,CAAC,IAAI,CAAC,YAAY,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAC1D,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IACtD,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IACtD,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,SAAS;QAAE,OAAO,KAAK,CAAC;IAEjD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAjBW,QAAA,aAAa,iBAiBxB", "sourcesContent": ["import type { IPactCommand } from '../interfaces/IPactCommand';\n\n/**\n * Typescript utility to verify that the passed object is a {@link IPactCommand}\n * @internal\n */\nexport const isPactCommand = (\n  command: Partial<IPactCommand>,\n): command is IPactCommand => {\n  if (command.payload === undefined) return false;\n\n  if (!('exec' in command.payload) && !('cont' in command.payload))\n    return false;\n  if (command.networkId === undefined) return false;\n  if (command.nonce === undefined) return false;\n  if (command.meta === undefined) return false;\n  if (command.meta.chainId === undefined) return false;\n  if (command.meta.creationTime === undefined) return false;\n  if (command.meta.gasLimit === undefined) return false;\n  if (command.meta.gasPrice === undefined) return false;\n  if (command.meta.ttl === undefined) return false;\n\n  return true;\n};\n"]}