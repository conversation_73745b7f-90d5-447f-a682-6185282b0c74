"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const pact_helpers_1 = require("../pact-helpers");
(0, vitest_1.describe)('readKeyset', () => {
    (0, vitest_1.it)('returns read-keyset string', () => {
        (0, vitest_1.expect)((0, pact_helpers_1.readKeyset)('ks')()).toBe('(read-keyset "ks")');
    });
});
(0, vitest_1.describe)('literal', () => {
    (0, vitest_1.it)('returns a function that returns the input', () => {
        (0, vitest_1.expect)((0, pact_helpers_1.literal)('free.contract').getValue()).toBe('free.contract');
    });
    (0, vitest_1.it)('returns a function that returns the input', () => {
        (0, vitest_1.expect)((0, pact_helpers_1.literal)('free.contract').toJSON()).toBe('Literal(free.contract)');
    });
    (0, vitest_1.it)('returns a function that returns the input', () => {
        (0, vitest_1.expect)((0, pact_helpers_1.literal)('free.contract').toString()).toBe('free.contract');
    });
});
(0, vitest_1.describe)('unpackLiterals', () => {
    (0, vitest_1.it)('returns a function that returns the input', () => {
        (0, vitest_1.expect)((0, pact_helpers_1.unpackLiterals)('["Literal(free.contract)", "Literal(coin)"]')).toBe('[free.contract, coin]');
    });
});
//# sourceMappingURL=pact-helpers.test.js.map