{"version": 3, "file": "createTransaction.test.js", "sourceRoot": "", "sources": ["../../../src/utils/tests/createTransaction.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAE9C,qCAAkC;AAClC,4DAAyD;AACzD,kDAA0C;AAE1C,MAAM,WAAW,GAAiB;IAChC,OAAO,EAAE;QACP,IAAI,EAAE;YACJ,IAAI,EAAE,oCAAoC;YAC1C,IAAI,EAAE,EAAE;SACT;KACF;IACD,OAAO,EAAE;QACP;YACE,MAAM,EAAE,gBAAgB;YACxB,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;SACxC;KACF;IACD,SAAS,EAAE,iBAAiB;IAC5B,KAAK,EAAE,YAAY;IACnB,IAAI,EAAE;QACJ,OAAO,EAAE,GAAG;QACZ,YAAY,EAAE,GAAG;QACjB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,aAAa;QACrB,GAAG,EAAE,IAAI;KACV;CACF,CAAC;AAEF,IAAA,iBAAQ,EAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAA,WAAE,EAAC,oDAAoD,EAAE,GAAG,EAAE;QAC5D,MAAM,WAAW,GAAG,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC;QAEnD,IAAA,eAAM,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAC3B,6CAA6C,CAC9C,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,MAAM,WAAW,GAAG,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC;QAEnD,IAAA,eAAM,EAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,mFAAmF,EAAE,GAAG,EAAE;QAC3F,MAAM,WAAW,GAAG,IAAA,qCAAiB,EAAC,WAAW,CAAC,CAAC;QAEnD,IAAA,eAAM,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACzC,IAAA,eAAM,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8FAA8F,EAAE,GAAG,EAAE;QACtG,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,WAAW,CAAC;QAE5C,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAA,qCAAiB,EAAC,OAAO,CAAC,CAAC;QAE/C,IAAA,eAAM,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;QACzD,MAAM,OAAO,GAAG,WAAI,CAAC,OAAO;aACzB,SAAS;QACR,8DAA8D;QAC7D,WAAI,CAAC,OAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CACpC,KAAK,EACL,OAAO,EACP,IAAA,sBAAO,EAAC,OAAO,CAAC,EAChB,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,IAAA,sBAAO,EAAC,MAAM,CAAC,EAAE,IAAA,sBAAO,EAAC,WAAW,CAAC,CAAC,EAAE,EAAE,EAC9D,EAAE,OAAO,EAAE,MAAM,EAAE,CACpB,CACF;aACA,QAAQ,CAAC,SAAS,CAAC;aACnB,iBAAiB,EAAE,CAAC;QACvB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACtB,GAAG,EAAE,2KAA2K;YAChL,IAAI,EAAE,6CAA6C;YACnD,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\nimport { Pact } from '../../pact';\nimport { createTransaction } from '../createTransaction';\nimport { literal } from '../pact-helpers';\n\nconst pactCommand: IPactCommand = {\n  payload: {\n    exec: {\n      code: '(coin.transfer \"alice\" \"bob\" 12.1)',\n      data: {},\n    },\n  },\n  signers: [\n    {\n      pubKey: 'bob_public_key',\n      clist: [{ args: [], name: 'coin.GAS' }],\n    },\n  ],\n  networkId: 'test-network-id',\n  nonce: 'test-nonce',\n  meta: {\n    chainId: '1',\n    creationTime: 123,\n    gasLimit: 400,\n    gasPrice: 381,\n    sender: 'gas-station',\n    ttl: 1000,\n  },\n};\n\ndescribe('createTransaction', () => {\n  it('returns a transaction object with the correct hash', () => {\n    const transaction = createTransaction(pactCommand);\n\n    expect(transaction.hash).toBe(\n      'tMvXzZPbK_Rd93C0ZwtNKzHpGaUhiEj3uaf-RSw29HU',\n    );\n  });\n\n  it('returns a transaction object with the correct cmd', () => {\n    const transaction = createTransaction(pactCommand);\n\n    expect(transaction.cmd).toBe(JSON.stringify(pactCommand));\n  });\n\n  it('returns a transaction object with the sigs array the same length as signers array', () => {\n    const transaction = createTransaction(pactCommand);\n\n    expect(transaction.sigs).toHaveLength(1);\n    expect(transaction.sigs).toStrictEqual([undefined]);\n  });\n\n  it('returns a transaction object with empty sigs array if there is no signers in the pactCommand', () => {\n    const { signers, ...command } = pactCommand;\n\n    expect(signers).toBeDefined();\n\n    const transaction = createTransaction(command);\n\n    expect(transaction.sigs).toHaveLength(0);\n  });\n  it('adds Literal values without quote to the output', () => {\n    const command = Pact.builder\n      .execution(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (Pact.modules as any).test['test-fun'](\n          'bob',\n          'alice',\n          literal('guard'),\n          { test: { modules: [literal('coin'), literal('free.coin')] } },\n          { decimal: '12.0' },\n        ),\n      )\n      .setNonce('nonce:1')\n      .createTransaction();\n    expect(command).toEqual({\n      cmd: '{\"payload\":{\"exec\":{\"code\":\"(test.test-fun \\\\\"bob\\\\\" \\\\\"alice\\\\\" guard {\\\\\"test\\\\\": {\\\\\"modules\\\\\": [coin free.coin]}} 12.0)\",\"data\":{}}},\"nonce\":\"nonce:1\",\"signers\":[]}',\n      hash: 'x_3osDNKNpusk2LScpEUwkm8wo0h_G7OXL03rQMYnUE',\n      sigs: [],\n    });\n  });\n});\n"]}