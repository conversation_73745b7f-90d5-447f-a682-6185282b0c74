"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const parseAsPactValue_1 = require("../parseAsPactValue");
(0, vitest_1.describe)('parseType', () => {
    (0, vitest_1.it)('parses a decimal number', () => {
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)({ decimal: '10.1' })).toEqual('10.1');
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)({ decimal: '10' })).toEqual('10.0');
    });
    (0, vitest_1.it)('parses a int number', () => {
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)({ int: '10' })).toEqual('10');
    });
    (0, vitest_1.it)('parses a Date object', () => {
        vitest_1.vi.useFakeTimers();
        const start = new Date('2023-07-20T14:55:11.139Z');
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)(start)).toEqual(`(time "2023-07-20T14:55:11Z")`);
        vitest_1.vi.useRealTimers();
    });
    (0, vitest_1.it)('throws exception if number is not integer', () => {
        (0, vitest_1.expect)(() => (0, parseAsPactValue_1.parseAsPactValue)({ int: '10.1' })).toThrowError(new Error('PactNumber is not an integer'));
    });
    (0, vitest_1.it)('throws exception if value is NaN', () => {
        (0, vitest_1.expect)(() => (0, parseAsPactValue_1.parseAsPactValue)({ decimal: 'test' })).toThrowError(new Error('Value is NaN'));
    });
    (0, vitest_1.it)('throws exception if value is a number', () => {
        (0, vitest_1.expect)(() => (0, parseAsPactValue_1.parseAsPactValue)(10)).toThrowError(new Error('Type `number` is not allowed in the command. Use `{ decimal: "10.0" }` or `{ int: "10" }` instead'));
    });
    (0, vitest_1.it)('parses a string', () => {
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)('test')).toEqual('"test"');
    });
    (0, vitest_1.it)('call arg, if its a function', () => {
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)(() => 'test')).toEqual('test');
    });
    (0, vitest_1.it)('parse object', () => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)({ test: { decimal: '2' } })).toEqual('{"test": 2.0}');
    });
    (0, vitest_1.it)('return "true" or "false", if its a boolean', () => {
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)(true)).toEqual('true');
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)(false)).toEqual('false');
    });
    (0, vitest_1.it)("returns input, if it doesn't match with any conditions", () => {
        const symbol = Symbol('test');
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)(symbol)).toEqual(symbol);
    });
    // it('parses an nested object with nested pact values', () => {});
    (0, vitest_1.it)('parses an array with nested pact values', () => {
        const start = new Date('2023-07-20T14:55:11.139Z');
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)([start, start, { decimal: '1' }, { int: '1' }])).toEqual('[(time "2023-07-20T14:55:11Z") (time "2023-07-20T14:55:11Z") 1.0 1]');
    });
    (0, vitest_1.it)('parses an object with nested pact values', () => {
        const start = new Date('2023-07-20T14:55:11.139Z');
        (0, vitest_1.expect)((0, parseAsPactValue_1.parseAsPactValue)({
            object: {
                start,
                end: start,
                test: { x: { decimal: '1' }, y: { int: '1' } },
            },
            simpleArray: [{ decimal: '1' }, { int: '1' }],
            arrayOfObject: [
                {
                    time: {
                        start,
                        end: start,
                        test: { x: { decimal: '1' }, y: { int: '1' } },
                    },
                },
            ],
        })).toEqual('{"object": {"start": (time "2023-07-20T14:55:11Z"), "end": (time "2023-07-20T14:55:11Z"), "test": {"x": 1.0, "y": 1}}, "simpleArray": [1.0 1], "arrayOfObject": [{"time": {"start": (time "2023-07-20T14:55:11Z"), "end": (time "2023-07-20T14:55:11Z"), "test": {"x": 1.0, "y": 1}}}]}');
    });
});
//# sourceMappingURL=parseAsPactValue.test.js.map