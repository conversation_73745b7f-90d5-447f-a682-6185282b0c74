"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const isPactCommand_1 = require("../isPactCommand");
(0, vitest_1.describe)('isCommand', () => {
    (0, vitest_1.it)('returns false if the object is not a command', () => {
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)({})).toBe(false);
    });
    (0, vitest_1.it)('returns true if the object is a command', () => {
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)({
            payload: {
                exec: {
                    code: '(coin.transfer "alice" "bob" 12.1)',
                    data: {},
                },
            },
            signers: [
                {
                    pubKey: 'bob_public_key',
                },
            ],
            networkId: 'test-network-id',
            nonce: 'test-nonce',
            meta: {
                chainId: '0',
                creationTime: 123,
                gasLimit: 400,
                gasPrice: 381,
                sender: 'gas-station',
                ttl: 1000,
            },
        })).toBe(true);
    });
    (0, vitest_1.it)("returns false if properties of the command object don't match ICommand interface", () => {
        const deleteProperty = (obj, prop) => {
            const parts = prop.split('.');
            if (parts.length > 1) {
                const [first, ...rest] = parts;
                return {
                    ...obj,
                    [first]: deleteProperty(obj[first], rest.join('.')),
                };
            }
            const newObj = { ...obj };
            delete newObj[prop];
            return newObj;
        };
        const command = {
            payload: {
                exec: {
                    code: '(coin.transfer "alice" "bob" 12.1)',
                    data: {},
                },
            },
            signers: [
                {
                    pubKey: 'bob_public_key',
                },
            ],
            networkId: 'test-network-id',
            nonce: 'test-nonce',
            meta: {
                chainId: '0',
                creationTime: 123,
                gasLimit: 400,
                gasPrice: 381,
                sender: 'gas-station',
                ttl: 1000,
            },
        };
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'payload'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'payload.exec'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'networkId'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'nonce'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'meta'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'meta.chainId'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'meta.creationTime'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'meta.gasLimit'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'meta.gasPrice'))).toBe(false);
        (0, vitest_1.expect)((0, isPactCommand_1.isPactCommand)(deleteProperty(command, 'meta.ttl'))).toBe(false);
    });
});
//# sourceMappingURL=isPactCommand.test.js.map