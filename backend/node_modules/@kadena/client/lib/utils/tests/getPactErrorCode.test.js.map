{"version": 3, "file": "getPactErrorCode.test.js", "sourceRoot": "", "sources": ["../../../src/utils/tests/getPactErrorCode.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,0DAAuD;AAEvD,IAAA,iBAAQ,EAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAA,WAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxD,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAA,WAAE,EAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,IAAA,eAAM,EACJ,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC,CACtE,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,CAC1D,mBAAmB,CACpB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,CAC1D,uBAAuB,CACxB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC,CAAC,IAAI,CACjE,YAAY,CACb,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,iBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAA,WAAE,EAAC,6FAA6F,EAAE,GAAG,EAAE;YACrG,IAAA,eAAM,EACJ,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC,CACtE,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,wEAAwE,EAAE,GAAG,EAAE;YAChF,IAAA,eAAM,EACJ,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC,CACrE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAA,WAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC,IAAI,CAC9D,uBAAuB,CACxB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { getPactErrorCode } from '../getPactErrorCode';\n\ndescribe('getPactErrorCode', () => {\n  it('returns \"ERROR\" if input undefined', () => {\n    expect(getPactErrorCode(undefined)).toBe('ERROR');\n  });\n\n  it('returns \"ERROR\" if message is falsy undefined', () => {\n    expect(getPactErrorCode({ message: '' })).toBe('ERROR');\n    expect(getPactErrorCode({ message: undefined })).toBe('ERROR');\n  });\n\n  describe('Pact 4 error messages', () => {\n    it('returns \"RECORD_NOT_FOUND\" for \"with-read: row not found: ...\" ', () => {\n      expect(\n        getPactErrorCode({ message: 'with-read: row not found: test-inout' }),\n      ).toBe('RECORD_NOT_FOUND');\n    });\n\n    it('returns \"DEFPACT_COMPLETED\" for \"pact completed\" ', () => {\n      expect(getPactErrorCode({ message: 'pact completed' })).toBe(\n        'DEFPACT_COMPLETED',\n      );\n    });\n\n    it('returns \"CANNOT_RESOLVE_MODULE\" for \"Cannot resolve\" ', () => {\n      expect(getPactErrorCode({ message: 'Cannot resolve' })).toBe(\n        'CANNOT_RESOLVE_MODULE',\n      );\n    });\n\n    it('returns \"EMPTY_CODE\" for \"Failed reading: mzero\" ', () => {\n      expect(getPactErrorCode({ message: 'Failed reading: mzero' })).toBe(\n        'EMPTY_CODE',\n      );\n    });\n  });\n\n  describe('Pact 5 error messages', () => {\n    it('returns \"RECORD_NOT_FOUND\" for \"No value found in table coin_coin-table for key: test-key\" ', () => {\n      expect(\n        getPactErrorCode({ message: 'with-read: row not found: test-inout' }),\n      ).toBe('RECORD_NOT_FOUND');\n    });\n\n    it('returns \"DEFPACT_COMPLETED\" for \"defpact execution already completed\" ', () => {\n      expect(\n        getPactErrorCode({ message: 'defpact execution already completed' }),\n      ).toBe('DEFPACT_COMPLETED');\n    });\n\n    it('returns \"CANNOT_RESOLVE_MODULE\" for \"has no such member\" ', () => {\n      expect(getPactErrorCode({ message: 'has no such member' })).toBe(\n        'CANNOT_RESOLVE_MODULE',\n      );\n    });\n  });\n});\n"]}