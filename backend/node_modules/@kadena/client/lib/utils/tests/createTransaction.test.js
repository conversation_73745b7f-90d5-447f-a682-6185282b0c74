"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const pact_1 = require("../../pact");
const createTransaction_1 = require("../createTransaction");
const pact_helpers_1 = require("../pact-helpers");
const pactCommand = {
    payload: {
        exec: {
            code: '(coin.transfer "alice" "bob" 12.1)',
            data: {},
        },
    },
    signers: [
        {
            pubKey: 'bob_public_key',
            clist: [{ args: [], name: 'coin.GAS' }],
        },
    ],
    networkId: 'test-network-id',
    nonce: 'test-nonce',
    meta: {
        chainId: '1',
        creationTime: 123,
        gasLimit: 400,
        gasPrice: 381,
        sender: 'gas-station',
        ttl: 1000,
    },
};
(0, vitest_1.describe)('createTransaction', () => {
    (0, vitest_1.it)('returns a transaction object with the correct hash', () => {
        const transaction = (0, createTransaction_1.createTransaction)(pactCommand);
        (0, vitest_1.expect)(transaction.hash).toBe('tMvXzZPbK_Rd93C0ZwtNKzHpGaUhiEj3uaf-RSw29HU');
    });
    (0, vitest_1.it)('returns a transaction object with the correct cmd', () => {
        const transaction = (0, createTransaction_1.createTransaction)(pactCommand);
        (0, vitest_1.expect)(transaction.cmd).toBe(JSON.stringify(pactCommand));
    });
    (0, vitest_1.it)('returns a transaction object with the sigs array the same length as signers array', () => {
        const transaction = (0, createTransaction_1.createTransaction)(pactCommand);
        (0, vitest_1.expect)(transaction.sigs).toHaveLength(1);
        (0, vitest_1.expect)(transaction.sigs).toStrictEqual([undefined]);
    });
    (0, vitest_1.it)('returns a transaction object with empty sigs array if there is no signers in the pactCommand', () => {
        const { signers, ...command } = pactCommand;
        (0, vitest_1.expect)(signers).toBeDefined();
        const transaction = (0, createTransaction_1.createTransaction)(command);
        (0, vitest_1.expect)(transaction.sigs).toHaveLength(0);
    });
    (0, vitest_1.it)('adds Literal values without quote to the output', () => {
        const command = pact_1.Pact.builder
            .execution(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        pact_1.Pact.modules.test['test-fun']('bob', 'alice', (0, pact_helpers_1.literal)('guard'), { test: { modules: [(0, pact_helpers_1.literal)('coin'), (0, pact_helpers_1.literal)('free.coin')] } }, { decimal: '12.0' }))
            .setNonce('nonce:1')
            .createTransaction();
        (0, vitest_1.expect)(command).toEqual({
            cmd: '{"payload":{"exec":{"code":"(test.test-fun \\"bob\\" \\"alice\\" guard {\\"test\\": {\\"modules\\": [coin free.coin]}} 12.0)","data":{}}},"nonce":"nonce:1","signers":[]}',
            hash: 'x_3osDNKNpusk2LScpEUwkm8wo0h_G7OXL03rQMYnUE',
            sigs: [],
        });
    });
});
//# sourceMappingURL=createTransaction.test.js.map