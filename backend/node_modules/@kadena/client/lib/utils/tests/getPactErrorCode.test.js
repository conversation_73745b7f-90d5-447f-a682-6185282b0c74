"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const getPactErrorCode_1 = require("../getPactErrorCode");
(0, vitest_1.describe)('getPactErrorCode', () => {
    (0, vitest_1.it)('returns "ERROR" if input undefined', () => {
        (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)(undefined)).toBe('ERROR');
    });
    (0, vitest_1.it)('returns "ERROR" if message is falsy undefined', () => {
        (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: '' })).toBe('ERROR');
        (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: undefined })).toBe('ERROR');
    });
    (0, vitest_1.describe)('Pact 4 error messages', () => {
        (0, vitest_1.it)('returns "RECORD_NOT_FOUND" for "with-read: row not found: ..." ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'with-read: row not found: test-inout' })).toBe('RECORD_NOT_FOUND');
        });
        (0, vitest_1.it)('returns "DEFPACT_COMPLETED" for "pact completed" ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'pact completed' })).toBe('DEFPACT_COMPLETED');
        });
        (0, vitest_1.it)('returns "CANNOT_RESOLVE_MODULE" for "Cannot resolve" ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'Cannot resolve' })).toBe('CANNOT_RESOLVE_MODULE');
        });
        (0, vitest_1.it)('returns "EMPTY_CODE" for "Failed reading: mzero" ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'Failed reading: mzero' })).toBe('EMPTY_CODE');
        });
    });
    (0, vitest_1.describe)('Pact 5 error messages', () => {
        (0, vitest_1.it)('returns "RECORD_NOT_FOUND" for "No value found in table coin_coin-table for key: test-key" ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'with-read: row not found: test-inout' })).toBe('RECORD_NOT_FOUND');
        });
        (0, vitest_1.it)('returns "DEFPACT_COMPLETED" for "defpact execution already completed" ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'defpact execution already completed' })).toBe('DEFPACT_COMPLETED');
        });
        (0, vitest_1.it)('returns "CANNOT_RESOLVE_MODULE" for "has no such member" ', () => {
            (0, vitest_1.expect)((0, getPactErrorCode_1.getPactErrorCode)({ message: 'has no such member' })).toBe('CANNOT_RESOLVE_MODULE');
        });
    });
});
//# sourceMappingURL=getPactErrorCode.test.js.map