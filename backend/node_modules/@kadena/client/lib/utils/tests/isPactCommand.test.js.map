{"version": 3, "file": "isPactCommand.test.js", "sourceRoot": "", "sources": ["../../../src/utils/tests/isPactCommand.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAE9C,oDAAiD;AAEjD,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,WAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;QACtD,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,IAAA,eAAM,EACJ,IAAA,6BAAa,EAAC;YACZ,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE;iBACT;aACF;YACD,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,gBAAgB;iBACzB;aACF;YACD,SAAS,EAAE,iBAAiB;YAC5B,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,YAAY,EAAE,GAAG;gBACjB,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE,aAAa;gBACrB,GAAG,EAAE,IAAI;aACV;SACF,CAAC,CACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kFAAkF,EAAE,GAAG,EAAE;QAC1F,MAAM,cAAc,GAAG,CACrB,GAAM,EACN,IAAY,EACA,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;gBAC/B,OAAO;oBACL,GAAG,GAAG;oBACN,CAAC,KAAK,CAAC,EAAE,cAAc,CACpB,GAA8B,CAAC,KAAK,CAAC,EACtC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CACf;iBACF,CAAC;YACJ,CAAC;YACD,MAAM,MAAM,GAAG,EAAE,GAAG,GAAG,EAA4B,CAAC;YACpD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO,MAA+B,CAAC;QACzC,CAAC,CAAC;QACF,MAAM,OAAO,GAAiB;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE;iBACT;aACF;YACD,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,gBAAgB;iBACzB;aACF;YACD,SAAS,EAAE,iBAAiB;YAC5B,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,YAAY,EAAE,GAAG;gBACjB,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE,aAAa;gBACrB,GAAG,EAAE,IAAI;aACV;SACF,CAAC;QAEF,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3E,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpE,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3E,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CACtE,KAAK,CACN,CAAC;QACF,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAA,eAAM,EAAC,IAAA,6BAAa,EAAC,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\nimport { isPactCommand } from '../isPactCommand';\n\ndescribe('isCommand', () => {\n  it('returns false if the object is not a command', () => {\n    expect(isPactCommand({})).toBe(false);\n  });\n\n  it('returns true if the object is a command', () => {\n    expect(\n      isPactCommand({\n        payload: {\n          exec: {\n            code: '(coin.transfer \"alice\" \"bob\" 12.1)',\n            data: {},\n          },\n        },\n        signers: [\n          {\n            pubKey: 'bob_public_key',\n          },\n        ],\n        networkId: 'test-network-id',\n        nonce: 'test-nonce',\n        meta: {\n          chainId: '0',\n          creationTime: 123,\n          gasLimit: 400,\n          gasPrice: 381,\n          sender: 'gas-station',\n          ttl: 1000,\n        },\n      }),\n    ).toBe(true);\n  });\n\n  it(\"returns false if properties of the command object don't match ICommand interface\", () => {\n    const deleteProperty = <T extends object>(\n      obj: T,\n      prop: string,\n    ): Partial<T> => {\n      const parts = prop.split('.');\n      if (parts.length > 1) {\n        const [first, ...rest] = parts;\n        return {\n          ...obj,\n          [first]: deleteProperty(\n            (obj as Record<string, object>)[first],\n            rest.join('.'),\n          ),\n        };\n      }\n      const newObj = { ...obj } as Record<string, object>;\n      delete newObj[prop];\n      return newObj as unknown as Partial<T>;\n    };\n    const command: IPactCommand = {\n      payload: {\n        exec: {\n          code: '(coin.transfer \"alice\" \"bob\" 12.1)',\n          data: {},\n        },\n      },\n      signers: [\n        {\n          pubKey: 'bob_public_key',\n        },\n      ],\n      networkId: 'test-network-id',\n      nonce: 'test-nonce',\n      meta: {\n        chainId: '0',\n        creationTime: 123,\n        gasLimit: 400,\n        gasPrice: 381,\n        sender: 'gas-station',\n        ttl: 1000,\n      },\n    };\n\n    expect(isPactCommand(deleteProperty(command, 'payload'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'payload.exec'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'networkId'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'nonce'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'meta'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'meta.chainId'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'meta.creationTime'))).toBe(\n      false,\n    );\n    expect(isPactCommand(deleteProperty(command, 'meta.gasLimit'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'meta.gasPrice'))).toBe(false);\n    expect(isPactCommand(deleteProperty(command, 'meta.ttl'))).toBe(false);\n  });\n});\n"]}