{"version": 3, "file": "parseAsPactValue.test.js", "sourceRoot": "", "sources": ["../../../src/utils/tests/parseAsPactValue.test.ts"], "names": [], "mappings": ";;AAAA,mCAAkD;AAClD,0DAAuD;AAEvD,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,WAAE,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACjC,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,qBAAqB,EAAE,GAAG,EAAE;QAC7B,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sBAAsB,EAAE,GAAG,EAAE;QAC9B,WAAE,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnD,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QACzE,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,IAAA,eAAM,EAAC,GAAG,EAAE,CAAC,IAAA,mCAAgB,EAAC,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,YAAY,CAC1D,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAC1C,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;QAC1C,IAAA,eAAM,EAAC,GAAG,EAAE,CAAC,IAAA,mCAAgB,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,YAAY,CAC9D,IAAI,KAAK,CAAC,cAAc,CAAC,CAC1B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;QAC/C,IAAA,eAAM,EAAC,GAAG,EAAE,CAAC,IAAA,mCAAgB,EAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAC7C,IAAI,KAAK,CACP,mGAAmG,CACpG,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,iBAAiB,EAAE,GAAG,EAAE;QACzB,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,cAAc,EAAE,GAAG,EAAE;QACtB,8DAA8D;QAC9D,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAS,CAAC,CAAC,CAAC,OAAO,CACjE,eAAe,CAChB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAA,eAAM,EAAC,IAAA,mCAAgB,EAAC,MAAe,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,mEAAmE;IACnE,IAAA,WAAE,EAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnD,IAAA,eAAM,EACJ,IAAA,mCAAgB,EAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CACjE,CAAC,OAAO,CACP,qEAAqE,CACtE,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnD,IAAA,eAAM,EACJ,IAAA,mCAAgB,EAAC;YACf,MAAM,EAAE;gBACN,KAAK;gBACL,GAAG,EAAE,KAAK;gBACV,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;aAC/C;YACD,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YAC7C,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE;wBACJ,KAAK;wBACL,GAAG,EAAE,KAAK;wBACV,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;qBAC/C;iBACF;aACF;SACF,CAAC,CACH,CAAC,OAAO,CACP,yRAAyR,CAC1R,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it, vi } from 'vitest';\nimport { parseAsPactValue } from '../parseAsPactValue';\n\ndescribe('parseType', () => {\n  it('parses a decimal number', () => {\n    expect(parseAsPactValue({ decimal: '10.1' })).toEqual('10.1');\n    expect(parseAsPactValue({ decimal: '10' })).toEqual('10.0');\n  });\n  it('parses a int number', () => {\n    expect(parseAsPactValue({ int: '10' })).toEqual('10');\n  });\n\n  it('parses a Date object', () => {\n    vi.useFakeTimers();\n    const start = new Date('2023-07-20T14:55:11.139Z');\n    expect(parseAsPactValue(start)).toEqual(`(time \"2023-07-20T14:55:11Z\")`);\n    vi.useRealTimers();\n  });\n\n  it('throws exception if number is not integer', () => {\n    expect(() => parseAsPactValue({ int: '10.1' })).toThrowError(\n      new Error('PactNumber is not an integer'),\n    );\n  });\n\n  it('throws exception if value is NaN', () => {\n    expect(() => parseAsPactValue({ decimal: 'test' })).toThrowError(\n      new Error('Value is NaN'),\n    );\n  });\n\n  it('throws exception if value is a number', () => {\n    expect(() => parseAsPactValue(10)).toThrowError(\n      new Error(\n        'Type `number` is not allowed in the command. Use `{ decimal: \"10.0\" }` or `{ int: \"10\" }` instead',\n      ),\n    );\n  });\n\n  it('parses a string', () => {\n    expect(parseAsPactValue('test')).toEqual('\"test\"');\n  });\n\n  it('call arg, if its a function', () => {\n    expect(parseAsPactValue(() => 'test')).toEqual('test');\n  });\n\n  it('parse object', () => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    expect(parseAsPactValue({ test: { decimal: '2' } } as any)).toEqual(\n      '{\"test\": 2.0}',\n    );\n  });\n\n  it('return \"true\" or \"false\", if its a boolean', () => {\n    expect(parseAsPactValue(true)).toEqual('true');\n    expect(parseAsPactValue(false)).toEqual('false');\n  });\n\n  it(\"returns input, if it doesn't match with any conditions\", () => {\n    const symbol = Symbol('test');\n    expect(parseAsPactValue(symbol as never)).toEqual(symbol);\n  });\n\n  // it('parses an nested object with nested pact values', () => {});\n  it('parses an array with nested pact values', () => {\n    const start = new Date('2023-07-20T14:55:11.139Z');\n    expect(\n      parseAsPactValue([start, start, { decimal: '1' }, { int: '1' }]),\n    ).toEqual(\n      '[(time \"2023-07-20T14:55:11Z\") (time \"2023-07-20T14:55:11Z\") 1.0 1]',\n    );\n  });\n\n  it('parses an object with nested pact values', () => {\n    const start = new Date('2023-07-20T14:55:11.139Z');\n    expect(\n      parseAsPactValue({\n        object: {\n          start,\n          end: start,\n          test: { x: { decimal: '1' }, y: { int: '1' } },\n        },\n        simpleArray: [{ decimal: '1' }, { int: '1' }],\n        arrayOfObject: [\n          {\n            time: {\n              start,\n              end: start,\n              test: { x: { decimal: '1' }, y: { int: '1' } },\n            },\n          },\n        ],\n      }),\n    ).toEqual(\n      '{\"object\": {\"start\": (time \"2023-07-20T14:55:11Z\"), \"end\": (time \"2023-07-20T14:55:11Z\"), \"test\": {\"x\": 1.0, \"y\": 1}}, \"simpleArray\": [1.0 1], \"arrayOfObject\": [{\"time\": {\"start\": (time \"2023-07-20T14:55:11Z\"), \"end\": (time \"2023-07-20T14:55:11Z\"), \"test\": {\"x\": 1.0, \"y\": 1}}}]}',\n    );\n  });\n});\n"]}