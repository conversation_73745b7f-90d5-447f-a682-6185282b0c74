{"version": 3, "file": "parseAsPactValue.js", "sourceRoot": "", "sources": ["../../src/utils/parseAsPactValue.ts"], "names": [], "mappings": ";;;AAAA,2CAA4C;AAE5C,iDAAyC;AAEzC,MAAM,MAAM,GAAG,CAAC,GAAY,EAAe,EAAE;IAC3C,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,IAAI;QAAE,OAAO,IAAI,CAAC;IAChE,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,KAA2C;IAE3C,IAAI,KAAK,YAAY,sBAAO,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC1B,CAAC;IACD,QAAQ,OAAO,KAAK,EAAE,CAAC;QACrB,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;gBACvB,OAAO,IAAI,mBAAU,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,SAAS,EAAE,CAAC;YAC7D,CAAC;YACD,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;gBACnB,OAAO,IAAI,mBAAU,CAAC,KAAK,CAAC,GAAa,CAAC,CAAC,SAAS,EAAE,CAAC;YACzD,CAAC;YACD,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClB,MAAM,OAAO,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBACxD,OAAO,UAAU,OAAO,IAAI,CAAC;YAC/B,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACtD,CAAC;YAED,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;iBAC7B,GAAG,CACF,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAkB,CAAC,EAAE,CACtE;iBACA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACnB,CAAC;QACD,KAAK,QAAQ;YACX,MAAM,IAAI,KAAK,CACb,mGAAmG,CACpG,CAAC;QACJ,KAAK,QAAQ;YACX,OAAO,IAAI,KAAK,GAAG,CAAC;QACtB,KAAK,UAAU;YACb,OAAO,KAAK,EAAE,CAAC;QACjB,KAAK,SAAS;YACZ,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC1B;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAzCD,4CAyCC", "sourcesContent": ["import { PactNumber } from '@kadena/pactjs';\nimport type { PactValue } from '@kadena/types';\nimport { Literal } from './pact-helpers';\n\nconst isDate = (obj: unknown): obj is Date => {\n  if (typeof obj === 'object' && obj instanceof Date) return true;\n  return false;\n};\n\n/**\n * @public\n */\nexport function parseAsPactValue(\n  input: PactValue | (() => string) | Literal,\n): string {\n  if (input instanceof Literal) {\n    return input.getValue();\n  }\n  switch (typeof input) {\n    case 'object': {\n      if ('decimal' in input) {\n        return new PactNumber(input.decimal as string).toDecimal();\n      }\n      if ('int' in input) {\n        return new PactNumber(input.int as string).toInteger();\n      }\n      if (isDate(input)) {\n        const isoTime = `${input.toISOString().split('.')[0]}Z`;\n        return `(time \"${isoTime}\")`;\n      }\n      if (Array.isArray(input)) {\n        return `[${input.map(parseAsPactValue).join(' ')}]`;\n      }\n\n      return `{${Object.entries(input)\n        .map(\n          ([key, value]) => `\"${key}\": ${parseAsPactValue(value as PactValue)}`,\n        )\n        .join(', ')}}`;\n    }\n    case 'number':\n      throw new Error(\n        'Type `number` is not allowed in the command. Use `{ decimal: \"10.0\" }` or `{ int: \"10\" }` instead',\n      );\n    case 'string':\n      return `\"${input}\"`;\n    case 'function':\n      return input();\n    case 'boolean':\n      return input.toString();\n    default:\n      return input;\n  }\n}\n"]}