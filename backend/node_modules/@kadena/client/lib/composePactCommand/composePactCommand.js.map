{"version": 3, "file": "composePactCommand.js", "sourceRoot": "", "sources": ["../../src/composePactCommand/composePactCommand.ts"], "names": [], "mappings": ";;;AACA,uDAAoD;AAgCpD,MAAM,eAAe,GAAG,CACtB,OAAqC,EACP,EAAE;;;IAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC5B,MAAM,YAAY,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;IACpC,MAAA,YAAY,CAAC,KAAK,oCAAlB,YAAY,CAAC,KAAK,GAAK,aAAa,QAAQ,EAAE,EAAC;IAC/C,MAAA,YAAY,CAAC,OAAO,oCAApB,YAAY,CAAC,OAAO,GAAK,EAAE,EAAC;IAC5B,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,MAAM;YAChB,MAAM,EAAE,EAAE;YACV,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;SAC5C,CAAC;QACF,YAAY,CAAC,IAAI,GAAG;YAClB,GAAG,WAAW;YACd,GAAG,YAAY,CAAC,IAAI;SACrB,CAAC;IACJ,CAAC;IACD,IAAI,YAAY,CAAC,OAAO,IAAI,MAAM,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3D,YAAA,YAAY,CAAC,OAAO,CAAC,IAAI,EAAC,KAAK,uCAAL,KAAK,GAAK,IAAI,EAAC;IAC3C,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,kBAAkB,GAC7B,CACE,KAAmD,EACnD,GAAG,IAAyD,EACJ,EAAE,CAC5D,CAAC,UAAwB,EAAE,EAAE,EAAE;IAC7B,MAAM,IAAI,GAGN,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IACrB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CACzB,CAAC,GAAG,EAAE,IAAa,EAAE,EAAE;QACrB,OAAO,OAAO,IAAI,KAAK,UAAU;YAC/B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAA,2BAAY,EAAC,GAAG,EAAE,IAAoC,CAAC,CAAC;IAC9D,CAAC,EACD,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,OAAO,CACpD,CAAC;IACF,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;IAC9C,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AApBS,QAAA,kBAAkB,sBAoB3B", "sourcesContent": ["import type { IPartialPactCommand } from '../interfaces/IPactCommand';\nimport { patchCommand } from './utils/patchCommand';\n\ntype NoPayload<TCommand> = TCommand extends { payload: unknown }\n  ? never\n  : TCommand;\n\ntype DataOrFunction<TData> = TData | ((a: TData) => TData);\n\ntype InitialInput =\n  | Partial<IPartialPactCommand>\n  | (() => Partial<IPartialPactCommand>);\n\n// TODO : improve the return value to merge all of the inputs as an object\ninterface IComposePactCommand {\n  <TPayload extends Pick<IPartialPactCommand, 'payload'>>(\n    payload: TPayload,\n    ...rest: [\n      ...Array<\n        | Partial<IPartialPactCommand>\n        | ((\n            payload: TPayload & Partial<IPartialPactCommand>,\n          ) => Partial<IPartialPactCommand>)\n      >,\n    ]\n  ): (cmd?: InitialInput) => Partial<IPartialPactCommand>;\n\n  (\n    first: DataOrFunction<NoPayload<Partial<IPartialPactCommand>>>,\n    ...rest: Array<DataOrFunction<Partial<IPartialPactCommand>>>\n  ): (cmd?: InitialInput) => Partial<IPartialPactCommand>;\n}\n\nconst finalizeCommand = (\n  command: Partial<IPartialPactCommand>,\n): Partial<IPartialPactCommand> => {\n  const dateInMs = Date.now();\n  const finalCommand = { ...command };\n  finalCommand.nonce ??= `kjs:nonce:${dateInMs}`;\n  finalCommand.signers ??= [];\n  if (finalCommand.meta) {\n    const defaultMeta = {\n      gasLimit: 2500,\n      gasPrice: 1.0e-8,\n      sender: '',\n      ttl: 8 * 60 * 60, // 8 hours,\n      creationTime: Math.floor(Date.now() / 1000),\n    };\n    finalCommand.meta = {\n      ...defaultMeta,\n      ...finalCommand.meta,\n    };\n  }\n  if (finalCommand.payload && 'cont' in finalCommand.payload) {\n    finalCommand.payload.cont.proof ??= null;\n  }\n  return finalCommand;\n};\n\n/**\n * Composer for PactCommand to use with reducers\n * @public\n */\nexport const composePactCommand: IComposePactCommand =\n  (\n    first: DataOrFunction<Partial<IPartialPactCommand>>,\n    ...rest: Array<DataOrFunction<Partial<IPartialPactCommand>>>\n  ): ((cmd?: InitialInput) => Partial<IPartialPactCommand>) =>\n  (initial: InitialInput = {}) => {\n    const args: Array<\n      | Partial<IPartialPactCommand>\n      | ((cmd: Partial<IPartialPactCommand>) => Partial<IPartialPactCommand>)\n    > = [first, ...rest];\n    const command = args.reduce<Partial<IPartialPactCommand>>(\n      (acc, next: unknown) => {\n        return typeof next === 'function'\n          ? next(acc)\n          : patchCommand(acc, next as Partial<IPartialPactCommand>);\n      },\n      typeof initial === 'function' ? initial() : initial,\n    );\n    const finalCommand = finalizeCommand(command);\n    return finalCommand;\n  };\n"]}