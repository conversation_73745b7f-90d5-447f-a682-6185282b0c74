{"version": 3, "file": "addSigner.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/addSigner.ts"], "names": [], "mappings": ";;;AASA,iDAA8C;AAW9C;;;;GAIG;AACU,QAAA,SAAS,GAAe,CAAC,CACpC,MAA2B,EAC3B,UAKW,EACF,EAAE;IACX,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1D,IAAI,KAA2D,CAAC;IAChE,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE,CAAC;QACrC,KAAK,GAAG,UAAU,CAAC,CAAC,IAAY,EAAE,GAAG,IAAe,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI;YACJ,IAAI;SACL,CAAC,CAAC,CAAC;IACN,CAAC;IAED,OAAO,CAAC,GAAwB,EAAE,EAAE,CAClC,IAAA,2BAAY,EAAC,GAAG,EAAE;QAChB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,MAAM,cAAc,GAClB,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC1D,MAAM,EACJ,MAAM,EACN,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,EAChD,OAAO,GAAG,SAAS,GACpB,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;YACvD,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7C,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzC,8DAA8D;aACxD,CAAC;QACX,CAAC,CAAC;KACH,CAAC,CAAC;IACL,8DAA8D;AAChE,CAAC,CAAQ,CAAC", "sourcesContent": ["import type { ICap } from '@kadena/types';\nimport type {\n  IPartialPactCommand,\n  ISigner,\n} from '../../interfaces/IPactCommand';\nimport type {\n  ExtractCapabilityType,\n  IGeneralCapability,\n} from '../../interfaces/type-utilities';\nimport { patchCommand } from './patchCommand';\n\ninterface IAddSigner {\n  (first: ISigner | ISigner[]): () => IPartialPactCommand;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  <TCommand extends any>(\n    first: ISigner | ISigner[],\n    capability: (withCapability: ExtractType<TCommand>) => ICap[],\n  ): TCommand;\n}\n\n/**\n * Reducer to add a signer and capabilities on a {@link IPactCommand}\n *\n * @public\n */\nexport const addSigner: IAddSigner = ((\n  signer: ISigner | ISigner[],\n  capability: (\n    withCapability: (\n      name: string,\n      ...args: unknown[]\n    ) => { name: string; args: unknown[] },\n  ) => ICap[],\n): unknown => {\n  const signers = Array.isArray(signer) ? signer : [signer];\n  let clist: undefined | Array<{ name: string; args: unknown[] }>;\n  if (typeof capability === 'function') {\n    clist = capability((name: string, ...args: unknown[]) => ({\n      name,\n      args,\n    }));\n  }\n\n  return (cmd: IPartialPactCommand) =>\n    patchCommand(cmd, {\n      signers: signers.map((item) => {\n        const isWhenAuthnKey =\n          typeof item === 'string' && item.startsWith('WEBAUTHN');\n        const {\n          pubKey,\n          scheme = isWhenAuthnKey ? 'WebAuthn' : 'ED25519',\n          address = undefined,\n        } = typeof item === 'object' ? item : { pubKey: item };\n        return {\n          pubKey,\n          scheme,\n          ...(address !== undefined ? { address } : {}),\n          ...(clist !== undefined ? { clist } : {}),\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        } as any;\n      }),\n    });\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n}) as any;\n\nexport type ExtractType<TCmdReducer> = TCmdReducer extends (cmd: {\n  payload: infer TPayload;\n}) => unknown\n  ? ExtractCapabilityType<{ payload: TPayload }>\n  : IGeneralCapability;\n"]}