{"version": 3, "file": "addVerifier.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/addVerifier.ts"], "names": [], "mappings": ";;;AAIA,iDAA8C;AAgB9C;;;;GAIG;AACU,QAAA,WAAW,GAAiB,CAAC,CACxC,QAAmB,EACnB,UAKW,EACF,EAAE;IACX,IAAI,KAAK,GAAyD,EAAE,CAAC;IACrE,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE,CAAC;QACrC,KAAK,GAAG,UAAU,CAAC,CAAC,IAAY,EAAE,GAAG,IAAe,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI;YACJ,IAAI;SACL,CAAC,CAAC,CAAC;IACN,CAAC;IAED,OAAO,CAAC,GAAwB,EAAE,EAAE,CAClC,IAAA,2BAAY,EAAC,GAAG,EAAE;QAChB,SAAS,EAAE;YACT;gBACE,GAAG,QAAQ;gBACX,KAAK;gBACL,8DAA8D;aACxD;SACT;KACF,CAAC,CAAC;IACL,8DAA8D;AAChE,CAAC,CAAQ,CAAC", "sourcesContent": ["import type { ICap, PactValue } from '@kadena/types';\nimport type { IPartialPactCommand } from '../../interfaces/IPactCommand';\n\nimport type { ExtractType } from './addSigner';\nimport { patchCommand } from './patchCommand';\n\nexport interface IVerifier {\n  name: string;\n  proof: PactValue;\n}\n\ninterface IAddVerifier {\n  (verifier: IVerifier): () => IPartialPactCommand;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  <TCommand extends any>(\n    verifier: IVerifier,\n    capability: (forCapability: ExtractType<TCommand>) => ICap[],\n  ): TCommand;\n}\n\n/**\n * Reducer to add a verifiers and capabilities on a {@link IPactCommand}\n *\n * @public\n */\nexport const addVerifier: IAddVerifier = ((\n  verifier: IVerifier,\n  capability: (\n    forCapability: (\n      name: string,\n      ...args: unknown[]\n    ) => { name: string; args: unknown[] },\n  ) => ICap[],\n): unknown => {\n  let clist: undefined | Array<{ name: string; args: unknown[] }> = [];\n  if (typeof capability === 'function') {\n    clist = capability((name: string, ...args: unknown[]) => ({\n      name,\n      args,\n    }));\n  }\n\n  return (cmd: IPartialPactCommand) =>\n    patchCommand(cmd, {\n      verifiers: [\n        {\n          ...verifier,\n          clist,\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        } as any,\n      ],\n    });\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n}) as any;\n"]}