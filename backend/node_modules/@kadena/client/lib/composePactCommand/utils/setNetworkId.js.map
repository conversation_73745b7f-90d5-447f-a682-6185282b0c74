{"version": 3, "file": "setNetworkId.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/setNetworkId.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACI,MAAM,YAAY,GAAG,CAAC,SAAiB,EAAyB,EAAE;IACvE,OAAO,EAAE,SAAS,EAAE,CAAC;AACvB,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB", "sourcesContent": ["/**\n * Function to set `networkId` on {@link IPactCommand.networkId}\n * @public\n */\nexport const setNetworkId = (networkId: string): { networkId: string } => {\n  return { networkId };\n};\n"]}