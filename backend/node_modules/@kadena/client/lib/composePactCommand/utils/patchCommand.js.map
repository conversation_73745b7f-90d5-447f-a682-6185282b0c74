{"version": 3, "file": "patchCommand.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/patchCommand.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACI,MAAM,YAAY,GAAG,CAC1B,OAAmD,EACnD,UAA0C,EACV,EAAE;;IAClC,IAAI,OAAO,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS;QACnD,OAAO,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,OAAO,CAAC;IAE/B,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,UAAU,EAAE,CAAC;QAC9C,OAAO;YACL,IAAI,EAAE;gBACJ,IAAI,EAAE,CAAC,MAAA,MAAA,OAAO,CAAC,IAAI,0CAAE,IAAI,mCAAI,EAAE,CAAC,GAAG,CAAC,MAAA,MAAA,UAAU,CAAC,IAAI,0CAAE,IAAI,mCAAI,EAAE,CAAC;gBAChE,IAAI,EAAE;oBACJ,GAAG,MAAA,OAAO,CAAC,IAAI,0CAAE,IAAI;oBACrB,GAAG,MAAA,UAAU,CAAC,IAAI,0CAAE,IAAI;iBACzB;aACF;SACF,CAAC;IACJ,CAAC;IAED,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,UAAU,EAAE,CAAC;QAC9C,OAAO;YACL,IAAI,EAAE;gBACJ,GAAG,OAAO,CAAC,IAAI;gBACf,GAAG,UAAU,CAAC,IAAI;gBAClB,IAAI,EAAE;oBACJ,GAAG,MAAA,OAAO,CAAC,IAAI,0CAAE,IAAI;oBACrB,GAAG,MAAA,UAAU,CAAC,IAAI,0CAAE,IAAI;iBACzB;aACF;SACF,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC3C,CAAC,CAAC;AAjCW,QAAA,YAAY,gBAiCvB;AAEF;;;;;;;;;GASG;AACH,SAAgB,YAAY,CAC1B,OAA4B,EAC5B,KAA0B;;IAE1B,MAAM,KAAK,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;IAC7B,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QAChC,KAAK,CAAC,OAAO,GAAG,IAAA,oBAAY,EAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7D,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC7B,KAAK,CAAC,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IAChD,CAAC;IACD,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;QAC9B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC5B,CAAC;IACD,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAClC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IACpC,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QAChC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;;YAC/B,MAAA,KAAK,CAAC,OAAO,oCAAb,KAAK,CAAC,OAAO,GAAK,EAAE,EAAC;YACrB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO;iBAC9B,MAAM,CAAC,OAAO,CAAC;iBACf,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,OAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,CAAC,CAAC;YACnD,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,WAAW,CAAC,KAAK,GAAG;oBAClB,GAAG,CAAC,MAAA,WAAW,CAAC,KAAK,mCAAI,EAAE,CAAC;oBAC5B,GAAG,CAAC,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,mCAAI,EAAE,CAAC;iBACzB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACD,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChE,KAAK,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,MAAA,KAAK,CAAC,SAAS,mCAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;IACrE,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AArCD,oCAqCC", "sourcesContent": ["import type { IPartialPactCommand } from '../../interfaces/IPactCommand';\n\n/**\n * @internal\n */\nexport const mergePayload = (\n  payload: IPartialPactCommand['payload'] | undefined,\n  newPayload: IPartialPactCommand['payload'],\n): IPartialPactCommand['payload'] => {\n  if (payload === undefined || newPayload === undefined)\n    return newPayload ?? payload;\n\n  if ('exec' in payload && 'exec' in newPayload) {\n    return {\n      exec: {\n        code: (payload.exec?.code ?? '') + (newPayload.exec?.code ?? ''),\n        data: {\n          ...payload.exec?.data,\n          ...newPayload.exec?.data,\n        },\n      },\n    };\n  }\n\n  if ('cont' in payload && 'cont' in newPayload) {\n    return {\n      cont: {\n        ...payload.cont,\n        ...newPayload.cont,\n        data: {\n          ...payload.cont?.data,\n          ...newPayload.cont?.data,\n        },\n      },\n    };\n  }\n\n  throw new Error('PAYLOAD_NOT_MERGEABLE');\n};\n\n/**\n * Merge a partial command on top of the command\n *\n * @remarks\n * It will only be necessary to use in advanced use cases\n *\n * @param command - the target command\n * @param patch - the properties to patch on top of the target command\n * @public\n */\nexport function patchCommand(\n  command: IPartialPactCommand,\n  patch: IPartialPactCommand,\n): IPartialPactCommand {\n  const state = { ...command };\n  if (patch.payload !== undefined) {\n    state.payload = mergePayload(state.payload, patch.payload);\n  }\n  if (patch.meta !== undefined) {\n    state.meta = { ...state.meta, ...patch.meta };\n  }\n  if (patch.nonce !== undefined) {\n    state.nonce = patch.nonce;\n  }\n  if (patch.networkId !== undefined) {\n    state.networkId = patch.networkId;\n  }\n  if (patch.signers !== undefined) {\n    patch.signers.forEach((signer) => {\n      state.signers ??= [];\n      const foundSigner = state.signers\n        .filter(Boolean)\n        .find((item) => signer?.pubKey === item?.pubKey);\n      if (foundSigner !== undefined) {\n        foundSigner.clist = [\n          ...(foundSigner.clist ?? []),\n          ...(signer?.clist ?? []),\n        ];\n      } else {\n        state.signers.push(signer);\n      }\n    });\n  }\n  if (patch.verifiers !== undefined && patch.verifiers.length > 0) {\n    state.verifiers = [...(state.verifiers ?? []), ...patch.verifiers];\n  }\n  return state;\n}\n"]}