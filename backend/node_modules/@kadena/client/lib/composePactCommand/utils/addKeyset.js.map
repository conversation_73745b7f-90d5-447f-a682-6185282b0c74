{"version": 3, "file": "addKeyset.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/addKeyset.ts"], "names": [], "mappings": ";;;AAIA,uCAAoC;AAgBpC;;;;GAIG;AACI,MAAM,SAAS,GAAe,CACnC,IAAY,EACZ,IAAY,EACZ,GAAG,UAAoB,EACvB,EAAE,CAAC,IAAA,iBAAO,EAAC,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AAJlC,QAAA,SAAS,aAIyB", "sourcesContent": ["import type {\n  BuiltInPredicate,\n  IPartialPactCommand,\n} from '../../interfaces/IPactCommand';\nimport { addData } from './addData';\n\nexport interface IAddKeyset {\n  <TKey extends string, PRED extends BuiltInPredicate>(\n    key: <PERSON><PERSON><PERSON>,\n    pred: PRED,\n    ...publicKeys: string[]\n  ): (cmd: IPartialPactCommand) => IPartialPactCommand;\n\n  <TKey extends string, PRED extends string>(\n    key: TK<PERSON>,\n    pred: PRED,\n    ...publicKeys: string[]\n  ): (cmd: IPartialPactCommand) => IPartialPactCommand;\n}\n\n/**\n * Helper to add keyset to the data property for {@link IPactCommand.payload}\n *\n * @public\n */\nexport const addKeyset: IAddKeyset = (\n  name: string,\n  pred: string,\n  ...publicKeys: string[]\n) => addData(name, { keys: publicKeys, pred });\n"]}