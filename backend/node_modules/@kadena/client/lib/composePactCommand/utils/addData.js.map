{"version": 3, "file": "addData.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/addData.ts"], "names": [], "mappings": ";;;AACA,iDAA8C;AAS9C,MAAM,OAAO,GAAG,CAAC,GAAwB,EAAE,GAAW,EAAW,EAAE;;IACjE,IACE,GAAG,CAAC,OAAO;QACX,MAAM,IAAI,GAAG,CAAC,OAAO;QACrB,CAAA,MAAA,GAAG,CAAC,OAAO,CAAC,IAAI,0CAAE,IAAI,MAAK,SAAS,EACpC,CAAC;QACD,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED,IACE,GAAG,CAAC,OAAO;QACX,MAAM,IAAI,GAAG,CAAC,OAAO;QACrB,CAAA,MAAA,GAAG,CAAC,OAAO,CAAC,IAAI,0CAAE,IAAI,MAAK,SAAS,EACpC,CAAC;QACD,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF;;;;;GAKG;AACI,MAAM,OAAO,GAIlB,CAAC,GAAW,EAAE,KAAqB,EAAE,EAAE,CACvC,CAAC,GAAwB,EAAuB,EAAE;IAChD,IAAI,MAAM,GAAoB,MAAM,CAAC;IACrC,IAAI,GAAG,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;QACzC,MAAM,GAAG,MAAM,CAAC;IAClB,CAAC;IACD,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;QACpC,MAAM,IAAI,KAAK,CACb,oBAAoB,GAAG,oCAAoC,CAC5D,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,GAAG;QACZ,OAAO,EAAE;YACP,CAAC,MAAM,CAAC,EAAE;gBACR,IAAI,EAAE;oBACJ,CAAC,GAAa,CAAC,EAAE,KAAK;iBACvB;aACF;SACF;KACqB,CAAC;IACzB,OAAO,IAAA,2BAAY,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAClC,CAAC,CAAC;AAzBS,QAAA,OAAO,WAyBhB", "sourcesContent": ["import type { IPartialPactCommand } from '../../interfaces/IPactCommand';\nimport { patchCommand } from './patchCommand';\n\nexport type ValidDataTypes =\n  | Record<string, unknown>\n  | string\n  | number\n  | boolean\n  | Array<ValidDataTypes>;\n\nconst getData = (cmd: IPartialPactCommand, key: string): unknown => {\n  if (\n    cmd.payload &&\n    'exec' in cmd.payload &&\n    cmd.payload.exec?.data !== undefined\n  ) {\n    return cmd.payload.exec.data[key];\n  }\n\n  if (\n    cmd.payload &&\n    'cont' in cmd.payload &&\n    cmd.payload.cont?.data !== undefined\n  ) {\n    return cmd.payload.cont.data[key];\n  }\n\n  return undefined;\n};\n\n/**\n * Reducer to add `data` to the {@link IPactCommand.payload}\n * @throws DUPLICATED_KEY: \"$\\{key\\}\" is already available in the data\n *\n * @public\n */\nexport const addData: (\n  key: string,\n  value: ValidDataTypes,\n) => (cmd: IPartialPactCommand) => IPartialPactCommand =\n  (key: string, value: ValidDataTypes) =>\n  (cmd: IPartialPactCommand): IPartialPactCommand => {\n    let target: 'exec' | 'cont' = 'exec';\n    if (cmd.payload && 'cont' in cmd.payload) {\n      target = 'cont';\n    }\n    if (getData(cmd, key) !== undefined) {\n      throw new Error(\n        `DUPLICATED_KEY: \"${key}\" is already available in the data`,\n      );\n    }\n    const patch = {\n      payload: {\n        [target]: {\n          data: {\n            [key as string]: value,\n          },\n        },\n      },\n    } as IPartialPactCommand;\n    return patchCommand(cmd, patch);\n  };\n\nexport interface IAddKeyset {\n  <TKey extends string, PRED extends 'keys-all' | 'keys-any' | 'keys-2'>(\n    key: TKey,\n    pred: PRED,\n    ...publicKeys: string[]\n  ): (cmd: IPartialPactCommand) => IPartialPactCommand;\n\n  <TKey extends string, PRED extends string>(\n    key: TKey,\n    pred: PRED,\n    ...publicKeys: string[]\n  ): (cmd: IPartialPactCommand) => IPartialPactCommand;\n}\n"]}