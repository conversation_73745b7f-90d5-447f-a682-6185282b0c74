{"version": 3, "file": "setMeta.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/setMeta.ts"], "names": [], "mappings": ";;;AAIA,iDAA8C;AAE9C;;;GAGG;AACI,MAAM,OAAO,GAClB,CACE,OAEC,EACwD,EAAE,CAC7D,CAAC,OAAO,EAAE,EAAE;IACV,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IAC3C,OAAO,IAAA,2BAAY,EAAC,OAAO,EAAE;QAC3B,IAAI,EAAE;YACJ,GAAG,OAAO,CAAC,IAAI;YACf,GAAG,IAAI;YACP,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SACnC;KACjC,CAAC,CAAC;AACL,CAAC,CAAC;AAfS,QAAA,OAAO,WAehB", "sourcesContent": ["import type {\n  IPactCommand,\n  IPartialPactCommand,\n} from '../../interfaces/IPactCommand';\nimport { patchCommand } from './patchCommand';\n\n/**\n * Reducer to set `meta` on {@link IPartialPactCommand.meta}\n * @public\n */\nexport const setMeta =\n  (\n    options: Partial<Omit<IPactCommand['meta'], 'sender'>> & {\n      senderAccount?: string;\n    },\n  ): ((command: IPartialPactCommand) => IPartialPactCommand) =>\n  (command) => {\n    const { senderAccount, ...rest } = options;\n    return patchCommand(command, {\n      meta: {\n        ...command.meta,\n        ...rest,\n        ...(senderAccount !== undefined ? { sender: senderAccount } : {}),\n      } as IPartialPactCommand['meta'],\n    });\n  };\n"]}