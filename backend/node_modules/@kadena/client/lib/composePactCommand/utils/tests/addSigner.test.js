"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const addSigner_1 = require("../addSigner");
(0, vitest_1.describe)('addSigner', () => {
    (0, vitest_1.it)('returns a signer object', () => {
        (0, vitest_1.expect)((0, addSigner_1.addSigner)('bob_public_key')()).toEqual({
            signers: [
                {
                    pubKey: 'bob_public_key',
                    scheme: 'ED25519',
                },
            ],
        });
    });
    (0, vitest_1.it)('adds capability if presented', () => {
        (0, vitest_1.expect)(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (0, addSigner_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.GAS'),
        ])()).toEqual({
            signers: [
                {
                    pubKey: 'bob_public_key',
                    scheme: 'ED25519',
                    clist: [{ args: [], name: 'coin.GAS' }],
                },
            ],
        });
    });
    (0, vitest_1.it)('accept signer object as a first argument', () => {
        (0, vitest_1.expect)((0, addSigner_1.addSigner)({ pubKey: 'test', scheme: 'ED25519' })()).toEqual({
            signers: [
                {
                    pubKey: 'test',
                    scheme: 'ED25519',
                },
            ],
        });
    });
    (0, vitest_1.it)('returns signers with address if its presented', () => {
        const signer = (0, addSigner_1.addSigner)({
            pubKey: 'key',
            address: 'address',
        })();
        (0, vitest_1.expect)(signer).toEqual({
            signers: [{ pubKey: 'key', address: 'address', scheme: 'ED25519' }],
        });
    });
    (0, vitest_1.it)('sets the scheme to WebAuthn if public key starts with WEBAUTHN', () => {
        (0, vitest_1.expect)((0, addSigner_1.addSigner)('WEBAUTHN-a-public-key')()).toEqual({
            signers: [
                {
                    pubKey: 'WEBAUTHN-a-public-key',
                    scheme: 'WebAuthn',
                },
            ],
        });
    });
});
//# sourceMappingURL=addSigner.test.js.map