"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const addVerifier_1 = require("../addVerifier");
(0, vitest_1.describe)('addVerifier', () => {
    (0, vitest_1.it)('returns a verifier object', () => {
        (0, vitest_1.expect)((0, addVerifier_1.addVerifier)({ name: 'test-verifier', proof: 1.0 }, (forCapability) => [forCapability('test.cap', 'a', 'b', 1)])()).toEqual({
            verifiers: [
                {
                    name: 'test-verifier',
                    proof: 1.0,
                    clist: [{ name: 'test.cap', args: ['a', 'b', 1] }],
                },
            ],
        });
    });
    (0, vitest_1.it)('returns a verifier object', () => {
        (0, vitest_1.expect)((0, addVerifier_1.addVerifier)({ name: 'test-verifier', proof: [{ item: '1' }] }, (forCapability) => [forCapability('test.cap', 'a', 'b', 1)])()).toEqual({
            verifiers: [
                {
                    name: 'test-verifier',
                    proof: [{ item: '1' }],
                    clist: [{ name: 'test.cap', args: ['a', 'b', 1] }],
                },
            ],
        });
    });
});
//# sourceMappingURL=addVerifier.test.js.map