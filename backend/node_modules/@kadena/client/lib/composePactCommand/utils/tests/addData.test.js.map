{"version": 3, "file": "addData.test.js", "sourceRoot": "", "sources": ["../../../../src/composePactCommand/utils/tests/addData.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,wCAAqC;AAErC,IAAA,iBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;IACvB,IAAA,WAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,IAAA,eAAM,EACJ,IAAA,iBAAO,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACpE,CAAC,OAAO,CAAC;YACR,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;SAC/D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,IAAA,eAAM,EACJ,IAAA,iBAAO,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CACjE,CAAC,OAAO,CAAC;YACR,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;SAC5D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,6DAA6D,EAAE,GAAG,EAAE;QACrE,IAAA,eAAM,EAAC,IAAA,iBAAO,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAC3C,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;SAC/C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,MAAM,GAAG,GAAG,IAAA,iBAAO,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACzC,IAAA,eAAM,EAAC,GAAG,EAAE,CAAC,IAAA,iBAAO,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CACtD,KAAK,CAAC,yDAAyD,CAAC,CACjE,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { addData } from '../addData';\n\ndescribe('addData', () => {\n  it('add data for exec payload', () => {\n    expect(\n      addData('test', 'value')({ payload: { exec: { code: '(func)' } } }),\n    ).toEqual({\n      payload: { exec: { code: '(func)', data: { test: 'value' } } },\n    });\n  });\n\n  it('add data for cont payload', () => {\n    expect(\n      addData('test', 'value')({ payload: { cont: { pactId: '1' } } }),\n    ).toEqual({\n      payload: { cont: { pactId: '1', data: { test: 'value' } } },\n    });\n  });\n\n  it('return data for exec payload if input does not have payload', () => {\n    expect(addData('test', 'value')({})).toEqual({\n      payload: { exec: { data: { test: 'value' } } },\n    });\n  });\n\n  it('throws exception is data already has the same key', () => {\n    const cmd = addData('test', 'value')({});\n    expect(() => addData('test', 'value')(cmd)).toThrowError(\n      Error(`DUPLICATED_KEY: \"test\" is already available in the data`),\n    );\n  });\n});\n"]}