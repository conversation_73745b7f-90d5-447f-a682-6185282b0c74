{"version": 3, "file": "addVerifier.test.js", "sourceRoot": "", "sources": ["../../../../src/composePactCommand/utils/tests/addVerifier.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,gDAA6C;AAE7C,IAAA,iBAAQ,EAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,IAAA,WAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,IAAA,eAAM,EACJ,IAAA,yBAAW,EACT,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,EACrC,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAC5D,EAAE,CACJ,CAAC,OAAO,CAAC;YACR,SAAS,EAAE;gBACT;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,GAAG;oBACV,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;iBACnD;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,IAAA,eAAM,EACJ,IAAA,yBAAW,EACT,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,EACjD,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAC5D,EAAE,CACJ,CAAC,OAAO,CAAC;YACR,SAAS,EAAE;gBACT;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;oBACtB,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;iBACnD;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { addVerifier } from '../addVerifier';\n\ndescribe('addVerifier', () => {\n  it('returns a verifier object', () => {\n    expect(\n      addVerifier<any>(\n        { name: 'test-verifier', proof: 1.0 },\n        (forCapability) => [forCapability('test.cap', 'a', 'b', 1)],\n      )(),\n    ).toEqual({\n      verifiers: [\n        {\n          name: 'test-verifier',\n          proof: 1.0,\n          clist: [{ name: 'test.cap', args: ['a', 'b', 1] }],\n        },\n      ],\n    });\n  });\n\n  it('returns a verifier object', () => {\n    expect(\n      addVerifier<any>(\n        { name: 'test-verifier', proof: [{ item: '1' }] },\n        (forCapability) => [forCapability('test.cap', 'a', 'b', 1)],\n      )(),\n    ).toEqual({\n      verifiers: [\n        {\n          name: 'test-verifier',\n          proof: [{ item: '1' }],\n          clist: [{ name: 'test.cap', args: ['a', 'b', 1] }],\n        },\n      ],\n    });\n  });\n});\n"]}