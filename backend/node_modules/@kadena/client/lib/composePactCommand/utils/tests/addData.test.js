"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const addData_1 = require("../addData");
(0, vitest_1.describe)('addData', () => {
    (0, vitest_1.it)('add data for exec payload', () => {
        (0, vitest_1.expect)((0, addData_1.addData)('test', 'value')({ payload: { exec: { code: '(func)' } } })).toEqual({
            payload: { exec: { code: '(func)', data: { test: 'value' } } },
        });
    });
    (0, vitest_1.it)('add data for cont payload', () => {
        (0, vitest_1.expect)((0, addData_1.addData)('test', 'value')({ payload: { cont: { pactId: '1' } } })).toEqual({
            payload: { cont: { pactId: '1', data: { test: 'value' } } },
        });
    });
    (0, vitest_1.it)('return data for exec payload if input does not have payload', () => {
        (0, vitest_1.expect)((0, addData_1.addData)('test', 'value')({})).toEqual({
            payload: { exec: { data: { test: 'value' } } },
        });
    });
    (0, vitest_1.it)('throws exception is data already has the same key', () => {
        const cmd = (0, addData_1.addData)('test', 'value')({});
        (0, vitest_1.expect)(() => (0, addData_1.addData)('test', 'value')(cmd)).toThrowError(Error(`DUPLICATED_KEY: "test" is already available in the data`));
    });
});
//# sourceMappingURL=addData.test.js.map