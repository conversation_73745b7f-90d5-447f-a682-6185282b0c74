{"version": 3, "file": "addKeyset.test.js", "sourceRoot": "", "sources": ["../../../../src/composePactCommand/utils/tests/addKeyset.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,4CAAyC;AAEzC,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,WAAE,EAAC,4BAA4B,EAAE,GAAG,EAAE;QACpC,IAAA,eAAM,EAAC,IAAA,qBAAS,EAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5D,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,IAAI,EAAE;4BACJ,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;4BAClB,IAAI,EAAE,UAAU;yBACjB;qBACF;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { addKeyset } from '../addKeyset';\n\ndescribe('addKeyset', () => {\n  it('returns keyset data format', () => {\n    expect(addKeyset('test', 'keys-any', 'p1', 'p2')({})).toEqual({\n      payload: {\n        exec: {\n          data: {\n            test: {\n              keys: ['p1', 'p2'],\n              pred: 'keys-any',\n            },\n          },\n        },\n      },\n    });\n  });\n});\n"]}