"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const addKeyset_1 = require("../addKeyset");
(0, vitest_1.describe)('addKeyset', () => {
    (0, vitest_1.it)('returns keyset data format', () => {
        (0, vitest_1.expect)((0, addKeyset_1.addKeyset)('test', 'keys-any', 'p1', 'p2')({})).toEqual({
            payload: {
                exec: {
                    data: {
                        test: {
                            keys: ['p1', 'p2'],
                            pred: 'keys-any',
                        },
                    },
                },
            },
        });
    });
});
//# sourceMappingURL=addKeyset.test.js.map