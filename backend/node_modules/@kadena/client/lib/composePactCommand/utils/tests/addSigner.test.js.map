{"version": 3, "file": "addSigner.test.js", "sourceRoot": "", "sources": ["../../../../src/composePactCommand/utils/tests/addSigner.test.ts"], "names": [], "mappings": ";;AAAA,mCAA8C;AAC9C,4CAAyC;AAEzC,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,WAAE,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACjC,IAAA,eAAM,EAAC,IAAA,qBAAS,EAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;YAC5C,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,gBAAgB;oBACxB,MAAM,EAAE,SAAS;iBAClB;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,IAAA,eAAM;QACJ,8DAA8D;QAC9D,IAAA,qBAAS,EAAM,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YACnD,cAAc,CAAC,UAAU,CAAC;SAC3B,CAAC,EAAE,CACL,CAAC,OAAO,CAAC;YACR,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,gBAAgB;oBACxB,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;iBACxC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,IAAA,eAAM,EAAC,IAAA,qBAAS,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;YACjE,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,SAAS;iBAClB;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,MAAM,MAAM,GAAG,IAAA,qBAAS,EAAC;YACvB,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,SAAS;SACnB,CAAC,EAAE,CAAC;QACL,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;SACpE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gEAAgE,EAAE,GAAG,EAAE;QACxE,IAAA,eAAM,EAAC,IAAA,qBAAS,EAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;YACnD,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,uBAAuB;oBAC/B,MAAM,EAAE,UAAU;iBACnB;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { describe, expect, it } from 'vitest';\nimport { addSigner } from '../addSigner';\n\ndescribe('addSigner', () => {\n  it('returns a signer object', () => {\n    expect(addSigner('bob_public_key')()).toEqual({\n      signers: [\n        {\n          pubKey: 'bob_public_key',\n          scheme: 'ED25519',\n        },\n      ],\n    });\n  });\n\n  it('adds capability if presented', () => {\n    expect(\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      addSigner<any>('bob_public_key', (withCapability) => [\n        withCapability('coin.GAS'),\n      ])(),\n    ).toEqual({\n      signers: [\n        {\n          pubKey: 'bob_public_key',\n          scheme: 'ED25519',\n          clist: [{ args: [], name: 'coin.GAS' }],\n        },\n      ],\n    });\n  });\n\n  it('accept signer object as a first argument', () => {\n    expect(addSigner({ pubKey: 'test', scheme: 'ED25519' })()).toEqual({\n      signers: [\n        {\n          pubKey: 'test',\n          scheme: 'ED25519',\n        },\n      ],\n    });\n  });\n\n  it('returns signers with address if its presented', () => {\n    const signer = addSigner({\n      pubKey: 'key',\n      address: 'address',\n    })();\n    expect(signer).toEqual({\n      signers: [{ pubKey: 'key', address: 'address', scheme: 'ED25519' }],\n    });\n  });\n\n  it('sets the scheme to WebAuthn if public key starts with WEBAUTHN', () => {\n    expect(addSigner('WEBAUTHN-a-public-key')()).toEqual({\n      signers: [\n        {\n          pubKey: 'WEBAUTHN-a-public-key',\n          scheme: 'WebAuthn',\n        },\n      ],\n    });\n  });\n});\n"]}