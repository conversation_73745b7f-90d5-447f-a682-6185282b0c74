{"version": 3, "file": "payload.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/payload.ts"], "names": [], "mappings": ";;;AAqCA;;;;GAIG;AACI,MAAM,SAAS,GAAU,CAAC,GAAG,KAAe,EAAE,EAAE;IACrD,MAAM,GAAG,GAA4B;QACnC,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;KACzC,CAAC;IACF,OAAO;QACL,OAAO,EAAE,GAAG;QACZ,2FAA2F;QAC3F,8DAA8D;KACxD,CAAC;AACX,CAAC,CAAC;AATW,QAAA,SAAS,aASpB;AAEF;;;;GAIG;AACI,MAAM,YAAY,GAAU,CAAC,OAAO,EAAE,EAAE;IAC7C,MAAM,KAAK,GAAG;QACZ,IAAI,EAAE,EAAE;QACR,GAAG,OAAO;KACX,CAAC;IACF,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QACpC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAChD,CAAC;IACD,OAAO;QACL,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;KACzB,CAAC;AACJ,CAAC,CAAC;AAXW,QAAA,YAAY,gBAWvB", "sourcesContent": ["import type { ICap } from '@kadena/types';\nimport type {\n  IContinuationPayloadObject,\n  IExecutionPayloadObject,\n} from '../../interfaces/IPactCommand';\nimport type { ExtractPactModule } from '../../interfaces/type-utilities';\n\nexport type AddCapabilities<T> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [K in keyof T]: T[K] extends { capability: any }\n    ? T[K]\n    : ExtractPactModule<T[K]>;\n};\n\ninterface IExec {\n  <\n    TCodes extends Array<\n      | (string & {\n          capability(name: string, ...args: unknown[]): ICap;\n        })\n      | string\n    >,\n  >(\n    ...codes: [...TCodes]\n  ): {\n    payload: { exec: Required<IExecutionPayloadObject['exec']> } & {\n      funs: AddCapabilities<[...TCodes]>;\n    };\n  };\n}\n\ninterface ICont {\n  (options: Partial<IContinuationPayloadObject['cont']>): {\n    payload: { cont: Partial<IContinuationPayloadObject['cont']> };\n  };\n}\n\n/**\n * Utility function to create payload for execution {@link IPactCommand.payload}\n *\n * @public\n */\nexport const execution: IExec = (...codes: string[]) => {\n  const pld: IExecutionPayloadObject = {\n    exec: { code: codes.join(''), data: {} },\n  };\n  return {\n    payload: pld,\n    // funs is a trick to make the type inferring work but it's not a real field in the payload\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } as any;\n};\n\n/**\n * Utility function to create payload for continuation  {@link IPactCommand.payload}\n *\n * @public\n */\nexport const continuation: ICont = (options) => {\n  const clone = {\n    data: {},\n    ...options,\n  };\n  if (typeof clone.proof === 'string') {\n    clone.proof = clone.proof.replace(/\\\"/gi, '');\n  }\n  return {\n    payload: { cont: clone },\n  };\n};\n"]}