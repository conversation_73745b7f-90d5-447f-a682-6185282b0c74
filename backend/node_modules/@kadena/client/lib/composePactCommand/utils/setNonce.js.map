{"version": 3, "file": "setNonce.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/utils/setNonce.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACI,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAqB,EAAE;IAC3D,OAAO,EAAE,KAAK,EAAE,CAAC;AACnB,CAAC,CAAC;AAFW,QAAA,QAAQ,YAEnB", "sourcesContent": ["/**\n * Function to set `nonce` on {@link IPactCommand.nonce}\n * @public\n */\nexport const setNonce = (nonce: string): { nonce: string } => {\n  return { nonce };\n};\n"]}