{"version": 3, "file": "coin-contract.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/test/coin-contract.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ICap } from '@kadena/types';\n\ninterface ITransferCapability {\n  (name: 'coin.GAS'): ICap;\n  (\n    name: 'coin.TRANSFER',\n    from: string,\n    to: string,\n    amount: { decimal: string },\n  ): ICap;\n}\n\ninterface ITransferCrosschainCapability {\n  (name: 'coin.GAS'): ICap;\n  (\n    name: 'coin.TRANSFER_XCHAIN',\n    sender: string,\n    receiver: string,\n    amount: { decimal: string },\n  ): ICap;\n  (name: 'coin.CREDIT', receiver: string): ICap;\n}\n\nexport interface ICoin {\n  transfer: (\n    from: string,\n    to: string,\n    amount: { decimal: string },\n  ) => string & {\n    capability: ITransferCapability;\n  };\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  'transfer-crosschain': (\n    sender: string,\n    receiver: string,\n    receiverGuard: string,\n    targetChain: string,\n    amount: { decimal: string },\n  ) => string & {\n    capability: ITransferCrosschainCapability;\n  };\n}\n"]}