"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const fp_1 = require("../../fp");
const pact_1 = require("../../pact");
const createTransaction_1 = require("../../utils/createTransaction");
const composePactCommand_1 = require("../composePactCommand");
const addData_1 = require("../utils/addData");
const addVerifier_1 = require("../utils/addVerifier");
const patchCommand_1 = require("../utils/patchCommand");
const coin = (0, pact_1.getModule)('coin');
(0, vitest_1.describe)('execution', () => {
    (0, vitest_1.it)('returns a payload object of a exec command', () => {
        const command = (0, fp_1.execution)(coin.transfer('alice', 'bob', { decimal: '12.1' }));
        (0, vitest_1.expect)(command.payload.exec.code).toBe('(coin.transfer "alice" "bob" 12.1)');
    });
    (0, vitest_1.it)('adds multiple command', () => {
        const command = (0, fp_1.execution)(coin.transfer('alice', 'bob', { decimal: '0.1' }), coin.transfer('bob', 'alice', { decimal: '0.1' }));
        (0, vitest_1.expect)(command.payload.exec.code).toBe('(coin.transfer "alice" "bob" 0.1)(coin.transfer "bob" "alice" 0.1)');
    });
});
(0, vitest_1.describe)('continuation', () => {
    (0, vitest_1.it)('returns a payload object of a cont command', () => {
        const command = (0, fp_1.continuation)({
            pactId: '1',
            proof: 'test-proof',
            step: 1,
            rollback: false,
        });
        (0, vitest_1.expect)(command.payload).toEqual({
            cont: {
                pactId: '1',
                proof: 'test-proof',
                rollback: false,
                step: 1,
                data: {},
            },
        });
    });
});
(0, vitest_1.describe)('composePactCommand', () => {
    (0, vitest_1.beforeEach)(() => {
        vitest_1.vi.useFakeTimers().setSystemTime(new Date('2023-07-27'));
    });
    (0, vitest_1.afterEach)(() => {
        vitest_1.vi.useRealTimers();
    });
    (0, vitest_1.it)('returns command object with signers and capabilities', () => {
        const command = (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('alice', 'bob', { decimal: '12.1' })), (0, fp_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.GAS'),
            withCapability('coin.TRANSFER', 'alice', 'bob', { decimal: '12.1' }),
        ]), (0, fp_1.setNonce)('test-nonce'))();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: {
                    code: '(coin.transfer "alice" "bob" 12.1)',
                    data: {},
                },
            },
            signers: [
                {
                    clist: [
                        { args: [], name: 'coin.GAS' },
                        {
                            args: ['alice', 'bob', { decimal: '12.1' }],
                            name: 'coin.TRANSFER',
                        },
                    ],
                    pubKey: 'bob_public_key',
                    scheme: 'ED25519',
                },
            ],
            nonce: 'test-nonce',
        });
    });
    (0, vitest_1.it)('returns a command based on ICommand interface', () => {
        const command = (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('alice', 'bob', { decimal: '12.1' })), (0, fp_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.GAS'),
            withCapability('coin.TRANSFER', 'alice', 'bob', { decimal: '12.1' }),
        ]), (0, fp_1.setMeta)({
            chainId: '1',
            senderAccount: 'gas-station',
            gasPrice: 381,
            gasLimit: 400,
            creationTime: 123,
            ttl: 1000,
        }), (0, fp_1.setNetworkId)('test-network-id'), (0, fp_1.setNonce)('test-nonce'))();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: {
                    code: '(coin.transfer "alice" "bob" 12.1)',
                    data: {},
                },
            },
            signers: [
                {
                    clist: [
                        { args: [], name: 'coin.GAS' },
                        {
                            args: ['alice', 'bob', { decimal: '12.1' }],
                            name: 'coin.TRANSFER',
                        },
                    ],
                    pubKey: 'bob_public_key',
                    scheme: 'ED25519',
                },
            ],
            meta: {
                chainId: '1',
                creationTime: 123,
                gasLimit: 400,
                gasPrice: 381,
                sender: 'gas-station',
                ttl: 1000,
            },
            networkId: 'test-network-id',
            nonce: 'test-nonce',
        });
    });
    (0, vitest_1.it)('adds kjs nonce if not presented in the input', () => {
        const command = (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })))();
        (0, vitest_1.expect)(command.nonce).toBe('kjs:nonce:*************');
    });
    (0, vitest_1.it)('merges payload if they are exec', () => {
        (0, vitest_1.expect)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.execution)(coin.transfer('alice', 'bob', { decimal: '1' })))().payload).toEqual({
            exec: {
                code: '(coin.transfer "bob" "alice" 1.0)(coin.transfer "alice" "bob" 1.0)',
                data: {},
            },
        });
    });
    (0, vitest_1.it)('merges payloads data if they are exec', () => {
        (0, vitest_1.expect)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' }), coin.transfer('alice', 'bob', { decimal: '1' })), (0, addData_1.addData)('one', 'test'), (0, addData_1.addData)('two', 'test'))().payload).toEqual({
            exec: {
                code: '(coin.transfer "bob" "alice" 1.0)(coin.transfer "alice" "bob" 1.0)',
                data: { one: 'test', two: 'test' },
            },
        });
    });
    (0, vitest_1.it)('throws exception if payloads are not mergable', () => {
        (0, vitest_1.expect)(() => (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.continuation)({ pactId: '1' }))().payload).toThrowError(new Error('PAYLOAD_NOT_MERGEABLE'));
    });
    (0, vitest_1.it)('accepts a signer without a capability', () => {
        (0, vitest_1.expect)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.addSigner)('bob_public_key'))().signers).toEqual([{ pubKey: 'bob_public_key', scheme: 'ED25519' }]);
    });
    (0, vitest_1.it)('merges capability arrays of one signer if presented twice', () => {
        (0, vitest_1.expect)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.GAS'),
        ]), (0, fp_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '1' }),
        ]))().signers).toEqual([
            {
                pubKey: 'bob_public_key',
                scheme: 'ED25519',
                clist: [
                    { args: [], name: 'coin.GAS' },
                    { args: ['bob', 'alice', { decimal: '1' }], name: 'coin.TRANSFER' },
                ],
            },
        ]);
        (0, vitest_1.expect)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.addSigner)('bob_public_key'), (0, fp_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '1' }),
        ]))().signers).toEqual([
            {
                pubKey: 'bob_public_key',
                scheme: 'ED25519',
                clist: [
                    { args: ['bob', 'alice', { decimal: '1' }], name: 'coin.TRANSFER' },
                ],
            },
        ]);
    });
    (0, vitest_1.it)("adds creationTime if it's not presented in the meta property", () => {
        var _a;
        (0, vitest_1.expect)((_a = (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.setMeta)({ chainId: '1' }))().meta) === null || _a === void 0 ? void 0 : _a.creationTime).toBe(1690416000);
    });
    (0, vitest_1.it)('returns transaction object by calling createTransaction', () => {
        (0, vitest_1.expect)((0, createTransaction_1.createTransaction)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('bob', 'alice', { decimal: '1' })), (0, fp_1.addSigner)('bob_public_key'), (0, fp_1.addSigner)('bob_public_key', (withCapability) => [
            withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '1' }),
        ]))())).toEqual({
            cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bob\\" \\"alice\\" 1.0)","data":{}}},"signers":[{"pubKey":"bob_public_key","scheme":"ED25519","clist":[{"name":"coin.TRANSFER","args":["bob","alice",{"decimal":"1"}]}]}],"nonce":"kjs:nonce:*************"}',
            hash: 'XjFto2SijaGZpRzdwWdZDkPI7WheTUuIMs8DHaqL2jU',
            sigs: [undefined],
        });
    });
    (0, vitest_1.it)('adds does not set sender if its not presented', () => {
        const command = (0, composePactCommand_1.composePactCommand)((0, composePactCommand_1.composePactCommand)((0, fp_1.execution)('(test)'), (0, fp_1.setMeta)({ senderAccount: 'test' })), (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)('(test 2)'), (0, fp_1.setMeta)({ chainId: '1' })), (0, fp_1.setMeta)({
            gasLimit: 1,
            gasPrice: 1,
            creationTime: 0,
            ttl: 1,
        }))();
        (0, vitest_1.expect)(command.payload).toEqual({
            exec: {
                code: '(test)(test 2)',
                data: {},
            },
        });
        (0, vitest_1.expect)(command.meta).toEqual({
            sender: 'test',
            chainId: '1',
            gasLimit: 1,
            gasPrice: 1,
            creationTime: 0,
            ttl: 1,
        });
    });
    (0, vitest_1.it)('merge data if they are continuation', () => {
        (0, vitest_1.expect)((0, composePactCommand_1.composePactCommand)((0, fp_1.continuation)({
            pactId: '1',
            step: 1,
            rollback: false,
            proof: null,
            data: { direct: 'test' },
        }), (0, addData_1.addData)('one', 'test'))().payload).toEqual({
            cont: {
                pactId: '1',
                step: 1,
                rollback: false,
                proof: null,
                data: {
                    one: 'test',
                    direct: 'test',
                },
            },
        });
    });
});
(0, vitest_1.describe)('mergePayload', () => {
    (0, vitest_1.it)('merge code part of two payload', () => {
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)({ exec: { code: '(one)' } }, { exec: { code: '(two)' } })).toEqual({
            exec: {
                code: '(one)(two)',
                data: {},
            },
        });
    });
    (0, vitest_1.it)('merge data part of two payload', () => {
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)({ exec: { code: '(one)', data: { one: 'test' } } }, { exec: { code: '(two)', data: { two: 'test' } } })).toEqual({
            exec: {
                code: '(one)(two)',
                data: {
                    one: 'test',
                    two: 'test',
                },
            },
        });
    });
    (0, vitest_1.it)('returns the non-undefined if one of the inputs is undefined', () => {
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)({ exec: { code: '(one)', data: { one: 'test' } } }, undefined)).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)(undefined, {
            exec: { code: '(one)', data: { one: 'test' } },
        })).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });
    });
    (0, vitest_1.it)('returns merged data', () => {
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)({ cont: { data: { one: 'test' } } }, { cont: { pactId: '1', data: { two: 'test' } } })).toEqual({
            cont: {
                pactId: '1',
                data: { one: 'test', two: 'test' },
            },
        });
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)(undefined, {
            exec: { code: '(one)', data: { one: 'test' } },
        })).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });
    });
    (0, vitest_1.it)('should not override input data', () => {
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)({ cont: { proof: 'proof', data: { one: 'test' } } }, { cont: { pactId: '1', data: { two: 'test' } } })).toEqual({
            cont: {
                pactId: '1',
                proof: 'proof',
                data: { one: 'test', two: 'test' },
            },
        });
        (0, vitest_1.expect)((0, patchCommand_1.mergePayload)(undefined, {
            exec: { code: '(one)', data: { one: 'test' } },
        })).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });
    });
    (0, vitest_1.it)('throws error if object are not the same brand', () => {
        (0, vitest_1.expect)(() => (0, patchCommand_1.mergePayload)({ exec: { code: 'test' } }, { cont: { pactId: '1' } })).toThrowError(new Error('PAYLOAD_NOT_MERGEABLE'));
    });
    (0, vitest_1.it)('adds creationTime to metadata of mataData is presented but does not have creationTime', () => {
        var _a;
        vitest_1.vi.useFakeTimers().setSystemTime(new Date('2023-07-27'));
        const pactCommand = (0, composePactCommand_1.composePactCommand)({
            meta: { chainId: '1' },
        })();
        (0, vitest_1.expect)((_a = pactCommand.meta) === null || _a === void 0 ? void 0 : _a.creationTime).toBe(1690416000);
        vitest_1.vi.useRealTimers();
    });
    (0, vitest_1.it)('returns command object with verifiers and capabilities', () => {
        const command = (0, composePactCommand_1.composePactCommand)((0, fp_1.execution)(coin.transfer('alice', 'bob', { decimal: '12.1' })), (0, addVerifier_1.addVerifier)({ name: 'test-verifier', proof: 'test-proof' }, (forCapability) => [
            forCapability('coin.GAS'),
            forCapability('coin.TRANSFER', 'alice', 'bob', { decimal: '12.1' }),
        ]), (0, fp_1.setNonce)('test-nonce'))();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: {
                    code: '(coin.transfer "alice" "bob" 12.1)',
                    data: {},
                },
            },
            signers: [],
            verifiers: [
                {
                    clist: [
                        { args: [], name: 'coin.GAS' },
                        {
                            args: ['alice', 'bob', { decimal: '12.1' }],
                            name: 'coin.TRANSFER',
                        },
                    ],
                    name: 'test-verifier',
                    proof: 'test-proof',
                },
            ],
            nonce: 'test-nonce',
        });
    });
});
//# sourceMappingURL=composePactCommand.test.js.map