{"version": 3, "file": "composePactCommand.test.js", "sourceRoot": "", "sources": ["../../../src/composePactCommand/test/composePactCommand.test.ts"], "names": [], "mappings": ";;AAAA,mCAAyE;AACzE,iCAOkB;AAElB,qCAAuC;AACvC,qEAAkE;AAClE,8DAA2D;AAC3D,8CAA2C;AAC3C,sDAAmD;AACnD,wDAAqD;AAGrD,MAAM,IAAI,GAAU,IAAA,gBAAS,EAAC,MAAM,CAAC,CAAC;AAEtC,IAAA,iBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,MAAM,OAAO,GAAG,IAAA,cAAS,EACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CACnD,CAAC;QACF,IAAA,eAAM,EAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CACpC,oCAAoC,CACrC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,uBAAuB,EAAE,GAAG,EAAE;QAC/B,MAAM,OAAO,GAAG,IAAA,cAAS,EACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EACjD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAClD,CAAC;QACF,IAAA,eAAM,EAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CACpC,oEAAoE,CACrE,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAA,WAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC;YAC3B,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QACH,IAAA,eAAM,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YAC9B,IAAI,EAAE;gBACJ,MAAM,EAAE,GAAG;gBACX,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,EAAE;aACT;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,WAAE,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAS,EAAC,GAAG,EAAE;QACb,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sDAAsD,EAAE,GAAG,EAAE;QAC9D,MAAM,OAAO,GAAG,IAAA,uCAAkB,EAChC,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,EAC7D,IAAA,cAAS,EAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,UAAU,CAAC;YAC1B,cAAc,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;SACrE,CAAC,EACF,IAAA,aAAQ,EAAC,YAAY,CAAC,CACvB,EAAE,CAAC;QACJ,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE;iBACT;aACF;YACD,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE;wBACL,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;wBAC9B;4BACE,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;4BAC3C,IAAI,EAAE,eAAe;yBACtB;qBACF;oBACD,MAAM,EAAE,gBAAgB;oBACxB,MAAM,EAAE,SAAS;iBAClB;aACF;YACD,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,MAAM,OAAO,GAAG,IAAA,uCAAkB,EAChC,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,EAC7D,IAAA,cAAS,EAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,UAAU,CAAC;YAC1B,cAAc,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;SACrE,CAAC,EACF,IAAA,YAAO,EAAC;YACN,OAAO,EAAE,GAAG;YACZ,aAAa,EAAE,aAAa;YAC5B,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,GAAG;YACb,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,IAAI;SACV,CAAC,EACF,IAAA,iBAAY,EAAC,iBAAiB,CAAC,EAC/B,IAAA,aAAQ,EAAC,YAAY,CAAC,CACvB,EAAE,CAAC;QAEJ,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE;iBACT;aACF;YACD,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE;wBACL,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;wBAC9B;4BACE,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;4BAC3C,IAAI,EAAE,eAAe;yBACtB;qBACF;oBACD,MAAM,EAAE,gBAAgB;oBACxB,MAAM,EAAE,SAAS;iBAClB;aACF;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,YAAY,EAAE,GAAG;gBACjB,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE,aAAa;gBACrB,GAAG,EAAE,IAAI;aACV;YACD,SAAS,EAAE,iBAAiB;YAC5B,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;QACtD,MAAM,OAAO,GAAG,IAAA,uCAAkB,EAChC,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAC3D,EAAE,CAAC;QAEJ,IAAA,eAAM,EAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAA,eAAM,EACJ,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAC3D,EAAE,CAAC,OAAO,CACZ,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,IAAI,EAAE,oEAAoE;gBAC1E,IAAI,EAAE,EAAE;aACT;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;QAC/C,IAAA,eAAM,EACJ,IAAA,uCAAkB,EAChB,IAAA,cAAS,EACP,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAC/C,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAChD,EACD,IAAA,iBAAO,EAAC,KAAK,EAAE,MAAM,CAAC,EACtB,IAAA,iBAAO,EAAC,KAAK,EAAE,MAAM,CAAC,CACvB,EAAE,CAAC,OAAO,CACZ,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,IAAI,EAAE,oEAAoE;gBAC1E,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;aACnC;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,IAAA,eAAM,EACJ,GAAG,EAAE,CACH,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,iBAAY,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAC9B,EAAE,CAAC,OAAO,CACd,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;QAC/C,IAAA,eAAM,EACJ,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,cAAS,EAAC,gBAAgB,CAAC,CAC5B,EAAE,CAAC,OAAO,CACZ,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;QACnE,IAAA,eAAM,EACJ,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,cAAS,EAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,UAAU,CAAC;SAC3B,CAAC,EACF,IAAA,cAAS,EAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;SAClE,CAAC,CACH,EAAE,CAAC,OAAO,CACZ,CAAC,OAAO,CAAC;YACR;gBACE,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE;oBACL,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;oBAC9B,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE;iBACpE;aACF;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EACJ,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,cAAS,EAAC,gBAAgB,CAAC,EAC3B,IAAA,cAAS,EAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;SAClE,CAAC,CACH,EAAE,CAAC,OAAO,CACZ,CAAC,OAAO,CAAC;YACR;gBACE,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE;oBACL,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE;iBACpE;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,8DAA8D,EAAE,GAAG,EAAE;;QACtE,IAAA,eAAM,EACJ,MAAA,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,YAAO,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAC1B,EAAE,CAAC,IAAI,0CAAE,YAAY,CACvB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,yDAAyD,EAAE,GAAG,EAAE;QACjE,IAAA,eAAM,EACJ,IAAA,qCAAiB,EACf,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAC1D,IAAA,cAAS,EAAC,gBAAgB,CAAC,EAC3B,IAAA,cAAS,EAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;SAClE,CAAC,CACH,EAAE,CACJ,CACF,CAAC,OAAO,CAAC;YACR,GAAG,EAAE,4PAA4P;YACjQ,IAAI,EAAE,6CAA6C;YACnD,IAAI,EAAE,CAAC,SAAS,CAAC;SAClB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,MAAM,OAAO,GAAG,IAAA,uCAAkB,EAChC,IAAA,uCAAkB,EAChB,IAAA,cAAS,EAAC,QAAQ,CAAC,EACnB,IAAA,YAAO,EAAC,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CACnC,EACD,IAAA,uCAAkB,EAAC,IAAA,cAAS,EAAC,UAAU,CAAC,EAAE,IAAA,YAAO,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EACpE,IAAA,YAAO,EAAC;YACN,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;YACX,YAAY,EAAE,CAAC;YACf,GAAG,EAAE,CAAC;SACP,CAAC,CACH,EAAE,CAAC;QAEJ,IAAA,eAAM,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YAC9B,IAAI,EAAE;gBACJ,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,EAAE;aACT;SACF,CAAC,CAAC;QACH,IAAA,eAAM,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAC3B,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,GAAG;YACZ,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;YACX,YAAY,EAAE,CAAC;YACf,GAAG,EAAE,CAAC;SACP,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;QAC7C,IAAA,eAAM,EACJ,IAAA,uCAAkB,EAChB,IAAA,iBAAY,EAAC;YACX,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,KAAK;YACf,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;SACzB,CAAC,EACF,IAAA,iBAAO,EAAC,KAAK,EAAE,MAAM,CAAC,CACvB,EAAE,CAAC,OAAO,CACZ,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE;oBACJ,GAAG,EAAE,MAAM;oBACX,MAAM,EAAE,MAAM;iBACf;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAA,iBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,IAAA,eAAM,EACJ,IAAA,2BAAY,EAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,CACvE,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,EAAE;aACT;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,IAAA,eAAM,EACJ,IAAA,2BAAY,EACV,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,EAClD,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CACnD,CACF,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE;oBACJ,GAAG,EAAE,MAAM;oBACX,GAAG,EAAE,MAAM;iBACZ;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,6DAA6D,EAAE,GAAG,EAAE;QACrE,IAAA,eAAM,EACJ,IAAA,2BAAY,EACV,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,EAClD,SAAS,CACV,CACF,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QAE9D,IAAA,eAAM,EACJ,IAAA,2BAAY,EAAC,SAAS,EAAE;YACtB,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;SAC/C,CAAC,CACH,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,qBAAqB,EAAE,GAAG,EAAE;QAC7B,IAAA,eAAM,EACJ,IAAA,2BAAY,EACV,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,EACnC,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CACjD,CACF,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;aACnC;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EACJ,IAAA,2BAAY,EAAC,SAAS,EAAE;YACtB,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;SAC/C,CAAC,CACH,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,IAAA,eAAM,EACJ,IAAA,2BAAY,EACV,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,EACnD,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CACjD,CACF,CAAC,OAAO,CAAC;YACR,IAAI,EAAE;gBACJ,MAAM,EAAE,GAAG;gBACX,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;aACnC;SACF,CAAC,CAAC;QAEH,IAAA,eAAM,EACJ,IAAA,2BAAY,EAAC,SAAS,EAAE;YACtB,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;SAC/C,CAAC,CACH,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,IAAA,eAAM,EAAC,GAAG,EAAE,CACV,IAAA,2BAAY,EAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CACpE,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,uFAAuF,EAAE,GAAG,EAAE;;QAC/F,WAAE,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAEzD,MAAM,WAAW,GAAG,IAAA,uCAAkB,EAAC;YACrC,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAA0B;SAC/C,CAAC,EAAE,CAAC;QACL,IAAA,eAAM,EAAC,MAAA,WAAW,CAAC,IAAI,0CAAE,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAExD,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,OAAO,GAAG,IAAA,uCAAkB,EAChC,IAAA,cAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,EAC7D,IAAA,yBAAW,EACT,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,EAAE,EAC9C,CAAC,aAAa,EAAE,EAAE,CAAC;YACjB,aAAa,CAAC,UAAU,CAAC;YACzB,aAAa,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;SACpE,CACF,EACD,IAAA,aAAQ,EAAC,YAAY,CAAC,CACvB,EAAE,CAAC;QACJ,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE;iBACT;aACF;YACD,OAAO,EAAE,EAAE;YACX,SAAS,EAAE;gBACT;oBACE,KAAK,EAAE;wBACL,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;wBAC9B;4BACE,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;4BAC3C,IAAI,EAAE,eAAe;yBACtB;qBACF;oBACD,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,YAAY;iBACpB;aACF;YACD,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';\nimport {\n  addSigner,\n  continuation,\n  execution,\n  setMeta,\n  setNetworkId,\n  setNonce,\n} from '../../fp';\nimport type { IPactCommand } from '../../interfaces/IPactCommand';\nimport { getModule } from '../../pact';\nimport { createTransaction } from '../../utils/createTransaction';\nimport { composePactCommand } from '../composePactCommand';\nimport { addData } from '../utils/addData';\nimport { addVerifier } from '../utils/addVerifier';\nimport { mergePayload } from '../utils/patchCommand';\nimport type { ICoin } from './coin-contract';\n\nconst coin: ICoin = getModule('coin');\n\ndescribe('execution', () => {\n  it('returns a payload object of a exec command', () => {\n    const command = execution(\n      coin.transfer('alice', 'bob', { decimal: '12.1' }),\n    );\n    expect(command.payload.exec.code).toBe(\n      '(coin.transfer \"alice\" \"bob\" 12.1)',\n    );\n  });\n\n  it('adds multiple command', () => {\n    const command = execution(\n      coin.transfer('alice', 'bob', { decimal: '0.1' }),\n      coin.transfer('bob', 'alice', { decimal: '0.1' }),\n    );\n    expect(command.payload.exec.code).toBe(\n      '(coin.transfer \"alice\" \"bob\" 0.1)(coin.transfer \"bob\" \"alice\" 0.1)',\n    );\n  });\n});\n\ndescribe('continuation', () => {\n  it('returns a payload object of a cont command', () => {\n    const command = continuation({\n      pactId: '1',\n      proof: 'test-proof',\n      step: 1,\n      rollback: false,\n    });\n    expect(command.payload).toEqual({\n      cont: {\n        pactId: '1',\n        proof: 'test-proof',\n        rollback: false,\n        step: 1,\n        data: {},\n      },\n    });\n  });\n});\n\ndescribe('composePactCommand', () => {\n  beforeEach(() => {\n    vi.useFakeTimers().setSystemTime(new Date('2023-07-27'));\n  });\n\n  afterEach(() => {\n    vi.useRealTimers();\n  });\n\n  it('returns command object with signers and capabilities', () => {\n    const command = composePactCommand(\n      execution(coin.transfer('alice', 'bob', { decimal: '12.1' })),\n      addSigner('bob_public_key', (withCapability) => [\n        withCapability('coin.GAS'),\n        withCapability('coin.TRANSFER', 'alice', 'bob', { decimal: '12.1' }),\n      ]),\n      setNonce('test-nonce'),\n    )();\n    expect(command).toStrictEqual({\n      payload: {\n        exec: {\n          code: '(coin.transfer \"alice\" \"bob\" 12.1)',\n          data: {},\n        },\n      },\n      signers: [\n        {\n          clist: [\n            { args: [], name: 'coin.GAS' },\n            {\n              args: ['alice', 'bob', { decimal: '12.1' }],\n              name: 'coin.TRANSFER',\n            },\n          ],\n          pubKey: 'bob_public_key',\n          scheme: 'ED25519',\n        },\n      ],\n      nonce: 'test-nonce',\n    });\n  });\n\n  it('returns a command based on ICommand interface', () => {\n    const command = composePactCommand(\n      execution(coin.transfer('alice', 'bob', { decimal: '12.1' })),\n      addSigner('bob_public_key', (withCapability) => [\n        withCapability('coin.GAS'),\n        withCapability('coin.TRANSFER', 'alice', 'bob', { decimal: '12.1' }),\n      ]),\n      setMeta({\n        chainId: '1',\n        senderAccount: 'gas-station',\n        gasPrice: 381,\n        gasLimit: 400,\n        creationTime: 123,\n        ttl: 1000,\n      }),\n      setNetworkId('test-network-id'),\n      setNonce('test-nonce'),\n    )();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: {\n          code: '(coin.transfer \"alice\" \"bob\" 12.1)',\n          data: {},\n        },\n      },\n      signers: [\n        {\n          clist: [\n            { args: [], name: 'coin.GAS' },\n            {\n              args: ['alice', 'bob', { decimal: '12.1' }],\n              name: 'coin.TRANSFER',\n            },\n          ],\n          pubKey: 'bob_public_key',\n          scheme: 'ED25519',\n        },\n      ],\n      meta: {\n        chainId: '1',\n        creationTime: 123,\n        gasLimit: 400,\n        gasPrice: 381,\n        sender: 'gas-station',\n        ttl: 1000,\n      },\n      networkId: 'test-network-id',\n      nonce: 'test-nonce',\n    });\n  });\n\n  it('adds kjs nonce if not presented in the input', () => {\n    const command = composePactCommand(\n      execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n    )();\n\n    expect(command.nonce).toBe('kjs:nonce:*************');\n  });\n\n  it('merges payload if they are exec', () => {\n    expect(\n      composePactCommand(\n        execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n        execution(coin.transfer('alice', 'bob', { decimal: '1' })),\n      )().payload,\n    ).toEqual({\n      exec: {\n        code: '(coin.transfer \"bob\" \"alice\" 1.0)(coin.transfer \"alice\" \"bob\" 1.0)',\n        data: {},\n      },\n    });\n  });\n\n  it('merges payloads data if they are exec', () => {\n    expect(\n      composePactCommand(\n        execution(\n          coin.transfer('bob', 'alice', { decimal: '1' }),\n          coin.transfer('alice', 'bob', { decimal: '1' }),\n        ),\n        addData('one', 'test'),\n        addData('two', 'test'),\n      )().payload,\n    ).toEqual({\n      exec: {\n        code: '(coin.transfer \"bob\" \"alice\" 1.0)(coin.transfer \"alice\" \"bob\" 1.0)',\n        data: { one: 'test', two: 'test' },\n      },\n    });\n  });\n\n  it('throws exception if payloads are not mergable', () => {\n    expect(\n      () =>\n        composePactCommand(\n          execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n          continuation({ pactId: '1' }),\n        )().payload,\n    ).toThrowError(new Error('PAYLOAD_NOT_MERGEABLE'));\n  });\n\n  it('accepts a signer without a capability', () => {\n    expect(\n      composePactCommand(\n        execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n        addSigner('bob_public_key'),\n      )().signers,\n    ).toEqual([{ pubKey: 'bob_public_key', scheme: 'ED25519' }]);\n  });\n\n  it('merges capability arrays of one signer if presented twice', () => {\n    expect(\n      composePactCommand(\n        execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n        addSigner('bob_public_key', (withCapability) => [\n          withCapability('coin.GAS'),\n        ]),\n        addSigner('bob_public_key', (withCapability) => [\n          withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '1' }),\n        ]),\n      )().signers,\n    ).toEqual([\n      {\n        pubKey: 'bob_public_key',\n        scheme: 'ED25519',\n        clist: [\n          { args: [], name: 'coin.GAS' },\n          { args: ['bob', 'alice', { decimal: '1' }], name: 'coin.TRANSFER' },\n        ],\n      },\n    ]);\n\n    expect(\n      composePactCommand(\n        execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n        addSigner('bob_public_key'),\n        addSigner('bob_public_key', (withCapability) => [\n          withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '1' }),\n        ]),\n      )().signers,\n    ).toEqual([\n      {\n        pubKey: 'bob_public_key',\n        scheme: 'ED25519',\n        clist: [\n          { args: ['bob', 'alice', { decimal: '1' }], name: 'coin.TRANSFER' },\n        ],\n      },\n    ]);\n  });\n  it(\"adds creationTime if it's not presented in the meta property\", () => {\n    expect(\n      composePactCommand(\n        execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n        setMeta({ chainId: '1' }),\n      )().meta?.creationTime,\n    ).toBe(1690416000);\n  });\n\n  it('returns transaction object by calling createTransaction', () => {\n    expect(\n      createTransaction(\n        composePactCommand(\n          execution(coin.transfer('bob', 'alice', { decimal: '1' })),\n          addSigner('bob_public_key'),\n          addSigner('bob_public_key', (withCapability) => [\n            withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '1' }),\n          ]),\n        )(),\n      ),\n    ).toEqual({\n      cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bob\\\\\" \\\\\"alice\\\\\" 1.0)\",\"data\":{}}},\"signers\":[{\"pubKey\":\"bob_public_key\",\"scheme\":\"ED25519\",\"clist\":[{\"name\":\"coin.TRANSFER\",\"args\":[\"bob\",\"alice\",{\"decimal\":\"1\"}]}]}],\"nonce\":\"kjs:nonce:*************\"}',\n      hash: 'XjFto2SijaGZpRzdwWdZDkPI7WheTUuIMs8DHaqL2jU',\n      sigs: [undefined],\n    });\n  });\n\n  it('adds does not set sender if its not presented', () => {\n    const command = composePactCommand(\n      composePactCommand(\n        execution('(test)'),\n        setMeta({ senderAccount: 'test' }),\n      ),\n      composePactCommand(execution('(test 2)'), setMeta({ chainId: '1' })),\n      setMeta({\n        gasLimit: 1,\n        gasPrice: 1,\n        creationTime: 0,\n        ttl: 1,\n      }),\n    )();\n\n    expect(command.payload).toEqual({\n      exec: {\n        code: '(test)(test 2)',\n        data: {},\n      },\n    });\n    expect(command.meta).toEqual({\n      sender: 'test',\n      chainId: '1',\n      gasLimit: 1,\n      gasPrice: 1,\n      creationTime: 0,\n      ttl: 1,\n    });\n  });\n  it('merge data if they are continuation', () => {\n    expect(\n      composePactCommand(\n        continuation({\n          pactId: '1',\n          step: 1,\n          rollback: false,\n          proof: null,\n          data: { direct: 'test' },\n        }),\n        addData('one', 'test'),\n      )().payload,\n    ).toEqual({\n      cont: {\n        pactId: '1',\n        step: 1,\n        rollback: false,\n        proof: null,\n        data: {\n          one: 'test',\n          direct: 'test',\n        },\n      },\n    });\n  });\n});\n\ndescribe('mergePayload', () => {\n  it('merge code part of two payload', () => {\n    expect(\n      mergePayload({ exec: { code: '(one)' } }, { exec: { code: '(two)' } }),\n    ).toEqual({\n      exec: {\n        code: '(one)(two)',\n        data: {},\n      },\n    });\n  });\n\n  it('merge data part of two payload', () => {\n    expect(\n      mergePayload(\n        { exec: { code: '(one)', data: { one: 'test' } } },\n        { exec: { code: '(two)', data: { two: 'test' } } },\n      ),\n    ).toEqual({\n      exec: {\n        code: '(one)(two)',\n        data: {\n          one: 'test',\n          two: 'test',\n        },\n      },\n    });\n  });\n\n  it('returns the non-undefined if one of the inputs is undefined', () => {\n    expect(\n      mergePayload(\n        { exec: { code: '(one)', data: { one: 'test' } } },\n        undefined,\n      ),\n    ).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });\n\n    expect(\n      mergePayload(undefined, {\n        exec: { code: '(one)', data: { one: 'test' } },\n      }),\n    ).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });\n  });\n\n  it('returns merged data', () => {\n    expect(\n      mergePayload(\n        { cont: { data: { one: 'test' } } },\n        { cont: { pactId: '1', data: { two: 'test' } } },\n      ),\n    ).toEqual({\n      cont: {\n        pactId: '1',\n        data: { one: 'test', two: 'test' },\n      },\n    });\n\n    expect(\n      mergePayload(undefined, {\n        exec: { code: '(one)', data: { one: 'test' } },\n      }),\n    ).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });\n  });\n\n  it('should not override input data', () => {\n    expect(\n      mergePayload(\n        { cont: { proof: 'proof', data: { one: 'test' } } },\n        { cont: { pactId: '1', data: { two: 'test' } } },\n      ),\n    ).toEqual({\n      cont: {\n        pactId: '1',\n        proof: 'proof',\n        data: { one: 'test', two: 'test' },\n      },\n    });\n\n    expect(\n      mergePayload(undefined, {\n        exec: { code: '(one)', data: { one: 'test' } },\n      }),\n    ).toEqual({ exec: { code: '(one)', data: { one: 'test' } } });\n  });\n\n  it('throws error if object are not the same brand', () => {\n    expect(() =>\n      mergePayload({ exec: { code: 'test' } }, { cont: { pactId: '1' } }),\n    ).toThrowError(new Error('PAYLOAD_NOT_MERGEABLE'));\n  });\n\n  it('adds creationTime to metadata of mataData is presented but does not have creationTime', () => {\n    vi.useFakeTimers().setSystemTime(new Date('2023-07-27'));\n\n    const pactCommand = composePactCommand({\n      meta: { chainId: '1' } as IPactCommand['meta'],\n    })();\n    expect(pactCommand.meta?.creationTime).toBe(1690416000);\n\n    vi.useRealTimers();\n  });\n\n  it('returns command object with verifiers and capabilities', () => {\n    const command = composePactCommand(\n      execution(coin.transfer('alice', 'bob', { decimal: '12.1' })),\n      addVerifier(\n        { name: 'test-verifier', proof: 'test-proof' },\n        (forCapability) => [\n          forCapability('coin.GAS'),\n          forCapability('coin.TRANSFER', 'alice', 'bob', { decimal: '12.1' }),\n        ],\n      ),\n      setNonce('test-nonce'),\n    )();\n    expect(command).toStrictEqual({\n      payload: {\n        exec: {\n          code: '(coin.transfer \"alice\" \"bob\" 12.1)',\n          data: {},\n        },\n      },\n      signers: [],\n      verifiers: [\n        {\n          clist: [\n            { args: [], name: 'coin.GAS' },\n            {\n              args: ['alice', 'bob', { decimal: '12.1' }],\n              name: 'coin.TRANSFER',\n            },\n          ],\n          name: 'test-verifier',\n          proof: 'test-proof',\n        },\n      ],\n      nonce: 'test-nonce',\n    });\n  });\n});\n"]}