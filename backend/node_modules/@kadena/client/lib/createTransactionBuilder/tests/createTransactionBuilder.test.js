"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const pact_1 = require("../../pact");
const createTransactionBuilder_1 = require("../createTransactionBuilder");
const coin = (0, pact_1.getModule)('coin');
(0, vitest_1.describe)('createTransactionBuilder', () => {
    (0, vitest_1.beforeEach)(() => {
        vitest_1.vi.useFakeTimers().setSystemTime(new Date('2023-07-27'));
    });
    (0, vitest_1.afterEach)(() => {
        vitest_1.vi.useRealTimers();
    });
    (0, vitest_1.it)('returns exec payload', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [],
            nonce: 'kjs:nonce:1690416000000',
        });
    });
    (0, vitest_1.it)('returns cont payload', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .continuation({
            pactId: '1',
            proof: 'proof',
            rollback: false,
            step: 1,
        })
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                cont: {
                    pactId: '1',
                    proof: 'proof',
                    data: {},
                    rollback: false,
                    step: 1,
                },
            },
            signers: [],
            nonce: 'kjs:nonce:1690416000000',
        });
    });
    (0, vitest_1.it)('returns command with signers', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .addSigner('bob_pubkey', (withCapability) => [
            withCapability('coin.GAS'),
            withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '12' }),
        ])
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [
                {
                    clist: [
                        { args: [], name: 'coin.GAS' },
                        {
                            args: ['bob', 'alice', { decimal: '12' }],
                            name: 'coin.TRANSFER',
                        },
                    ],
                    pubKey: 'bob_pubkey',
                    scheme: 'ED25519',
                },
            ],
            nonce: 'kjs:nonce:1690416000000',
        });
    });
    (0, vitest_1.it)('returns command with verifiers', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .addVerifier({ name: 'test-verifier', proof: 'test-proof' }, (forCapability) => [
            forCapability('coin.GAS'),
            forCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '12' }),
        ])
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [],
            verifiers: [
                {
                    name: 'test-verifier',
                    proof: 'test-proof',
                    clist: [
                        { args: [], name: 'coin.GAS' },
                        {
                            args: ['bob', 'alice', { decimal: '12' }],
                            name: 'coin.TRANSFER',
                        },
                    ],
                },
            ],
            nonce: 'kjs:nonce:1690416000000',
        });
    });
    (0, vitest_1.it)('returns command with meta', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .setMeta({ chainId: '0' })
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [],
            meta: {
                chainId: '0',
                creationTime: 1690416000,
                gasLimit: 2500,
                gasPrice: 1e-8,
                sender: '',
                ttl: 28800,
            },
            nonce: 'kjs:nonce:1690416000000',
        });
    });
    (0, vitest_1.it)('returns command with custom nonce', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .setNonce('test-nonce')
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [],
            nonce: 'test-nonce',
        });
    });
    (0, vitest_1.it)('returns command with custom nonce by using nonce generator', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .setNonce((cmd) => {
            return `test-nonce:${cmd.payload.exec.code}`;
        })
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [],
            nonce: 'test-nonce:(coin.transfer "bob" "alice" 12.0)',
        });
    });
    (0, vitest_1.it)('returns command with network', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .setNetworkId('mainnet01')
            .getCommand();
        (0, vitest_1.expect)(command).toStrictEqual({
            payload: {
                exec: { code: '(coin.transfer "bob" "alice" 12.0)', data: {} },
            },
            signers: [],
            networkId: 'mainnet01',
            nonce: 'kjs:nonce:1690416000000',
        });
    });
    (0, vitest_1.it)('returns unsigned transaction', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const unSignedTr = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .addSigner('bob_bup_key')
            .setNetworkId('mainnet01')
            .createTransaction();
        (0, vitest_1.expect)(unSignedTr).toStrictEqual({
            cmd: '{"payload":{"exec":{"code":"(coin.transfer \\"bob\\" \\"alice\\" 12.0)","data":{}}},"nonce":"kjs:nonce:1690416000000","signers":[{"pubKey":"bob_bup_key","scheme":"ED25519"}],"networkId":"mainnet01"}',
            hash: '_sjzo-mOADRp7XEXIupbNHTGrKzbm-A3fKZhtzXcwxM',
            sigs: [undefined],
        });
    });
    (0, vitest_1.it)('returns exec command with data', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .addData('test', 'value')
            .getCommand();
        (0, vitest_1.expect)(command).toEqual({
            nonce: 'kjs:nonce:1690416000000',
            payload: {
                exec: {
                    code: '(coin.transfer "bob" "alice" 12.0)',
                    data: { test: 'value' },
                },
            },
            signers: [],
        });
    });
    (0, vitest_1.it)('returns cont command with data', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .continuation({ pactId: '1', rollback: false, step: 1 })
            .addData('test', 'value')
            .getCommand();
        (0, vitest_1.expect)(command).toEqual({
            nonce: 'kjs:nonce:1690416000000',
            payload: {
                cont: {
                    pactId: '1',
                    data: { test: 'value' },
                    rollback: false,
                    proof: null,
                    step: 1,
                },
            },
            signers: [],
        });
    });
    (0, vitest_1.it)('returns command with keyset', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)();
        const command = builder
            .execution(coin.transfer('bob', 'alice', { decimal: '12' }))
            .addKeyset('ks', 'keys-all', 'pub1', 'pub2')
            .getCommand();
        (0, vitest_1.expect)(command).toEqual({
            nonce: 'kjs:nonce:1690416000000',
            payload: {
                exec: {
                    code: '(coin.transfer "bob" "alice" 12.0)',
                    data: {
                        ks: {
                            pred: 'keys-all',
                            keys: ['pub1', 'pub2'],
                        },
                    },
                },
            },
            signers: [],
        });
    });
    (0, vitest_1.it)('accepts initials and merges them to the final result', () => {
        const builder = (0, createTransactionBuilder_1.createTransactionBuilder)({
            networkId: 'my-network',
            meta: {
                chainId: '0',
                sender: 'sender',
                gasLimit: 1,
                gasPrice: 1,
                ttl: 1,
                creationTime: 1,
            },
        });
        const command = builder.execution('(+ 1 1)').getCommand();
        (0, vitest_1.expect)(command).toEqual({
            meta: {
                chainId: '0',
                creationTime: 1,
                gasLimit: 1,
                gasPrice: 1,
                sender: 'sender',
                ttl: 1,
            },
            networkId: 'my-network',
            nonce: 'kjs:nonce:1690416000000',
            payload: { exec: { code: '(+ 1 1)', data: {} } },
            signers: [],
        });
    });
});
//# sourceMappingURL=createTransactionBuilder.test.js.map