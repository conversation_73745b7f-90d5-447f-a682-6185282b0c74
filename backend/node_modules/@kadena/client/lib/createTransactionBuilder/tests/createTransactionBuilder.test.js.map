{"version": 3, "file": "createTransactionBuilder.test.js", "sourceRoot": "", "sources": ["../../../src/createTransactionBuilder/tests/createTransactionBuilder.test.ts"], "names": [], "mappings": ";;AAAA,mCAAyE;AAGzE,qCAAuC;AACvC,0EAAuE;AAEvE,MAAM,IAAI,GAAU,IAAA,gBAAS,EAAC,MAAM,CAAC,CAAC;AAEtC,IAAA,iBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAA,mBAAU,EAAC,GAAG,EAAE;QACd,WAAE,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAS,EAAC,GAAG,EAAE;QACb,WAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sBAAsB,EAAE,GAAG,EAAE;QAC9B,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sBAAsB,EAAE,GAAG,EAAE;QAC9B,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,YAAY,CAAC;YACZ,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,CAAC;SACR,CAAC;aACD,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE,GAAG;oBACX,KAAK,EAAE,OAAO;oBACd,IAAI,EAAE,EAAE;oBACR,QAAQ,EAAE,KAAK;oBACf,IAAI,EAAE,CAAC;iBACR;aACF;YACD,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,SAAS,CAAC,YAAY,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;YAC3C,cAAc,CAAC,UAAU,CAAC;YAC1B,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACnE,CAAC;aACD,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE;wBACL,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;wBAC9B;4BACE,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;4BACzC,IAAI,EAAE,eAAe;yBACtB;qBACF;oBACD,MAAM,EAAE,YAAY;oBACpB,MAAM,EAAE,SAAS;iBAClB;aACF;YACD,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,WAAW,CACV,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,EAAE,EAC9C,CAAC,aAAa,EAAE,EAAE,CAAC;YACjB,aAAa,CAAC,UAAU,CAAC;YACzB,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SAClE,CACF;aACA,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE,EAAE;YACX,SAAS,EAAE;gBACT;oBACE,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,YAAY;oBACnB,KAAK,EAAE;wBACL,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;wBAC9B;4BACE,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;4BACzC,IAAI,EAAE,eAAe;yBACtB;qBACF;iBACF;aACF;YACD,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;aACzB,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE,EAAE;YACX,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,EAAE;gBACV,GAAG,EAAE,KAAK;aACX;YACD,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAA,WAAE,EAAC,mCAAmC,EAAE,GAAG,EAAE;QAC3C,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,QAAQ,CAAC,YAAY,CAAC;aACtB,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,4DAA4D,EAAE,GAAG,EAAE;QACpE,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE;YAChB,OAAO,cACJ,GAAG,CAAC,OAAmC,CAAC,IAAI,CAAC,IAChD,EAAE,CAAC;QACL,CAAC,CAAC;aACD,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,+CAA+C;SACvD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,YAAY,CAAC,WAAW,CAAC;aACzB,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC;YAC5B,OAAO,EAAE;gBACP,IAAI,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE,IAAI,EAAE,EAAE,EAAE;aAC/D;YACD,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,WAAW;YACtB,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO;aACvB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,SAAS,CAAC,aAAa,CAAC;aACxB,YAAY,CAAC,WAAW,CAAC;aACzB,iBAAiB,EAAE,CAAC;QAEvB,IAAA,eAAM,EAAC,UAAU,CAAC,CAAC,aAAa,CAAC;YAC/B,GAAG,EAAE,wMAAwM;YAC7M,IAAI,EAAE,6CAA6C;YACnD,IAAI,EAAE,CAAC,SAAS,CAAC;SAClB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC;aACxB,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACtB,KAAK,EAAE,yBAAyB;YAChC,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;iBACxB;aACF;YACD,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,YAAY,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;aACvD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC;aACxB,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACtB,KAAK,EAAE,yBAAyB;YAChC,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE,GAAG;oBACX,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;oBACvB,QAAQ,EAAE,KAAK;oBACf,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,CAAC;iBACR;aACF;YACD,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,OAAO,GAAG,IAAA,mDAAwB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO;aACpB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3D,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;aAC3C,UAAU,EAAE,CAAC;QAEhB,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACtB,KAAK,EAAE,yBAAyB;YAChC,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE;wBACJ,EAAE,EAAE;4BACF,IAAI,EAAE,UAAU;4BAChB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;yBACvB;qBACF;iBACF;aACF;YACD,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,WAAE,EAAC,sDAAsD,EAAE,GAAG,EAAE;QAC9D,MAAM,OAAO,GAAG,IAAA,mDAAwB,EAAC;YACvC,SAAS,EAAE,YAAY;YACvB,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;gBACX,GAAG,EAAE,CAAC;gBACN,YAAY,EAAE,CAAC;aAChB;SACF,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,CAAC;QAE1D,IAAA,eAAM,EAAC,OAAO,CAAC,CAAC,OAAO,CAAC;YACtB,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG;gBACZ,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,QAAQ;gBAChB,GAAG,EAAE,CAAC;aACP;YACD,SAAS,EAAE,YAAY;YACvB,KAAK,EAAE,yBAAyB;YAChC,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;YAChD,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';\nimport type { ICoin } from '../../composePactCommand/test/coin-contract';\nimport type { IExecutionPayloadObject } from '../../interfaces/IPactCommand';\nimport { getModule } from '../../pact';\nimport { createTransactionBuilder } from '../createTransactionBuilder';\n\nconst coin: ICoin = getModule('coin');\n\ndescribe('createTransactionBuilder', () => {\n  beforeEach(() => {\n    vi.useFakeTimers().setSystemTime(new Date('2023-07-27'));\n  });\n\n  afterEach(() => {\n    vi.useRealTimers();\n  });\n\n  it('returns exec payload', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [],\n      nonce: 'kjs:nonce:1690416000000',\n    });\n  });\n\n  it('returns cont payload', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .continuation({\n        pactId: '1',\n        proof: 'proof',\n        rollback: false,\n        step: 1,\n      })\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        cont: {\n          pactId: '1',\n          proof: 'proof',\n          data: {},\n          rollback: false,\n          step: 1,\n        },\n      },\n      signers: [],\n      nonce: 'kjs:nonce:1690416000000',\n    });\n  });\n\n  it('returns command with signers', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .addSigner('bob_pubkey', (withCapability) => [\n        withCapability('coin.GAS'),\n        withCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '12' }),\n      ])\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [\n        {\n          clist: [\n            { args: [], name: 'coin.GAS' },\n            {\n              args: ['bob', 'alice', { decimal: '12' }],\n              name: 'coin.TRANSFER',\n            },\n          ],\n          pubKey: 'bob_pubkey',\n          scheme: 'ED25519',\n        },\n      ],\n      nonce: 'kjs:nonce:1690416000000',\n    });\n  });\n\n  it('returns command with verifiers', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .addVerifier(\n        { name: 'test-verifier', proof: 'test-proof' },\n        (forCapability) => [\n          forCapability('coin.GAS'),\n          forCapability('coin.TRANSFER', 'bob', 'alice', { decimal: '12' }),\n        ],\n      )\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [],\n      verifiers: [\n        {\n          name: 'test-verifier',\n          proof: 'test-proof',\n          clist: [\n            { args: [], name: 'coin.GAS' },\n            {\n              args: ['bob', 'alice', { decimal: '12' }],\n              name: 'coin.TRANSFER',\n            },\n          ],\n        },\n      ],\n      nonce: 'kjs:nonce:1690416000000',\n    });\n  });\n\n  it('returns command with meta', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .setMeta({ chainId: '0' })\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [],\n      meta: {\n        chainId: '0',\n        creationTime: 1690416000,\n        gasLimit: 2500,\n        gasPrice: 1e-8,\n        sender: '',\n        ttl: 28800,\n      },\n      nonce: 'kjs:nonce:1690416000000',\n    });\n  });\n  it('returns command with custom nonce', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .setNonce('test-nonce')\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [],\n      nonce: 'test-nonce',\n    });\n  });\n\n  it('returns command with custom nonce by using nonce generator', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .setNonce((cmd) => {\n        return `test-nonce:${\n          (cmd.payload as IExecutionPayloadObject).exec.code\n        }`;\n      })\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [],\n      nonce: 'test-nonce:(coin.transfer \"bob\" \"alice\" 12.0)',\n    });\n  });\n\n  it('returns command with network', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .setNetworkId('mainnet01')\n      .getCommand();\n\n    expect(command).toStrictEqual({\n      payload: {\n        exec: { code: '(coin.transfer \"bob\" \"alice\" 12.0)', data: {} },\n      },\n      signers: [],\n      networkId: 'mainnet01',\n      nonce: 'kjs:nonce:1690416000000',\n    });\n  });\n\n  it('returns unsigned transaction', () => {\n    const builder = createTransactionBuilder();\n    const unSignedTr = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .addSigner('bob_bup_key')\n      .setNetworkId('mainnet01')\n      .createTransaction();\n\n    expect(unSignedTr).toStrictEqual({\n      cmd: '{\"payload\":{\"exec\":{\"code\":\"(coin.transfer \\\\\"bob\\\\\" \\\\\"alice\\\\\" 12.0)\",\"data\":{}}},\"nonce\":\"kjs:nonce:1690416000000\",\"signers\":[{\"pubKey\":\"bob_bup_key\",\"scheme\":\"ED25519\"}],\"networkId\":\"mainnet01\"}',\n      hash: '_sjzo-mOADRp7XEXIupbNHTGrKzbm-A3fKZhtzXcwxM',\n      sigs: [undefined],\n    });\n  });\n\n  it('returns exec command with data', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .addData('test', 'value')\n      .getCommand();\n\n    expect(command).toEqual({\n      nonce: 'kjs:nonce:1690416000000',\n      payload: {\n        exec: {\n          code: '(coin.transfer \"bob\" \"alice\" 12.0)',\n          data: { test: 'value' },\n        },\n      },\n      signers: [],\n    });\n  });\n\n  it('returns cont command with data', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .continuation({ pactId: '1', rollback: false, step: 1 })\n      .addData('test', 'value')\n      .getCommand();\n\n    expect(command).toEqual({\n      nonce: 'kjs:nonce:1690416000000',\n      payload: {\n        cont: {\n          pactId: '1',\n          data: { test: 'value' },\n          rollback: false,\n          proof: null,\n          step: 1,\n        },\n      },\n      signers: [],\n    });\n  });\n\n  it('returns command with keyset', () => {\n    const builder = createTransactionBuilder();\n    const command = builder\n      .execution(coin.transfer('bob', 'alice', { decimal: '12' }))\n      .addKeyset('ks', 'keys-all', 'pub1', 'pub2')\n      .getCommand();\n\n    expect(command).toEqual({\n      nonce: 'kjs:nonce:1690416000000',\n      payload: {\n        exec: {\n          code: '(coin.transfer \"bob\" \"alice\" 12.0)',\n          data: {\n            ks: {\n              pred: 'keys-all',\n              keys: ['pub1', 'pub2'],\n            },\n          },\n        },\n      },\n      signers: [],\n    });\n  });\n\n  it('accepts initials and merges them to the final result', () => {\n    const builder = createTransactionBuilder({\n      networkId: 'my-network',\n      meta: {\n        chainId: '0',\n        sender: 'sender',\n        gasLimit: 1,\n        gasPrice: 1,\n        ttl: 1,\n        creationTime: 1,\n      },\n    });\n    const command = builder.execution('(+ 1 1)').getCommand();\n\n    expect(command).toEqual({\n      meta: {\n        chainId: '0',\n        creationTime: 1,\n        gasLimit: 1,\n        gasPrice: 1,\n        sender: 'sender',\n        ttl: 1,\n      },\n      networkId: 'my-network',\n      nonce: 'kjs:nonce:1690416000000',\n      payload: { exec: { code: '(+ 1 1)', data: {} } },\n      signers: [],\n    });\n  });\n});\n"]}