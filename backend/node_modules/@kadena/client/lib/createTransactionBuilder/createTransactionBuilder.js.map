{"version": 3, "file": "createTransactionBuilder.js", "sourceRoot": "", "sources": ["../../src/createTransactionBuilder/createTransactionBuilder.ts"], "names": [], "mappings": ";;;AACA,8DAU+B;AAG/B,yEAAsE;AACtE,2EAAwE;AAcxE,kEAA+D;AA6Q/D,MAAM,eAAe,GAAG,CAAC,IAAyB,EAAoB,EAAE;IACtE,IAAI,OAAO,GAAG,IAAA,uCAAkB,EAAC,IAAI,CAAC,CAAC;IAEvC,OAAO;QACL,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE;YACrB,OAAO,GAAG,IAAA,uCAAkB,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC;QACjB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAI,IAAyB,EAAe,EAAE;IAC/D,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,OAAO,GAAgB;QAC3B,OAAO,EAAE,CAAC,GAAW,EAAE,KAAqB,EAAE,EAAE;YAC9C,KAAK,CAAC,WAAW,CAAC,IAAA,4BAAO,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;YACvC,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,SAAS,EAAE,CAAC,GAAW,EAAE,IAAY,EAAE,GAAG,UAAoB,EAAE,EAAE;YAChE,KAAK,CAAC,WAAW,CAAC,IAAA,8BAAS,EAAC,GAAG,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC;YACvD,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,SAAS,EAAE,CAAC,MAAM,EAAE,GAAa,EAAE,EAAE;YACnC,KAAK,CAAC,WAAW,CACf,IAAA,8BAAS,EACP,MAAM,EACN,GAAqD,CACD,CACvD,CAAC;YACF,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,WAAW,EAAE,CAAC,QAAmB,EAAE,GAAa,EAAE,EAAE;YAClD,KAAK,CAAC,WAAW,CACf,IAAA,yBAAW,EACT,QAAQ,EACR,GAAqD,CACD,CACvD,CAAC;YACF,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAChB,KAAK,CAAC,WAAW,CAAC,IAAA,4BAAO,EAAC,IAAI,CAAC,CAAC,CAAC;YACjC,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,YAAY,EAAE,CAAC,EAAU,EAAE,EAAE;YAC3B,KAAK,CAAC,WAAW,CAAC,IAAA,iCAAY,EAAC,EAAE,CAAC,CAAC,CAAC;YACpC,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,QAAQ,EAAE,CAAC,GAAoD,EAAE,EAAE;YACjE,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE;gBACxB,MAAM,KAAK,GAAG,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBACzD,OAAO,IAAA,2BAAY,EAAC,GAAG,EAAE,IAAA,6BAAQ,EAAC,KAAK,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,UAAU,EAAE,GAAG,EAAE;YACf,OAAO,KAAK,CAAC,QAAQ,CAAC,EAAE,CAA0B,CAAC;QACrD,CAAC;QACD,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAA,qCAAiB,EAAC,OAAO,CAAC,UAAU,EAAE,CAAC;KACjE,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CACtC,OAA6B,EACR,EAAE;IACvB,OAAO;QACL,SAAS,EAAE,CAAC,GAAG,eAAyB,EAAE,EAAE;YAC1C,MAAM,IAAI,GAAG,OAAO;gBAClB,CAAC,CAAC,IAAA,2BAAY,EAAC,OAAO,EAAE,IAAA,8BAAS,EAAC,GAAG,eAAe,CAAC,CAAC;gBACtD,CAAC,CAAC,IAAA,8BAAS,EAAC,GAAG,eAAe,CAAC,CAAC;YAElC,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QACD,YAAY,EAAE,CAAC,WAA+C,EAAE,EAAE;YAChE,MAAM,gBAAgB,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,WAAW,EAAE,CAAC;YACzD,MAAM,IAAI,GAAG,OAAO;gBAClB,CAAC,CAAC,IAAA,2BAAY,EAAC,OAAO,EAAE,IAAA,iCAAY,EAAC,gBAAgB,CAAC,CAAC;gBACvD,CAAC,CAAC,IAAA,iCAAY,EAAC,gBAAgB,CAAC,CAAC;YAEnC,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,wBAAwB,4BAoBnC", "sourcesContent": ["import type { ICap, IExecPayload, IUnsignedCommand } from '@kadena/types';\nimport {\n  addData,\n  addKeyset,\n  addSigner,\n  composePactCommand,\n  continuation,\n  execution,\n  setMeta,\n  setNetworkId,\n  setNonce,\n} from '../composePactCommand';\nimport type { ValidDataTypes } from '../composePactCommand/utils/addData';\nimport type { IVerifier } from '../composePactCommand/utils/addVerifier';\nimport { addVerifier } from '../composePactCommand/utils/addVerifier';\nimport { patchCommand } from '../composePactCommand/utils/patchCommand';\nimport type { AddCapabilities } from '../composePactCommand/utils/payload';\nimport type {\n  BuiltInPredicate,\n  IContinuationPayloadObject,\n  IPactCommand,\n  IPartialPactCommand,\n  ISigner,\n} from '../interfaces/IPactCommand';\nimport type {\n  ExtractCapabilityType,\n  IGeneralCapability,\n  WithRequired,\n} from '../interfaces/type-utilities';\nimport { createTransaction } from '../utils/createTransaction';\n\ninterface IAddSigner<TCommand> {\n  /**\n   * Add signer without capability\n   */\n  (first: ISigner | ISigner[]): IBuilder<TCommand>;\n  /**\n   * Add a signer including capabilities. The withCapability function is obtained from\n   * the function you call in the execution part.\n   * @example\n   * Pact.builder.execute(\n   *   Pact.coin.transfer(\"alice\", \"bob\", \\{ decimal:\"1\" \\})\n   * ).addSigner(\"public_key\", (withCapability) =\\> [\n   *   withCapability(\"coin.GAS\"),\n   *   withCapability(\"coin.TRANSFER\", \"alice\", \"bob\", \\{ decimal:\"1\" \\})\n   * ])\n   */\n  (\n    first: ISigner | ISigner[],\n    capability: (withCapability: ExtractCapabilityType<TCommand>) => ICap[],\n  ): IBuilder<TCommand>;\n}\n\ninterface IAddVerifier<TCommand> {\n  /**\n   * Add verifier without capability\n   */\n  (verifier: IVerifier): IBuilder<TCommand>;\n  /**\n   * Add a signer including capabilities. The withCapability function is obtained from\n   * the function you call in the execution part.\n   * @example\n   * Pact.builder.execute(\n   *   Pact.coin.transfer(\"alice\", \"bob\", \\{ decimal:\"1\" \\})\n   * ).addVerifier(\\{ name:\"name\", proof:\"proof\" \\}, (withCapability) =\\> [\n   *   withCapability(\"coin.GAS\"),\n   *   withCapability(\"coin.TRANSFER\", \"alice\", \"bob\", \\{ decimal:\"1\" \\})\n   * ])\n   */\n  (\n    verifier: IVerifier,\n    capability: (withCapability: ExtractCapabilityType<TCommand>) => ICap[],\n  ): IBuilder<TCommand>;\n}\n\ninterface ISetNonce<TCommand> {\n  /**\n   * Overriding the default nonce by calling this function\n   */\n  (nonce: string): IBuilder<TCommand>;\n  /**\n   * Overriding the default nonce by calling this function. The `nonceGenerator` function will receive the command object\n   * and should return the nonce as a string.\n   */\n  (nonceGenerator: (cmd: IPartialPactCommand) => string): IBuilder<TCommand>;\n}\n\ninterface IAddKeyset<TCommand> {\n  <TKey extends string, PRED extends BuiltInPredicate>(\n    key: TKey,\n    pred: PRED,\n    ...publicKeys: string[]\n  ): IBuilder<TCommand>;\n\n  <TKey extends string, PRED extends string>(\n    key: TKey,\n    pred: PRED,\n    ...publicKeys: string[]\n  ): IBuilder<TCommand>;\n}\n/**\n * The interface of the return value `Pact.builder.execution` or `Pact.builder.continuation`\n *\n * @see {@link IPact}\n * @public\n */\nexport interface IBuilder<TCommand> {\n  /**\n   * Add verifier with theirs capabilities\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   // add verifier without scoping to any capabilities\n   *   .addVerifier(\\{ name:\"bridge\", proof:\"proof\" \\})\n   *   // add verifier without scoping to the capabilities\n   *   .addVerifier(\\{ name:\"zk\", proof:\"proof\" \\},()=>[\n   *       withCapability(\"coin.GAS\"),\n   *       withCapability(\"myModule.CAP\",\"arg1\",{ decimal: 2 })\n   *    ])\n   * ```\n   */\n  addVerifier: IAddVerifier<TCommand>;\n\n  /**\n   * Add signer with theirs capabilities\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   // add signer without scoping to any capabilities\n   *   .addSigner(\"public_key\")\n   *   // add signer without scoping to the capabilities\n   *   .addSigner(\"gas_payer_public_key\",()=>[\n   *       withCapability(\"coin.GAS\"),\n   *       withCapability(\"myModule.CAP\",\"arg1\",{ decimal: 2 })\n   *    ])\n   * ```\n   */\n  addSigner: IAddSigner<TCommand>;\n\n  /**\n   * Set meta data\n   *\n   * @param meta - includes sender parameter which is the account address of the gas payer\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   // select the chain and gas_payer_account\n   *   .setMeta({\n   *     chainId: '0',\n   *     sender: 'gas_payer_account',\n   *   })\n   * ```\n   */\n  setMeta: (\n    meta: Partial<Omit<IPactCommand['meta'], 'sender'>> & {\n      senderAccount?: string;\n    },\n  ) => IBuilder<TCommand>;\n  /**\n   * Set nonce\n   *\n   * if its not presented the commandBuilder uses the default nonce generator. `kjs:nonce:timestamp`\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   .setNonce('my-custom-nonce')\n   * ```\n   */\n  setNonce: ISetNonce<TCommand>;\n  /**\n   * Set network id\n   *\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   .setNetworkId('mainnet01')\n   * ```\n   */\n  setNetworkId: (id: string) => IBuilder<TCommand>;\n  /**\n   * add data\n   *\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   .addData('theKey','theValue')\n   * ```\n   */\n  addData: (key: string, data: ValidDataTypes) => IBuilder<TCommand>;\n  /**\n   * add keyset to the data part\n   *\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   .addKeyset('keysetName', 'keys-all', 'fist-public-key', 'second-public-key')\n   * ```\n   */\n  addKeyset: IAddKeyset<TCommand>;\n  /**\n   * finalizing the command and create the transaction object in `IUnsignedCommand` format\n   *\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"albert\"))\n   *   // select the chain and gas_payer_account\n   *   .setMeta({\n   *     chainId: '0',\n   *     sender: 'gas_payer_account',\n   *   })\n   *   // the sigs array has the same length as the signers array in the command but it fills all\n   *   // by undefined which then will be replaced by the final signatures by using the wallet helpers\n   *   .createTransaction(); // {cmd:\"stringified command\", hash:\"string-hash\", sigs:[undefined]}\n   * ```\n   */\n  createTransaction: () => IUnsignedCommand;\n\n  /**\n   * finalizing the command by adding all default values.\n   *\n   */\n  getCommand: () => Partial<IPactCommand>;\n}\n\n/**\n * @internal\n */\ninterface IExecution {\n  <\n    TCodes extends Array<\n      | (string & {\n          capability(name: string, ...args: unknown[]): ICap;\n        })\n      | string\n    >,\n  >(\n    ...codes: [...TCodes]\n  ): IBuilder<{\n    payload: IExecPayload & { funs: AddCapabilities<[...TCodes]> };\n  }>;\n}\n\n/**\n * @internal\n */\ninterface IContinuation {\n  (\n    options: WithRequired<\n      IContinuationPayloadObject['cont'],\n      'pactId' | 'rollback' | 'step'\n    >,\n  ): IBuilder<{\n    payload: IContinuationPayloadObject;\n  }>;\n}\n\n/**\n * @public\n */\nexport interface ITransactionBuilder {\n  /**\n   * create execution command\n   *\n   * @example\n   * ```\n   * Pact.builder\n   *   .execution(Pact.modules.coin.transfer(\"bob\",\"alice\", {decimal:\"10\"}))\n   * ```\n   */\n  execution: IExecution;\n  /**\n   * create continuation command\n   * @example\n   * ```\n   * Pact.builder\n   *   .continuation({ pactId:\"id\", proof:\"spv_proof\", rollback: false , step:1 , data:{} })\n   * ```\n   */\n  continuation: IContinuation;\n}\n\ninterface IStatefulCompose {\n  composeWith: (\n    patch:\n      | IPartialPactCommand\n      | ((cmd: IPartialPactCommand) => IPartialPactCommand),\n  ) => void;\n  readonly finalize: (command: IPartialPactCommand) => IPartialPactCommand;\n}\n\nconst statefulCompose = (init: IPartialPactCommand): IStatefulCompose => {\n  let reducer = composePactCommand(init);\n\n  return {\n    composeWith: (patch) => {\n      reducer = composePactCommand(reducer, patch);\n    },\n    get finalize() {\n      return reducer;\n    },\n  };\n};\n\nconst getBuilder = <T>(init: IPartialPactCommand): IBuilder<T> => {\n  const state = statefulCompose(init);\n  const builder: IBuilder<T> = {\n    addData: (key: string, value: ValidDataTypes) => {\n      state.composeWith(addData(key, value));\n      return builder;\n    },\n    addKeyset: (key: string, pred: string, ...publicKeys: string[]) => {\n      state.composeWith(addKeyset(key, pred, ...publicKeys));\n      return builder;\n    },\n    addSigner: (pubKey, cap?: unknown) => {\n      state.composeWith(\n        addSigner(\n          pubKey,\n          cap as (withCapability: IGeneralCapability) => ICap[],\n        ) as (cmd: IPartialPactCommand) => IPartialPactCommand,\n      );\n      return builder;\n    },\n\n    addVerifier: (verifier: IVerifier, cap?: unknown) => {\n      state.composeWith(\n        addVerifier(\n          verifier,\n          cap as (withCapability: IGeneralCapability) => ICap[],\n        ) as (cmd: IPartialPactCommand) => IPartialPactCommand,\n      );\n      return builder;\n    },\n    setMeta: (meta) => {\n      state.composeWith(setMeta(meta));\n      return builder;\n    },\n    setNetworkId: (id: string) => {\n      state.composeWith(setNetworkId(id));\n      return builder;\n    },\n    setNonce: (arg: string | ((cmd: IPartialPactCommand) => string)) => {\n      state.composeWith((cmd) => {\n        const nonce = typeof arg === 'function' ? arg(cmd) : arg;\n        return patchCommand(cmd, setNonce(nonce));\n      });\n      return builder;\n    },\n    getCommand: () => {\n      return state.finalize({}) as Partial<IPactCommand>;\n    },\n    createTransaction: () => createTransaction(builder.getCommand()),\n  };\n  return builder;\n};\n\n/**\n * returns a new instance of command builder\n * @param initial - the initial command\n *\n * @public\n */\nexport const createTransactionBuilder = (\n  initial?: IPartialPactCommand,\n): ITransactionBuilder => {\n  return {\n    execution: (...pactExpressions: string[]) => {\n      const init = initial\n        ? patchCommand(initial, execution(...pactExpressions))\n        : execution(...pactExpressions);\n\n      return getBuilder(init);\n    },\n    continuation: (contOptions: IContinuationPayloadObject['cont']) => {\n      const contWithDefaults = { proof: null, ...contOptions };\n      const init = initial\n        ? patchCommand(initial, continuation(contWithDefaults))\n        : continuation(contWithDefaults);\n\n      return getBuilder(init);\n    },\n  };\n};\n"]}