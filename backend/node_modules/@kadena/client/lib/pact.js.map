{"version": 3, "file": "pact.js", "sourceRoot": "", "sources": ["../src/pact.ts"], "names": [], "mappings": ";;;AAAA,2CAA2C;AAE3C,kGAA+F;AAC/F,uDAAsD;AACtD,+DAA4D;AAiB5D;;GAEG;AACH,8DAA8D;AACvD,MAAM,SAAS,GAAG,CAAC,IAAY,EAAO,EAAE;IAC7C,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,8DAA8D;IAC9D,MAAM,EAAE,GAAQ,IAAI,KAAK,CAAM,cAAa,CAAQ,EAAE;QACpD,GAAG,CAAC,MAAM,EAAE,IAAY;YACtB,8BAA8B;YAC9B,IAAI,IAAI,KAAK,SAAS;gBAAE,OAAO,EAAE,CAAC;YAClC,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI;YACzB,MAAM,GAAG,GAAG,IAAA,6BAAc,EACxB,IAAA,kBAAS,EAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,mCAAgB,CAAC,CAAC,CAC/C,CAAC;YACF,IAAI,GAAG,IAAI,CAAC;YACZ,OAAO,GAAG,CAAC;QACb,CAAC;KACF,CAAC,CAAC;IACH,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAnBW,QAAA,SAAS,aAmBpB;AAEF,MAAM,WAAW,GAAG,GAAU,EAAE;IAC9B,8DAA8D;IAC9D,OAAO,IAAI,KAAK,CACd,EAAE,EACF;QACE,GAAG,CAAC,MAAM,EAAE,IAAY;YACtB,OAAO,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC;QACzB,CAAC;KACF,CACF,CAAC;AACJ,CAAC,CAAC;AAEF;;;GAGG;AACU,QAAA,IAAI,GAAU;IACzB;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,WAAW,EAAE,CAAC;IACvB,CAAC;IACD;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAA,mDAAwB,GAAE,CAAC;IACpC,CAAC;CACF,CAAC", "sourcesContent": ["import { createExp } from '@kadena/pactjs';\nimport type { ITransactionBuilder } from './createTransactionBuilder/createTransactionBuilder';\nimport { createTransactionBuilder } from './createTransactionBuilder/createTransactionBuilder';\nimport { unpackLiterals } from './utils/pact-helpers';\nimport { parseAsPactValue } from './utils/parseAsPactValue';\n\n/**\n * Interface that represents the generated Pact modules\n * @public\n */\nexport interface IPactModules {}\n\n/**\n * Interface that represents the Pact object\n * @public\n */\nexport interface IPact {\n  modules: IPactModules;\n  builder: ITransactionBuilder;\n}\n\n/**\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const getModule = (name: string): any => {\n  let code = name;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const pr: any = new Proxy<any>(function () {} as any, {\n    get(target, path: string) {\n      // dont add depact to the code\n      if (path === 'defpact') return pr;\n      code = `${code}.${path}`;\n      return pr;\n    },\n    apply(target, thisArg, args) {\n      const exp = unpackLiterals(\n        createExp(code, ...args.map(parseAsPactValue)),\n      );\n      code = name;\n      return exp;\n    },\n  });\n  return pr;\n};\n\nconst pactCreator = (): IPact => {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return new Proxy<any>(\n    {},\n    {\n      get(target, path: string) {\n        return getModule(path);\n      },\n    },\n  );\n};\n\n/**\n * The wrapper object that provides the Transaction builder and Contract interface\n * @public\n */\nexport const Pact: IPact = {\n  /**\n   * Generated modules\n   */\n  get modules() {\n    return pactCreator();\n  },\n  /**\n   * Transaction builder\n   */\n  get builder() {\n    return createTransactionBuilder();\n  },\n};\n"]}