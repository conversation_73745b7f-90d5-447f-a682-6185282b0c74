"""
Solana 代币兑换视图
"""

import logging
from decimal import Decimal
from typing import Dict, Any
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from common.exceptions import APIError
from common.views import AsyncAPIView
from .services.swap import SolanaSwapService

logger = logging.getLogger(__name__)

class SwapQuoteView(AsyncAPIView):
    """代币兑换报价视图"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.swap_service = SolanaSwapService()
    
    async def get(self, request, wallet_id: str):
        """获取代币兑换报价
        
        Args:
            request: 请求对象
            wallet_id: 钱包 ID
            
        Returns:
            Response: 报价信息
        """
        try:
            # 获取请求参数
            device_id = request.query_params.get('device_id')
            from_token = request.query_params.get('from_token')
            to_token = request.query_params.get('to_token')
            amount = request.query_params.get('amount')
            slippage = request.query_params.get('slippage')
            
            # 验证参数
            if not all([device_id, from_token, to_token, amount]):
                raise APIError("缺少必要参数", code="MISSING_PARAMS")
            
            # 获取钱包地址
            from_address = request.user.wallet.address
            
            # 转换滑点
            slippage_decimal = None
            if slippage:
                try:
                    slippage_decimal = Decimal(slippage)
                except Exception as e:
                    logger.warning(f"滑点转换失败: {str(e)}")
            
            # 获取报价
            quote = await self.swap_service.get_quote(
                from_token=from_token,
                to_token=to_token,
                amount=amount,
                from_address=from_address,
                slippage=slippage_decimal
            )
            
            return Response({
                "status": "success",
                "data": quote
            })
            
        except APIError as e:
            logger.error(f"获取报价失败: {str(e)}")
            return Response({
                "status": "error",
                "message": str(e),
                "code": e.code
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"获取报价失败: {str(e)}")
            return Response({
                "status": "error",
                "message": f"获取报价失败: {str(e)}",
                "code": "QUOTE_FAILED"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 