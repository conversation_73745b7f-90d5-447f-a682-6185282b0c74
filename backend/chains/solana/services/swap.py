"""
Solana 代币兑换服务
基于 Jupiter API 实现代币兑换功能
"""

import aiohttp
import asyncio
import json
import logging
import time
from decimal import Decimal
from typing import Dict, Any, List, Optional
from solana.rpc.api import Client
from solana.rpc.commitment import Commitment
from solders.pubkey import Pubkey as PublicKey
from solders.transaction import Transaction
from solders.system_program import TransferParams, transfer
from common.config import Config
import os
from base58 import b58decode

logger = logging.getLogger(__name__)

class SwapError(Exception):
    """兑换错误"""
    pass

class InsufficientBalanceError(SwapError):
    """余额不足错误"""
    pass

class SolanaSwapService:
    """Solana 代币兑换服务"""

    def __init__(self):
        """初始化 Solana 代币兑换服务"""
        # 初始化 Solana RPC 客户端
        config = Config.get_solana_config("SOL")
        self.client = Client(config["rpc_url"])

        self.session = None
        # Jupiter API 端点
        self.jup_api_urls = [
            "https://quote-api.jup.ag/v6",  # 报价API
            "https://public-api.birdeye.so/public",  # Birdeye API
            "https://token.jup.ag/strict"    # 代币列表API
        ]
        self.current_api_url_index = 0

        # SOL 代币信息
        self.sol_token_info = {
            'address': 'So11111111111111111111111111111111111111112',
            'name': 'Solana',
            'symbol': 'SOL',
            'decimals': 9,
            'logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png'
        }

        # 设置 aiohttp 会话配置
        self.timeout = aiohttp.ClientTimeout(
            total=120,    # 总超时时间增加到2分钟
            connect=30,   # 连接超时时间
            sock_connect=30,
            sock_read=60  # 读取超时时间
        )

        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
        }

        self.max_retries = 5  # 增加重试次数

    async def _get_session(self):
        """获取或创建 aiohttp 会话"""
        if self.session is None or self.session.closed:
            # 创建 SSL 上下文，允许更宽松的连接
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # 创建连接器
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers=self.headers,
                connector=connector
            )
        return self.session

    async def _close_session(self):
        """关闭 aiohttp 会话"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _get_token_decimals(self, token_address: str) -> int:
        """获取代币精度"""
        if token_address == self.sol_token_info['address']:
            return self.sol_token_info['decimals']

        # 这里可以添加从其他API获取代币精度的逻辑
        # 暂时返回默认值
        return 6

    async def get_supported_tokens(self) -> List[Dict[str, Any]]:
        """获取支持的代币列表"""
        last_error = None
        session = None

        for retry in range(self.max_retries):
            try:
                if session is None or session.closed:
                    session = await self._get_session()

                # 从Jupiter API获取代币列表
                token_url = self.jup_api_urls[2]  # 使用代币API端点
                logger.debug(f"正在从 {token_url} 获取代币列表 (第 {retry + 1} 次尝试)")

                # 添加请求参数和额外的头部信息
                headers = {
                    **self.headers,
                    'Origin': 'https://jup.ag',
                    'Referer': 'https://jup.ag/'
                }

                async with session.get(token_url, headers=headers) as response:
                    if response.status == 200:
                        tokens = await response.json()
                        logger.info(f"成功获取 {len(tokens)} 个代币")

                        # 添加 SOL 代币到列表开头
                        result = [self.sol_token_info]
                        result.extend(tokens[:100])  # 限制返回前100个代币

                        return result
                    else:
                        error_text = await response.text()
                        last_error = f"HTTP {response.status}: {error_text}"
                        logger.warning(f"获取代币列表失败 (第 {retry + 1} 次尝试): {last_error}")

            except asyncio.TimeoutError:
                last_error = "请求超时"
                logger.warning(f"获取代币列表超时 (第 {retry + 1} 次尝试)")
            except Exception as e:
                last_error = str(e)
                logger.error(f"获取代币列表时发生错误 (第 {retry + 1} 次尝试): {last_error}")

            if retry < self.max_retries - 1:
                await asyncio.sleep(2 ** retry)  # 指数退避

        # 如果所有重试都失败，返回默认的代币列表
        logger.error(f"获取代币列表失败，返回默认列表。最后错误: {last_error}")
        return [self.sol_token_info]

    async def get_swap_quote(self, from_token: str, to_token: str, amount: Decimal, slippage: Optional[Decimal] = None) -> Dict[str, Any]:
        """获取兑换报价"""
        if slippage is None:
            slippage = Decimal('0.5')  # 默认0.5%滑点

        logger.info(f"开始获取兑换报价: {from_token} -> {to_token}, 数量: {amount}, 滑点: {slippage}")

        try:
            session = await self._get_session()

            # 获取代币精度并转换金额
            try:
                amount_decimal = Decimal(str(amount))
                decimals = await self._get_token_decimals(from_token)
                amount_in_units = int(amount_decimal * Decimal(10) ** decimals)
                amount_str = str(amount_in_units)

                logger.debug(f"金额转换: {amount_decimal} -> {amount_in_units} (精度: {decimals})")

            except Exception as e:
                logger.error(f"金额转换错误: 原始金额={amount}, 错误={str(e)}")
                raise SwapError(f"金额转换失败: {str(e)}")

            # 构建请求参数
            params = {
                'inputMint': from_token,
                'outputMint': to_token,
                'amount': amount_str,
                'slippageBps': str(int(slippage * 100)) if slippage else '50',
                'onlyDirectRoutes': 'false',
                'asLegacyTransaction': 'true',
                'platformFeeBps': '0'
            }

            url = f"{self.jup_api_urls[0]}/quote"

            logger.info(f"请求 Jupiter API: {url}")
            logger.debug(f"请求参数: {params}")

            async with session.get(url, params=params) as response:
                response_text = await response.text()
                logger.debug(f"Jupiter API 响应状态: {response.status}")

                if response.status == 200:
                    try:
                        quote_data = json.loads(response_text)
                        logger.debug(f"解析报价数据成功")

                        # 解析报价数据
                        in_amount = int(quote_data.get('inAmount', '0'))
                        out_amount = int(quote_data.get('outAmount', '0'))

                        # 获取输出代币精度
                        to_decimals = await self._get_token_decimals(to_token)

                        # 转换为可读格式
                        in_amount_formatted = Decimal(in_amount) / Decimal(10) ** decimals
                        out_amount_formatted = Decimal(out_amount) / Decimal(10) ** to_decimals

                        # 计算价格影响
                        price_impact = quote_data.get('priceImpactPct', '0')

                        result = {
                            'status': 'success',
                            'quote_id': json.dumps(quote_data),  # 保存完整的报价数据
                            'from_token': from_token,
                            'to_token': to_token,
                            'in_amount': str(in_amount_formatted),
                            'out_amount': str(out_amount_formatted),
                            'price_impact': price_impact,
                            'slippage': str(slippage),
                            'route_plan': quote_data.get('routePlan', []),
                            'other_amount_threshold': quote_data.get('otherAmountThreshold', '0'),
                            'swap_mode': quote_data.get('swapMode', 'ExactIn'),
                            'fees': quote_data.get('platformFee', {}),
                            'timestamp': int(time.time())
                        }

                        logger.info(f"获取报价成功: 输入 {in_amount_formatted} -> 输出 {out_amount_formatted}, 价格影响: {price_impact}%")
                        return result

                    except json.JSONDecodeError as e:
                        logger.error(f"解析 Jupiter API 响应失败: {str(e)}, 响应内容: {response_text[:500]}")
                        raise SwapError(f"解析报价数据失败: {str(e)}")
                else:
                    logger.error(f"Jupiter API 请求失败: HTTP {response.status}, 响应: {response_text[:500]}")
                    raise SwapError(f"获取报价失败: HTTP {response.status}")

        except SwapError:
            # 重新抛出 SwapError
            raise
        except Exception as e:
            logger.error(f"获取兑换报价时发生未知错误: {str(e)}")
            raise SwapError(f"获取兑换报价失败: {str(e)}")

    def is_valid_address(self, address: str) -> bool:
        """验证 Solana 地址格式
        
        Args:
            address: 要验证的地址
            
        Returns:
            bool: 地址是否有效
        """
        try:
            # 特殊处理 Solana 原生代币地址
            if address == self.sol_token_info['address']:
                return True
            
            # 检查地址长度
            if len(address) != 44:  # Solana 地址是 base58 编码的 32 字节
                return False
            
            # 尝试解码地址
            try:
                decoded = b58decode(address)
                return len(decoded) == 32  # Solana 地址应该是 32 字节
            except Exception:
                return False
                
        except Exception as e:
            logger.error(f"验证地址失败: {str(e)}")
            return False

    async def get_quote(self, from_token: str, to_token: str, amount: str, from_address: str, slippage: Optional[Decimal] = None) -> Dict[str, Any]:
        """获取代币兑换报价
        
        Args:
            from_token: 源代币地址
            to_token: 目标代币地址
            amount: 兑换数量
            from_address: 发送方地址
            slippage: 滑点容忍度
            
        Returns:
            Dict: 报价信息
        """
        try:
            logger.info(f"开始获取 {from_token} 到 {to_token} 的兑换报价")
            
            # 验证地址格式
            if not self.is_valid_address(from_token):
                raise SwapError(f"无效的源代币地址: {from_token}")
            if not self.is_valid_address(to_token):
                raise SwapError(f"无效的目标代币地址: {to_token}")
            if not self.is_valid_address(from_address):
                raise SwapError(f"无效的发送方地址: {from_address}")
            
            # 获取代币精度并转换金额
            try:
                amount_decimal = Decimal(amount)
                decimals = await self._get_token_decimals(from_token)
                amount_in_units = int(amount_decimal * Decimal(10) ** decimals)
                amount_str = str(amount_in_units)
                
                logger.debug(f"金额转换: {amount_decimal} -> {amount_in_units} (精度: {decimals})")
                
            except Exception as e:
                logger.error(f"金额转换错误: 原始金额={amount}, 错误={str(e)}")
                raise SwapError(f"金额转换失败: {str(e)}")
            
            # 构建请求参数
            params = {
                'inputMint': from_token,
                'outputMint': to_token,
                'amount': amount_str,
                'slippageBps': str(int(slippage * 100)) if slippage else '50',
                'onlyDirectRoutes': 'false',
                'asLegacyTransaction': 'true',
                'platformFeeBps': '0'
            }
            
            # 构建请求头
            headers = {
                **self.headers,
                'Origin': 'https://jup.ag',
                'Referer': 'https://jup.ag/'
            }
            
            # 构建请求 URL
            url = f"{self.jup_api_urls[0]}/quote"
            
            logger.info(f"请求 URL: {url}")
            logger.info(f"请求参数: {params}")
            logger.info(f"请求头: {headers}")
            
            # 获取会话
            session = await self._get_session()
            
            try:
                async with session.get(url, params=params, headers=headers) as response:
                    logger.info(f"响应状态: {response.status}")
                    logger.info(f"响应头: {response.headers}")
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Jupiter API 错误: {error_text}")
                        raise SwapError(f"Jupiter API 错误: {error_text}")
                    
                    try:
                        data = await response.json()
                        logger.info(f"响应数据: {json.dumps(data, indent=2)}")
                        
                        if not data:
                            raise SwapError("Jupiter API 返回空数据")
                        
                        # 添加 quote_id
                        data['quote_id'] = json.dumps(data)
                        
                        return data
                        
                    except json.JSONDecodeError as e:
                        error_text = await response.text()
                        logger.error(f"解析响应失败: {error_text}")
                        raise SwapError(f"解析响应失败: {str(e)}")
                        
            except aiohttp.ClientError as e:
                logger.error(f"请求失败: {str(e)}")
                raise SwapError(f"请求失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"获取报价失败: {str(e)}")
            raise SwapError(f"获取报价失败: {str(e)}")

    async def execute_swap(self, quote_id: str, from_token: str, to_token: str, amount: str, from_address: str, private_key: str, slippage: Optional[Decimal] = None) -> Dict[str, Any]:
        """执行代币兑换"""
        try:
            # 获取代币精度
            from_token_decimals = await self._get_token_decimals(from_token)

            # 正确转换金额
            amount_decimal = Decimal(str(amount))
            amount_in_units = int(amount_decimal * Decimal(10) ** from_token_decimals)

            logger.info(f"交易金额转换: 原始金额: {amount}, 代币精度: {from_token_decimals}, 转换后金额: {amount_in_units}")

            # 构建交易请求
            swap_request = {
                'quoteResponse': json.loads(quote_id),
                'userPublicKey': from_address,
                'wrapUnwrapSOL': True,
                'computeUnitPriceMicroLamports': 1000,
                'asLegacyTransaction': True
            }

            session = await self._get_session()
            url = f"{self.jup_api_urls[0]}/swap"

            # 发送交易请求，带重试机制
            last_error = None
            for retry in range(self.max_retries):
                try:
                    logger.info(f"发送 Jupiter swap 请求 (第 {retry + 1} 次尝试)")

                    async with session.post(url, json=swap_request) as response:
                        if response.status == 200:
                            swap_data = await response.json()

                            # 获取未签名的交易数据
                            swap_transaction = swap_data.get('swapTransaction')
                            if not swap_transaction:
                                raise SwapError("未获取到交易数据")

                            logger.info(f"获取到未签名交易，开始签名和提交...")

                            # 签名和提交交易
                            try:
                                signature = await self._sign_and_send_transaction(
                                    swap_transaction,
                                    private_key,
                                    from_address
                                )

                                logger.info(f"交易提交成功，签名: {signature}")

                                return {
                                    'status': 'success',
                                    'message': 'Swap executed and submitted successfully',
                                    'transaction_signature': str(signature),  # 转换为字符串
                                    'transaction_data': {
                                        'swapTransaction': swap_data.get('swapTransaction'),
                                        'lastValidBlockHeight': swap_data.get('lastValidBlockHeight'),
                                        'prioritizationFeeLamports': swap_data.get('prioritizationFeeLamports'),
                                        'computeUnitLimit': swap_data.get('computeUnitLimit'),
                                        'prioritizationType': swap_data.get('prioritizationType'),
                                        'simulationSlot': swap_data.get('simulationSlot'),
                                        'dynamicSlippageReport': swap_data.get('dynamicSlippageReport'),
                                        'simulationError': swap_data.get('simulationError'),
                                        'addressesByLookupTableAddress': swap_data.get('addressesByLookupTableAddress')
                                    },
                                    'solscan_url': f'https://solscan.io/tx/{str(signature)}',
                                    'explorer_url': f'https://explorer.solana.com/tx/{str(signature)}'
                                }

                            except Exception as e:
                                logger.error(f"签名或提交交易失败: {str(e)}")
                                # 即使签名失败，也返回交易数据供调试
                                return {
                                    'status': 'partial_success',
                                    'message': f'获取交易数据成功，但签名失败: {str(e)}',
                                    'transaction_data': {
                                        'swapTransaction': swap_data.get('swapTransaction'),
                                        'lastValidBlockHeight': swap_data.get('lastValidBlockHeight'),
                                        'prioritizationFeeLamports': swap_data.get('prioritizationFeeLamports'),
                                        'computeUnitLimit': swap_data.get('computeUnitLimit'),
                                        'prioritizationType': swap_data.get('prioritizationType'),
                                        'simulationSlot': swap_data.get('simulationSlot'),
                                        'dynamicSlippageReport': swap_data.get('dynamicSlippageReport'),
                                        'simulationError': swap_data.get('simulationError'),
                                        'addressesByLookupTableAddress': swap_data.get('addressesByLookupTableAddress')
                                    },
                                    'error': str(e),
                                    'manual_signing': {
                                        'description': '自动签名失败，可以使用以下交易数据手动签名',
                                        'transaction_base64': swap_data.get('swapTransaction'),
                                        'instructions': [
                                            '1. 复制上面的 transaction_base64 数据',
                                            '2. 使用 Phantom 钱包或其他 Solana 钱包导入并签名',
                                            '3. 或者检查私钥格式是否正确'
                                        ]
                                    }
                                }
                        else:
                            error_text = await response.text()
                            last_error = f"HTTP {response.status}: {error_text}"
                            logger.warning(f"Jupiter API 请求失败 (第 {retry + 1} 次尝试): {last_error}")

                except aiohttp.ClientError as e:
                    last_error = f"网络连接错误: {str(e)}"
                    logger.warning(f"网络连接失败 (第 {retry + 1} 次尝试): {last_error}")
                except asyncio.TimeoutError:
                    last_error = "请求超时"
                    logger.warning(f"请求超时 (第 {retry + 1} 次尝试)")
                except Exception as e:
                    last_error = str(e)
                    logger.error(f"执行兑换时发生错误 (第 {retry + 1} 次尝试): {last_error}")

                # 如果不是最后一次重试，等待后重试
                if retry < self.max_retries - 1:
                    wait_time = 2 ** retry  # 指数退避: 1s, 2s, 4s, 8s, 16s
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)

            # 所有重试都失败
            logger.error(f"所有重试都失败，最后错误: {last_error}")
            raise SwapError(f"执行兑换失败: {last_error}")

        except Exception as e:
            logger.error(f"执行兑换失败: {str(e)}")
            raise SwapError(f"执行兑换失败: {str(e)}")

    async def _sign_and_send_transaction(self, swap_transaction: str, private_key: str, from_address: str) -> str:
        """签名并提交交易到 Solana 网络 - 使用 solders 库"""
        try:
            import base64
            import base58
            # 使用正确的 solders 导入
            from solders.transaction import Transaction
            from solders.keypair import Keypair
            from solana.rpc.async_api import AsyncClient
            from solana.rpc.commitment import Commitment
            from solana.rpc.types import TxOpts

            logger.info(f"开始签名交易，钱包地址: {from_address}")

            # 解码交易数据
            transaction_bytes = base64.b64decode(swap_transaction)
            logger.info(f"解码交易数据成功，长度: {len(transaction_bytes)} 字节")

            # 获取最新的 blockhash 并签名交易 - 完全按照 old_backend 实现
            try:
                config = Config.get_solana_config("SOL")
                async_client = AsyncClient(config["rpc_url"])

                # 获取最新的 blockhash
                blockhash_response = await async_client.get_latest_blockhash()
                if not blockhash_response or not blockhash_response.value:
                    raise SwapError("获取最新 blockhash 失败")

                blockhash = blockhash_response.value.blockhash
                logger.info(f"获取到最新 blockhash: {blockhash}")

                # 使用最新的 solders 库处理交易
                try:
                    # 使用 VersionedTransaction 反序列化 Jupiter 交易
                    from solders.transaction import VersionedTransaction
                    transaction = VersionedTransaction.from_bytes(transaction_bytes)
                    logger.info(f"成功反序列化 VersionedTransaction")

                    # 创建密钥对
                    keypair = Keypair.from_base58_string(private_key)
                    logger.info(f"成功创建密钥对，公钥: {str(keypair.pubkey())}")

                    # 验证公钥是否匹配
                    if str(keypair.pubkey()) != from_address:
                        logger.warning(f"公钥不匹配: 期望 {from_address}, 实际 {str(keypair.pubkey())}")

                    # 获取消息并序列化进行签名
                    try:
                        # 使用 solders 0.26.0 版本的 API
                        from solders.signature import Signature
                        from solders.transaction import VersionedTransaction
                        
                        # 记录关键对象的类型和状态
                        logger.info(f"Keypair 类型: {type(keypair)}")
                        logger.info(f"Keypair 公钥: {str(keypair.pubkey())}")
                        logger.info(f"Message 类型: {type(transaction.message)}")
                        
                        # 获取消息字节
                        message_bytes = bytes(transaction.message)
                        logger.info(f"Message bytes 长度: {len(message_bytes)}")
                        logger.info(f"Message bytes 前32字节: {message_bytes[:32].hex()}")
                        
                        # 签名消息
                        signature = keypair.sign_message(message_bytes)
                        logger.info(f"签名类型: {type(signature)}")
                        logger.info(f"签名值: {str(signature)}")
                        
                        # 使用正确的 VersionedTransaction 构造方法
                        # 注意：这里直接使用 keypair 作为签名者
                        signed_transaction = VersionedTransaction(transaction.message, [keypair])
                        logger.info(f"已签名交易类型: {type(signed_transaction)}")
                        logger.info(f"构造已签名 VersionedTransaction 成功")

                        # 序列化交易
                        serialized_transaction = bytes(signed_transaction)
                        logger.info(f"序列化交易长度: {len(serialized_transaction)} 字节")
                        logger.info(f"序列化交易前32字节: {serialized_transaction[:32].hex()}")

                    except Exception as sign_error:
                        logger.error(f"签名失败: {str(sign_error)}")
                        logger.error(f"错误类型: {type(sign_error)}")
                        import traceback
                        logger.error(f"错误堆栈: {traceback.format_exc()}")
                        raise SwapError(f"签名失败: {str(sign_error)}")

                except Exception as e:
                    logger.error(f"处理交易失败: {str(e)}")
                    await async_client.close()
                    raise SwapError(f"处理交易失败: {str(e)}")

            except Exception as e:
                logger.error(f"获取 blockhash 或签名交易失败: {str(e)}")
                if 'async_client' in locals():
                    await async_client.close()
                raise SwapError(f"签名交易失败: {str(e)}")

            # 发送已签名的交易到 Solana 网络
            try:
                logger.info("开始提交已签名交易到 Solana 网络...")

                # 发送交易到网络
                response = await async_client.send_raw_transaction(
                    serialized_transaction,
                    opts=TxOpts(skip_preflight=True)
                )

                # 提取交易签名
                actual_signature = response.value if hasattr(response, 'value') else response
                logger.info(f"交易已发送，签名: {actual_signature}")

                # 等待交易确认
                max_retries = 30  # 最多等待30秒
                for attempt in range(max_retries):
                    try:
                        status = await async_client.get_signature_statuses([actual_signature])
                        if status and status.value and status.value[0]:
                            status_info = status.value[0]
                            if status_info.confirmation_status:
                                logger.info(f"交易确认成功: {actual_signature}, 状态: {status_info.confirmation_status}")
                                await async_client.close()
                                return actual_signature

                        # 等待1秒后重试
                        await asyncio.sleep(1)

                    except Exception as e:
                        logger.warning(f"检查交易状态失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(1)

                # 如果达到最大尝试次数仍未确认，返回签名但标记为 pending
                logger.warning(f"交易已提交但未在 {max_retries} 秒内确认: {actual_signature}")
                await async_client.close()
                return actual_signature

            except Exception as e:
                logger.error(f"提交交易失败: {str(e)}")
                if 'async_client' in locals():
                    await async_client.close()
                raise SwapError(f"提交交易失败: {str(e)}")

        except SwapError:
            raise
        except Exception as e:
            logger.error(f"签名和提交交易失败: {str(e)}")
            raise SwapError(f"签名和提交交易失败: {str(e)}")

    def get_token_prices(self, token_addresses: List[str]) -> Dict[str, Any]:
        """获取代币价格信息 (同步接口)"""
        try:
            import requests
            import time
            from decimal import Decimal
            from common.config import Config

            prices = {}
            session = requests.Session()
            
            # 获取 Moralis API 密钥
            moralis_api_key = Config.MORALIS_API_KEY
            if not moralis_api_key:
                logger.error("Moralis API key is not configured")
                return {address: {'price': "0.00", 'price_change_24h': "0.00"} for address in token_addresses}

            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json',
                'X-API-Key': moralis_api_key
            }

            # 处理每个代币
            for address in token_addresses:
                try:
                    # 使用 Moralis API 获取代币价格
                    url = f"https://solana-gateway.moralis.io/token/mainnet/{address}/price"
                    response = session.get(url, headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data and isinstance(data, dict):
                            price = float(data.get('usdPrice', 0))
                            price_change = float(data.get('usdPrice24hrPercentChange', 0))
                            
                            prices[address] = {
                                'price': f"{price:.2f}",
                                'price_change_24h': f"{price_change:.2f}"
                            }
                            logger.info(f"获取到代币 {address} 价格: ${price}, 24h变化: {price_change}%")
                    else:
                        logger.warning(f"获取代币 {address} 价格失败: HTTP {response.status_code}")
                        prices[address] = {
                            'price': "0.00",
                            'price_change_24h': "0.00"
                        }
                    
                    # 添加延迟以避免请求过快
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"处理代币 {address} 价格时出错: {str(e)}")
                    prices[address] = {
                        'price': "0.00",
                        'price_change_24h': "0.00"
                    }

            return prices

        except Exception as e:
            logger.error(f"获取代币价格失败: {str(e)}")
            # 发生错误时返回空价格
            return {address: {'price': "0.00", 'price_change_24h': "0.00"} for address in token_addresses}

    def get_swap_history(self, wallet_address: str) -> List[Dict[str, Any]]:
        """获取钱包的交换历史"""
        try:
            # 这里可以集成实际的 DEX API 来获取交换历史
            # 暂时返回空列表
            return []
        except Exception as e:
            logger.error(f"获取交换历史失败: {str(e)}")
            raise SwapError(f"获取交换历史失败: {str(e)}")

    # 保持与原有接口的兼容性
    def get_swap_quote_legacy(self,
                      token_in: str,
                      token_out: str,
                      amount_in: str) -> Dict[str, Any]:
        """获取代币交换报价 (兼容性接口)"""
        return self.get_quote(
            wallet_id="",
            device_id="",
            from_token=token_in,
            to_token=token_out,
            amount=amount_in
        )

    def execute_swap_legacy(self,
                    token_in: str,
                    token_out: str,
                    amount_in: str,
                    wallet_address: str,
                    private_key: str) -> Dict[str, Any]:
        """执行代币交换 (兼容性接口)"""
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 首先获取报价
            quote = self.get_quote("", "", token_in, token_out, amount_in)

            # 执行兑换
            async def execute_with_cleanup():
                try:
                    result = await self.execute_swap(
                        quote_id=quote['quote_id'],
                        from_token=token_in,
                        to_token=token_out,
                        amount=amount_in,
                        from_address=wallet_address,
                        private_key=private_key
                    )
                    return result
                finally:
                    await self._close_session()

            result = loop.run_until_complete(execute_with_cleanup())
            return result

        finally:
            loop.close()