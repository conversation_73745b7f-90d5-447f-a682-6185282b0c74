"""
Solana 转账服务
"""

import logging
import time
from typing import Dict, Any, List, Optional
from decimal import Decimal
from common.config import Config
from solana.rpc.api import Client
from solana.rpc.commitment import Commitment
from typing import cast
# 使用正确的导入路径（基于 solana==0.25.1）
try:
    from solana.transaction import Transaction as SolanaTransaction
    from solana.keypair import Keypair
    from solana.publickey import PublicKey
    from solana.system_program import TransferParams, transfer
    from solana.blockhash import Blockhash
    from solana.rpc.types import TxOpts
    # 尝试导入 Message
    try:
        from solana.message import Message
    except ImportError:
        try:
            from solders.message import Message
        except ImportError:
            Message = None
    SOLANA_IMPORTS_OK = True
except ImportError:
    # 如果上面的导入失败，尝试使用 solders
    try:
        from solders.transaction import Transaction as SolanaTransaction
        from solders.keypair import Keypair
        from solders.pubkey import Pubkey as PublicKey
        from solders.system_program import TransferParams, transfer
        from solders.hash import Hash as Blockhash
        from solders.message import Message
        TxOpts = None  # solders 可能没有这个
        SOLANA_IMPORTS_OK = True
    except ImportError:
        SOLANA_IMPORTS_OK = False
        Message = None

# SPL Token 相关导入
try:
    from spl.token.instructions import create_associated_token_account, transfer_checked, TransferCheckedParams
    from spl.token.constants import TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID
    SPL_IMPORTS_OK = True
except ImportError:
    SPL_IMPORTS_OK = False
import base58

logger = logging.getLogger(__name__)


class SolanaTransferService:
    """Solana 转账服务"""

    def __init__(self):
        self.config = Config()

        # 检查必要的导入
        if not SOLANA_IMPORTS_OK:
            raise ImportError("Solana 库导入失败，请检查 solana 或 solders 包是否正确安装")

        if not SPL_IMPORTS_OK:
            logger.warning("SPL Token 库导入失败，SPL Token 转账功能将不可用")

    def _get_client(self) -> Client:
        """获取 Solana 客户端"""
        config = self.config.get_solana_config("SOL")
        return Client(config['rpc_url'])

    def _get_keypair_from_private_key(self, private_key: str) -> Keypair:
        """从私钥创建 Keypair"""
        try:
            # 如果私钥是 base58 编码的
            if len(private_key) > 64:
                secret_key = base58.b58decode(private_key)
                return Keypair.from_bytes(secret_key)
            else:
                # 如果私钥是十六进制编码的
                secret_key = bytes.fromhex(private_key)
                return Keypair.from_bytes(secret_key)
        except Exception as e:
            raise ValueError(f"无效的私钥格式: {str(e)}")

    def _get_public_key(self, keypair: Keypair):
        """获取 Keypair 的公钥（兼容不同版本）"""
        if hasattr(keypair, 'public_key'):
            return keypair.public_key
        elif hasattr(keypair, 'pubkey'):
            return keypair.pubkey()
        else:
            raise Exception("无法获取 Keypair 的公钥")

    def _get_associated_token_address(self, wallet_address: str, token_address: str) -> str:
        """计算关联代币账户地址"""
        try:
            # 使用正确的方法创建 PublicKey
            if hasattr(PublicKey, 'from_string'):
                wallet_pubkey = PublicKey.from_string(wallet_address)
                token_pubkey = PublicKey.from_string(token_address)
            else:
                wallet_pubkey = PublicKey(wallet_address)
                token_pubkey = PublicKey(token_address)

            # 使用 find_program_address 查找关联代币账户地址
            seeds = [
                bytes(wallet_pubkey),
                bytes(TOKEN_PROGRAM_ID),
                bytes(token_pubkey)
            ]
            ata, _ = PublicKey.find_program_address(seeds, ASSOCIATED_TOKEN_PROGRAM_ID)
            return str(ata)
        except Exception as e:
            logger.error(f"计算关联代币账户地址失败: {str(e)}")
            raise Exception(f"Failed to get associated token account: {str(e)}")

    def _check_token_account_exists(self, account_address: str) -> bool:
        """检查代币账户是否存在"""
        try:
            client = self._get_client()
            if hasattr(PublicKey, 'from_string'):
                pubkey = PublicKey.from_string(account_address)
            else:
                pubkey = PublicKey(account_address)
            response = client.get_account_info(pubkey)
            return response.value is not None
        except Exception as e:
            logger.error(f"检查代币账户失败: {str(e)}")
            return False

    def _get_token_decimals(self, token_address: str) -> int:
        """获取代币精度"""
        try:
            client = self._get_client()
            if hasattr(PublicKey, 'from_string'):
                pubkey = PublicKey.from_string(token_address)
            else:
                pubkey = PublicKey(token_address)
            response = client.get_token_supply(pubkey)
            if response.value:
                return response.value.decimals
            return 9  # 默认精度
        except Exception as e:
            logger.error(f"获取代币精度失败: {str(e)}")
            return 9  # 默认精度

    def transfer_sol(self, from_address: str, to_address: str, amount: str, private_key: str) -> Dict[str, Any]:
        """转账 SOL 原生代币"""
        try:
            logger.info(f"开始 SOL 转账: {from_address} -> {to_address}, 金额: {amount}")
            
            # 验证金额
            try:
                amount_decimal = Decimal(amount)
                if amount_decimal <= 0:
                    raise ValueError("转账金额必须大于0")
                # 转换为 lamports (1 SOL = 10^9 lamports)
                lamports = int(amount_decimal * Decimal(10**9))
            except (ValueError, TypeError) as e:
                raise ValueError(f"无效的转账金额: {amount}")

            # 获取客户端和密钥对
            client = self._get_client()
            sender_keypair = self._get_keypair_from_private_key(private_key)
            
            # 验证发送者地址
            sender_public_key = self._get_public_key(sender_keypair)
            if str(sender_public_key) != from_address:
                raise ValueError("私钥与发送地址不匹配")

            # 检查余额
            try:
                balance = client.get_balance(sender_public_key)
                current_balance = balance.value if balance else 0

                # 估算手续费 (大约5000 lamports)
                estimated_fee = 5000
                total_needed = lamports + estimated_fee

                logger.info(f"余额检查: 当前={current_balance} lamports, 需要={total_needed} lamports")

                if current_balance < total_needed:
                    current_sol = current_balance / 10**9
                    needed_sol = total_needed / 10**9
                    raise ValueError(f"余额不足: 当前余额 {current_sol:.9f} SOL, 需要 {needed_sol:.9f} SOL")

            except ValueError:
                raise  # 重新抛出余额不足的错误
            except Exception as e:
                logger.warning(f"余额检查失败，继续执行转账: {str(e)}")

            # 创建转账指令
            if hasattr(PublicKey, 'from_string'):
                to_pubkey = PublicKey.from_string(to_address)
            else:
                to_pubkey = PublicKey(to_address)

            transfer_instruction = transfer(
                TransferParams(
                    from_pubkey=sender_public_key,
                    to_pubkey=to_pubkey,
                    lamports=lamports
                )
            )

            # 获取最新的区块哈希
            recent_blockhash = client.get_latest_blockhash()
            if not recent_blockhash.value:
                raise Exception("Failed to get recent blockhash")

            # 创建交易，兼容 solana-py 和 solders 两种实现
            instructions = [transfer_instruction]
            transaction = None

            if hasattr(SolanaTransaction, 'add'):
                # solana-py 方式
                transaction = SolanaTransaction()
                transaction.recent_blockhash = recent_blockhash.value.blockhash
                transaction.fee_payer = sender_public_key
                for ix in instructions:
                    transaction.add(ix)
                transaction.sign(sender_keypair)
            else:
                # solders 方式
                if Message is None:
                    raise Exception("solders.message.Message 未导入，无法构造交易")
                # 构造 Message，兼容不同版本
                try:
                    msg = Message(
                        instructions=instructions,
                        payer=sender_public_key,
                        recent_blockhash=recent_blockhash.value.blockhash
                    )
                except TypeError:
                    # 如果 solders 版本不支持 recent_blockhash 参数，则用其他方式构造
                    msg = Message(
                        instructions=instructions,
                        payer=sender_public_key
                    )
                transaction = SolanaTransaction(
                    [sender_keypair],
                    msg,
                    recent_blockhash.value.blockhash
                )

            # 发送交易，兼容 solana-py 和 solders 两种实现
            if hasattr(SolanaTransaction, 'add'):
                # solana-py 方式
                response = client.send_transaction(
                    transaction,
                    sender_keypair,
                    opts=TxOpts(skip_preflight=False) if TxOpts else None
                )
            else:
                # solders 方式
                response = client.send_transaction(
                    transaction,
                    opts=TxOpts(skip_preflight=False) if TxOpts else None
                )
            
            if response.value:
                logger.info(f"SOL 转账成功: {response.value}")
                return {
                    'status': 'success',
                    'transaction_hash': str(response.value),
                    'from_address': from_address,
                    'to_address': to_address,
                    'amount': amount,
                    'lamports': lamports,
                    'chain': 'SOL'
                }
            else:
                logger.error("SOL 转账失败: 无交易哈希返回")
                return {
                    'status': 'error',
                    'error': '转账失败: 无交易哈希返回',
                    'from_address': from_address,
                    'to_address': to_address,
                    'amount': amount
                }
                
        except Exception as e:
            logger.error(f"SOL 转账异常: {str(e)}")
            raise Exception(f"SOL 转账失败: {str(e)}")

    def transfer_token(self, from_address: str, to_address: str, token_address: str,
                      amount: str, private_key: str) -> Dict[str, Any]:
        """转账 SPL 代币"""
        try:
            logger.info(f"开始 SPL 代币转账: {from_address} -> {to_address}, 代币: {token_address}, 金额: {amount}")

            # 检查 SPL Token 功能是否可用
            if not SPL_IMPORTS_OK:
                raise Exception("SPL Token 库导入失败，无法进行 SPL Token 转账。请检查 spl 包是否正确安装。")

            # 验证地址格式
            try:
                # 使用正确的方法创建 PublicKey（从 Base58 字符串）
                if hasattr(PublicKey, 'from_string'):
                    to_pubkey = PublicKey.from_string(to_address)
                    token_pubkey = PublicKey.from_string(token_address)
                else:
                    # 如果没有 from_string 方法，尝试直接构造
                    to_pubkey = PublicKey(to_address)
                    token_pubkey = PublicKey(token_address)
                logger.info(f"地址验证成功: to={to_address}, token={token_address}")
            except Exception as e:
                logger.error(f"地址验证失败: to={to_address}, token={token_address}, error={str(e)}")
                raise ValueError(f"Invalid address format: {str(e)}")

            # 获取客户端和密钥对
            client = self._get_client()
            sender_keypair = self._get_keypair_from_private_key(private_key)
            sender_public_key = self._get_public_key(sender_keypair)

            # 验证发送者地址
            if str(sender_public_key) != from_address:
                raise ValueError("私钥与发送地址不匹配")

            # 获取代币精度
            decimals = self._get_token_decimals(token_address)
            logger.info(f"代币精度: {decimals}")

            # 获取发送方和接收方的关联代币账户地址
            from_token_account = self._get_associated_token_address(from_address, token_address)
            to_token_account = self._get_associated_token_address(to_address, token_address)

            logger.info(f"发送方代币账户: {from_token_account}")
            logger.info(f"接收方代币账户: {to_token_account}")

            # 转换金额为最小单位
            try:
                amount_decimal = Decimal(amount)
                if amount_decimal <= 0:
                    raise ValueError("转账金额必须大于0")
                amount_in_smallest_unit = int(amount_decimal * Decimal(10**decimals))
                logger.info(f"金额转换: {amount} -> {amount_in_smallest_unit} (精度: {decimals})")
            except (ValueError, TypeError) as e:
                raise ValueError(f"无效的转账金额: {amount}")

            # 检查发送方代币账户余额
            try:
                client = self._get_client()
                if hasattr(PublicKey, 'from_string'):
                    from_token_pubkey = PublicKey.from_string(from_token_account)
                else:
                    from_token_pubkey = PublicKey(from_token_account)

                # 首先检查账户是否存在
                account_exists = self._check_token_account_exists(from_token_account)
                logger.info(f"发送方代币账户是否存在: {account_exists}")

                if not account_exists:
                    raise Exception(f"发送方代币账户不存在: {from_token_account}")

                balance_response = client.get_token_account_balance(from_token_pubkey)
                logger.info(f"余额查询响应: {balance_response}")

                if balance_response.value:
                    current_balance = int(balance_response.value.amount)
                    ui_balance = balance_response.value.ui_amount
                    actual_decimals = balance_response.value.decimals
                    logger.info(f"账户余额详情:")
                    logger.info(f"  - 原始余额: {current_balance}")
                    logger.info(f"  - UI余额: {ui_balance}")
                    logger.info(f"  - 代币精度: {actual_decimals}")
                    logger.info(f"  - 预期精度: {decimals}")
                    logger.info(f"  - 转账金额: {amount_in_smallest_unit}")

                    # 检查精度是否一致
                    if actual_decimals != decimals:
                        logger.warning(f"精度不一致! 实际: {actual_decimals}, 预期: {decimals}")
                        # 重新计算转账金额
                        amount_in_smallest_unit = int(amount_decimal * Decimal(10**actual_decimals))
                        logger.info(f"重新计算转账金额: {amount_in_smallest_unit}")

                    # 检查余额是否足够
                    if amount_in_smallest_unit > current_balance:
                        raise Exception(f"余额不足: 需要 {amount_in_smallest_unit}, 当前 {current_balance} (UI: {ui_balance})")

                    logger.info(f"余额检查通过: 需要 {amount_in_smallest_unit}, 当前 {current_balance}")
                else:
                    raise Exception("无法获取代币账户余额")

            except Exception as e:
                logger.error(f"余额检查失败: {str(e)}")
                raise Exception(f"查询 SPL 代币账户余额失败: {str(e)}")



            # 检查接收方代币账户是否存在
            to_account_exists = self._check_token_account_exists(to_token_account)
            logger.info(f"接收方账户是否存在: {to_account_exists}")

            # 构建交易指令列表
            instructions = []

            # 如果接收方代币账户不存在，添加创建账户指令
            if not to_account_exists:
                if hasattr(PublicKey, 'from_string'):
                    owner_pubkey = PublicKey.from_string(to_address)
                    mint_pubkey = PublicKey.from_string(token_address)
                else:
                    owner_pubkey = PublicKey(to_address)
                    mint_pubkey = PublicKey(token_address)

                create_account_ix = create_associated_token_account(
                    payer=sender_public_key,
                    owner=owner_pubkey,
                    mint=mint_pubkey
                )
                instructions.append(create_account_ix)
                logger.info("添加了创建关联代币账户指令")

            # 添加转账指令
            if hasattr(PublicKey, 'from_string'):
                source_pubkey = PublicKey.from_string(from_token_account)
                mint_pubkey = PublicKey.from_string(token_address)
                dest_pubkey = PublicKey.from_string(to_token_account)
            else:
                source_pubkey = PublicKey(from_token_account)
                mint_pubkey = PublicKey(token_address)
                dest_pubkey = PublicKey(to_token_account)

            transfer_ix = transfer_checked(
                TransferCheckedParams(
                    program_id=TOKEN_PROGRAM_ID,
                    source=source_pubkey,
                    mint=mint_pubkey,
                    dest=dest_pubkey,
                    owner=sender_public_key,
                    amount=amount_in_smallest_unit,
                    decimals=decimals,
                    signers=[]
                )
            )
            instructions.append(transfer_ix)
            logger.info("添加了转账指令")

            # 获取最新的区块哈希
            recent_blockhash = client.get_latest_blockhash()
            if not recent_blockhash.value:
                raise Exception("Failed to get recent blockhash")

            # 创建交易，兼容 solana-py 和 solders 两种实现
            transaction = None
            if hasattr(SolanaTransaction, 'add'):
                # solana-py 方式
                transaction = SolanaTransaction()
                transaction.recent_blockhash = recent_blockhash.value.blockhash
                transaction.fee_payer = sender_public_key
                for ix in instructions:
                    transaction.add(ix)
                transaction.sign(sender_keypair)
            else:
                # solders 方式
                if Message is None:
                    raise Exception("solders.message.Message 未导入，无法构造交易")
                # 构造 Message，兼容不同版本
                try:
                    msg = Message(
                        instructions=instructions,
                        payer=sender_public_key,
                        recent_blockhash=recent_blockhash.value.blockhash
                    )
                except TypeError:
                    # 如果 solders 版本不支持 recent_blockhash 参数，则用其他方式构造
                    msg = Message(
                        instructions=instructions,
                        payer=sender_public_key
                    )
                transaction = SolanaTransaction(
                    [sender_keypair],
                    msg,
                    recent_blockhash.value.blockhash
                )

            # 发送交易，兼容 solana-py 和 solders 两种实现
            if hasattr(SolanaTransaction, 'add'):
                # solana-py 方式
                response = client.send_transaction(
                    transaction,
                    sender_keypair,
                    opts=TxOpts(skip_preflight=False) if TxOpts else None
                )
            else:
                # solders 方式
                response = client.send_transaction(
                    transaction,
                    opts=TxOpts(skip_preflight=False) if TxOpts else None
                )

            # 获取交易哈希
            tx_hash = response.value if hasattr(response, 'value') else response

            if tx_hash:
                logger.info(f"SPL 代币转账成功: {tx_hash}")

                # 等待交易确认
                time.sleep(2)

                return {
                    'status': 'success',
                    'transaction_hash': str(tx_hash),
                    'from_address': from_address,
                    'to_address': to_address,
                    'token_address': token_address,
                    'amount': amount,
                    'amount_in_smallest_unit': amount_in_smallest_unit,
                    'decimals': decimals,
                    'chain': 'SOL',
                    'from_token_account': from_token_account,
                    'to_token_account': to_token_account,
                    'account_created': not to_account_exists
                }
            else:
                logger.error("SPL 代币转账失败: 无交易哈希返回")
                return {
                    'status': 'error',
                    'error': '代币转账失败: 无交易哈希返回',
                    'from_address': from_address,
                    'to_address': to_address,
                    'token_address': token_address,
                    'amount': amount
                }

        except Exception as e:
            logger.error(f"SPL 代币转账异常: {str(e)}")
            raise Exception(f"SPL 代币转账失败: {str(e)}")

    def estimate_fee(self, to_address: str, amount: str, token_address: Optional[str] = None) -> Dict[str, Any]:
        """估算转账手续费"""
        try:
            client = self._get_client()
            
            # Solana 的基础交易费用通常是 5000 lamports
            base_fee = 5000  # lamports
            
            if token_address:
                # SPL 代币转账可能需要额外费用
                estimated_fee = base_fee + 2000  # 额外 2000 lamports
            else:
                # SOL 原生转账
                estimated_fee = base_fee
            
            # 转换为 SOL
            fee_in_sol = Decimal(estimated_fee) / Decimal(10**9)
            
            return {
                'estimated_fee': str(fee_in_sol),
                'estimated_fee_lamports': estimated_fee,
                'chain': 'SOL',
                'token_address': token_address
            }
            
        except Exception as e:
            logger.error(f"估算 Solana 手续费失败: {str(e)}")
            raise Exception(f"估算手续费失败: {str(e)}")

    def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """
        获取交易状态

        Args:
            tx_hash: 交易哈希

        Returns:
            交易状态信息，包含status字段：success, failed, pending
        """
        try:
            logger.info(f"检查 SOL 交易状态: {tx_hash}")

            # 如果交易哈希以pending_开头，说明是临时哈希，还没有真实的交易哈希
            if tx_hash.startswith('pending_'):
                logger.info(f"临时交易哈希，无法查询状态: {tx_hash}")
                return {'status': 'pending'}

            # 获取客户端
            client = self._get_client()

            # 获取交易信息
            try:
                # 方法1：尝试获取交易签名状态
                try:
                    from solders.signature import Signature
                    signature = Signature.from_string(tx_hash)
                    signature_status = client.get_signature_statuses([signature])

                    if signature_status and hasattr(signature_status, 'value'):
                        status_info = signature_status.value[0]
                        if status_info:
                            # 交易已确认
                            if status_info.err is None:
                                logger.info(f"交易已确认成功: {tx_hash}")
                                return {
                                    'status': 'success',
                                    'confirmations': status_info.confirmations,
                                    'slot': status_info.slot
                                }
                            else:
                                logger.info(f"交易已确认但执行失败: {tx_hash}")
                                return {
                                    'status': 'failed',
                                    'error': str(status_info.err),
                                    'slot': status_info.slot
                                }
                        else:
                            # 状态为None，尝试方法2
                            logger.info(f"签名状态为空，尝试获取完整交易信息: {tx_hash}")

                except Exception as sig_error:
                    logger.warning(f"获取签名状态失败: {sig_error}")

                # 方法2：尝试获取完整的交易信息
                try:
                    from solders.signature import Signature
                    signature = Signature.from_string(tx_hash)
                    transaction_info = client.get_transaction(signature, encoding="json")

                    if transaction_info:
                        # 检查交易是否成功
                        if hasattr(transaction_info, 'value') and transaction_info.value:
                            tx_data = transaction_info.value
                            logger.info(f"找到交易数据: {tx_hash}")

                            # 正确访问meta数据：transaction.meta
                            if hasattr(tx_data, 'transaction') and tx_data.transaction:
                                transaction = tx_data.transaction
                                if hasattr(transaction, 'meta') and transaction.meta:
                                    meta = transaction.meta
                                    if meta.err is None:
                                        logger.info(f"交易确认成功: {tx_hash}")
                                        return {
                                            'status': 'success',
                                            'slot': getattr(tx_data, 'slot', None),
                                            'fee': getattr(meta, 'fee', None)
                                        }
                                    else:
                                        logger.info(f"交易执行失败: {tx_hash}, 错误: {meta.err}")
                                        return {
                                            'status': 'failed',
                                            'error': str(meta.err),
                                            'slot': getattr(tx_data, 'slot', None)
                                        }
                                else:
                                    logger.warning(f"交易缺少meta数据: {tx_hash}")
                            else:
                                logger.warning(f"交易数据结构异常: {tx_hash}")

                            # 如果没有meta数据但找到了交易，假设成功
                            logger.info(f"交易存在但缺少状态信息，假设成功: {tx_hash}")
                            return {'status': 'success', 'slot': getattr(tx_data, 'slot', None)}

                except Exception as tx_error:
                    logger.warning(f"获取完整交易信息失败: {tx_error}")

                # 如果两种方法都失败，返回pending
                logger.info(f"交易未找到或还在处理中: {tx_hash}")
                return {'status': 'pending'}

                if signature_status and hasattr(signature_status, 'value'):
                    status_info = signature_status.value[0]

                    if status_info:
                        # 交易已确认
                        if status_info.err is None:
                            logger.info(f"交易已确认成功: {tx_hash}")
                            return {
                                'status': 'success',
                                'confirmations': status_info.confirmations,
                                'slot': status_info.slot
                            }
                        else:
                            logger.info(f"交易已确认但执行失败: {tx_hash}")
                            return {
                                'status': 'failed',
                                'error': str(status_info.err),
                                'slot': status_info.slot
                            }
                    else:
                        # 交易未找到，可能是网络延迟，尝试其他方法
                        logger.info(f"签名状态未找到，尝试其他方法: {tx_hash}")
                else:
                    logger.info(f"无法获取签名状态: {tx_hash}")

                # 尝试使用get_transaction方法
                try:
                    from solders.signature import Signature
                    signature = Signature.from_string(tx_hash)
                    transaction_info = client.get_transaction(signature, encoding="json")

                    if transaction_info and hasattr(transaction_info, 'value') and transaction_info.value:
                        tx_data = transaction_info.value
                        if hasattr(tx_data, 'meta') and tx_data.meta:
                            if tx_data.meta.err is None:
                                logger.info(f"交易确认成功（通过get_transaction）: {tx_hash}")
                                return {'status': 'success', 'slot': getattr(tx_data, 'slot', None)}
                            else:
                                logger.info(f"交易执行失败（通过get_transaction）: {tx_hash}")
                                return {'status': 'failed', 'error': str(tx_data.meta.err)}

                    logger.info(f"get_transaction未找到交易: {tx_hash}")

                except Exception as tx_error:
                    logger.warning(f"get_transaction调用失败: {tx_error}")

                # 如果所有方法都失败，返回pending
                logger.info(f"所有查询方法都未找到交易，可能还在处理中: {tx_hash}")
                return {'status': 'pending'}

            except Exception as e:
                # 如果获取交易信息失败，可能是交易还未被打包
                if "not found" in str(e).lower():
                    logger.info(f"交易尚未被打包: {tx_hash}")
                    return {'status': 'pending'}
                else:
                    logger.error(f"获取交易信息失败: {str(e)}")
                    return {'status': 'pending'}

        except Exception as e:
            logger.error(f"检查 SOL 交易状态失败: {str(e)}")
            return {'status': 'pending'}

    def get_transaction_history(self, address: str, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """获取交易历史"""
        try:
            logger.info(f"获取 Solana 交易历史: {address}, 页码: {page}, 限制: {limit}")

            client = self._get_client()
            if hasattr(PublicKey, 'from_string'):
                pubkey = PublicKey.from_string(address)
            else:
                pubkey = PublicKey(address)

            # 获取交易签名列表
            signatures = client.get_signatures_for_address(
                pubkey,
                limit=limit,
                commitment=Commitment("confirmed")
            )

            transactions = []
            for sig_info in signatures.value:
                try:
                    # 获取交易详情，支持新的交易版本
                    tx = client.get_transaction(
                        sig_info.signature,
                        commitment=Commitment("confirmed"),
                        max_supported_transaction_version=0
                    )

                    if tx.value and tx.value.transaction:
                        transaction_data = self._parse_transaction_details(tx.value, address, sig_info)
                        if transaction_data:
                            transactions.append(transaction_data)

                except Exception as e:
                    logger.warning(f"获取交易详情失败: {str(e)}")
                    continue

            return {
                'transactions': transactions,
                'total': len(transactions),
                'page': page,
                'limit': limit,
                'chain': 'SOL',
                'address': address
            }

        except Exception as e:
            logger.error(f"获取 Solana 交易历史失败: {str(e)}")
            raise Exception(f"获取交易历史失败: {str(e)}")

    def _parse_transaction_details(self, tx_result, user_address: str, sig_info) -> Dict[str, Any]:
        """解析交易详情"""
        try:
            transaction = tx_result.transaction
            meta = transaction.meta

            if not meta:
                return None

            # 基本交易信息
            tx_data = {
                'signature': str(sig_info.signature),
                'slot': sig_info.slot,
                'block_time': sig_info.block_time,
                'status': 'success' if not sig_info.err else 'failed',
                'fee': meta.fee,
                'type': 'transaction',
                'amount': '0',
                'token_symbol': 'SOL',
                'token_name': 'Solana',
                'token_logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
                'from_address': '',
                'to_address': ''
            }

            # 首先检查是否有SPL代币转账
            spl_transfer = self._parse_spl_token_transfer(meta, user_address)
            if spl_transfer:
                tx_data.update(spl_transfer)
                return tx_data

            # 解析SOL原生代币转账
            sol_transfer = self._parse_sol_transfer(meta, user_address, transaction)
            if sol_transfer:
                tx_data.update(sol_transfer)
                return tx_data

            # 如果没有找到转账信息，返回基本交易信息
            return tx_data

        except Exception as e:
            logger.warning(f"解析交易详情失败: {str(e)}")
            return None

    def _parse_sol_transfer(self, meta, user_address: str, transaction) -> Dict[str, Any]:
        """解析SOL原生代币转账"""
        try:
            if not hasattr(meta, 'pre_balances') or not hasattr(meta, 'post_balances'):
                return None

            pre_balances = meta.pre_balances or []
            post_balances = meta.post_balances or []
            accounts = []

            # 获取账户列表
            if hasattr(transaction, 'message') and hasattr(transaction.message, 'account_keys'):
                accounts = [str(key) for key in transaction.message.account_keys]
            elif hasattr(transaction, 'transaction') and hasattr(transaction.transaction, 'message'):
                accounts = [str(key) for key in transaction.transaction.message.account_keys]

            if len(accounts) != len(pre_balances) or len(accounts) != len(post_balances):
                return None

            # 查找用户地址的索引
            user_index = -1
            for i, account in enumerate(accounts):
                if account == user_address:
                    user_index = i
                    break

            if user_index == -1:
                return None

            # 计算用户余额变化
            user_pre_balance = pre_balances[user_index]
            user_post_balance = post_balances[user_index]
            balance_change = user_post_balance - user_pre_balance

            # 如果余额没有变化（除了手续费），跳过
            if abs(balance_change) <= meta.fee:
                return None

            # 计算实际转账金额（lamports转SOL）
            amount_lamports = abs(balance_change)
            if balance_change < 0:
                # 发送交易，需要加上手续费
                amount_lamports = abs(balance_change) - meta.fee

            amount_sol = amount_lamports / 1_000_000_000  # 1 SOL = 1,000,000,000 lamports

            # 确定交易类型和对方地址
            if balance_change < 0:
                # 发送交易
                transaction_type = 'sent'
                # 查找接收方（余额增加的账户）
                to_address = self._find_recipient_address(accounts, pre_balances, post_balances, user_index)
                from_address = user_address
            else:
                # 接收交易
                transaction_type = 'received'
                # 查找发送方（余额减少的账户）
                from_address = self._find_sender_address(accounts, pre_balances, post_balances, user_index)
                to_address = user_address

            return {
                'type': transaction_type,
                'amount': str(amount_sol),
                'token_symbol': 'SOL',
                'token_name': 'Solana',
                'token_logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
                'from_address': from_address,
                'to_address': to_address
            }

        except Exception as e:
            logger.warning(f"解析SOL转账失败: {str(e)}")
            return None

    def _parse_spl_token_transfer(self, meta, user_address: str) -> Dict[str, Any]:
        try:
            if not hasattr(meta, 'pre_token_balances') or not hasattr(meta, 'post_token_balances'):
                return None

            pre_token_balances = meta.pre_token_balances or []
            post_token_balances = meta.post_token_balances or []

            # 查找用户地址相关的代币余额变化
            for pre_balance in pre_token_balances:
                if hasattr(pre_balance, 'owner') and str(pre_balance.owner) == user_address:
                    # 找到对应的post_balance
                    for post_balance in post_token_balances:
                        if (hasattr(post_balance, 'account_index') and
                            hasattr(pre_balance, 'account_index') and
                            post_balance.account_index == pre_balance.account_index):

                            # 计算余额变化
                            pre_amount = float(pre_balance.ui_token_amount.amount) if hasattr(pre_balance, 'ui_token_amount') else 0
                            post_amount = float(post_balance.ui_token_amount.amount) if hasattr(post_balance, 'ui_token_amount') else 0

                            if pre_amount != post_amount:
                                # 获取代币信息
                                token_mint = str(pre_balance.mint) if hasattr(pre_balance, 'mint') else 'Unknown'
                                decimals = pre_balance.ui_token_amount.decimals if hasattr(pre_balance, 'ui_token_amount') else 0

                                # 计算实际转账金额
                                amount_change = post_amount - pre_amount
                                actual_amount = abs(amount_change) / (10 ** decimals)

                                # 获取代币元数据
                                token_metadata = self._get_token_metadata(token_mint)

                                # 推断 from_address/to_address
                                if amount_change < 0:
                                    # 发送
                                    from_address = user_address
                                    to_address = self._find_recipient_token_address(pre_token_balances, post_token_balances, user_address, token_mint)
                                else:
                                    # 接收
                                    from_address = self._find_sender_token_address(pre_token_balances, post_token_balances, user_address, token_mint)
                                    to_address = user_address

                                return {
                                    'type': 'sent' if amount_change < 0 else 'received',
                                    'amount': str(actual_amount),
                                    'token_symbol': token_metadata.get('symbol', token_mint[:8] + '...'),
                                    'token_name': token_metadata.get('name', 'Unknown'),
                                    'token_logo': token_metadata.get('logo', ''),
                                    'token_address': token_mint,
                                    'from_address': from_address,
                                    'to_address': to_address
                                }

            # 检查新创建的代币账户（接收代币）
            for post_balance in post_token_balances:
                if hasattr(post_balance, 'owner') and str(post_balance.owner) == user_address:
                    # 检查是否在pre_balances中不存在
                    found_in_pre = False
                    for pre_balance in pre_token_balances:
                        if (hasattr(pre_balance, 'account_index') and
                            hasattr(post_balance, 'account_index') and
                            pre_balance.account_index == post_balance.account_index):
                            found_in_pre = True
                            break

                    if not found_in_pre and hasattr(post_balance, 'ui_token_amount'):
                        # 新创建的代币账户，说明是接收代币
                        post_amount = float(post_balance.ui_token_amount.amount)
                        if post_amount > 0:
                            token_mint = str(post_balance.mint) if hasattr(post_balance, 'mint') else 'Unknown'
                            decimals = post_balance.ui_token_amount.decimals
                            actual_amount = post_amount / (10 ** decimals)
                            token_symbol = self._get_token_symbol(token_mint)

                            return {
                                'type': 'received',
                                'amount': str(actual_amount),
                                'token_symbol': token_symbol,
                                'token_address': token_mint,
                                'from_address': 'Unknown',
                                'to_address': user_address
                            }
            return None
        except Exception as e:
            logger.warning(f"解析SPL代币转账失败: {str(e)}")
            return None

    def _find_recipient_token_address(self, pre_token_balances, post_token_balances, user_address, token_mint):
        """推断 SPL 代币接收方地址"""
        try:
            for post_balance in post_token_balances:
                if str(post_balance.owner) != user_address and str(post_balance.mint) == token_mint:
                    pre_amount = 0
                    for pre_balance in pre_token_balances:
                        if (hasattr(pre_balance, 'account_index') and
                            hasattr(post_balance, 'account_index') and
                            pre_balance.account_index == post_balance.account_index):
                            pre_amount = float(pre_balance.ui_token_amount.amount) if hasattr(pre_balance, 'ui_token_amount') else 0
                            break
                    post_amount = float(post_balance.ui_token_amount.amount) if hasattr(post_balance, 'ui_token_amount') else 0
                    if post_amount > pre_amount:
                        return str(post_balance.owner)
            return 'Unknown'
        except Exception:
            return 'Unknown'

    def _find_sender_token_address(self, pre_token_balances, post_token_balances, user_address, token_mint):
        """推断 SPL 代币发送方地址"""
        try:
            for pre_balance in pre_token_balances:
                if str(pre_balance.owner) != user_address and str(pre_balance.mint) == token_mint:
                    pre_amount = float(pre_balance.ui_token_amount.amount) if hasattr(pre_balance, 'ui_token_amount') else 0
                    post_amount = 0
                    for post_balance in post_token_balances:
                        if (hasattr(post_balance, 'account_index') and
                            hasattr(pre_balance, 'account_index') and
                            post_balance.account_index == pre_balance.account_index):
                            post_amount = float(post_balance.ui_token_amount.amount) if hasattr(post_balance, 'ui_token_amount') else 0
                            break
                    if pre_amount > post_amount:
                        return str(pre_balance.owner)
            return 'Unknown'
        except Exception:
            return 'Unknown'

    def _get_token_symbol(self, token_mint: str) -> str:
        """获取代币符号"""
        # 常见代币地址到符号的映射
        token_symbols = {
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
            'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 'BONK',
            'So11111111111111111111111111111111111111112': 'SOL',
        }

        return token_symbols.get(token_mint, token_mint[:8] + '...')  # 如果找不到符号，显示地址前8位






    def _get_token_metadata(self, token_mint: str) -> Dict[str, Any]:
        """获取代币元数据"""
        try:
            # 使用SolanaTokenService获取代币元数据
            from chains.solana.services.token import SolanaTokenService
            token_service = SolanaTokenService()
            metadata = token_service.get_token_metadata(token_mint)

            if metadata and isinstance(metadata, dict):
                return metadata
            else:
                logger.warning(f"获取代币元数据失败或返回格式错误: {token_mint}")
                return self._get_fallback_metadata(token_mint)

        except Exception as e:
            logger.warning(f"获取代币元数据异常: {str(e)}")
            return self._get_fallback_metadata(token_mint)

    def _get_fallback_metadata(self, token_mint: str) -> Dict[str, Any]:
        """获取备用代币元数据"""
        # 常见代币地址到元数据的映射
        token_metadata = {
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': {
                'symbol': 'USDC',
                'name': 'USD Coin',
                'logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png'
            },
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': {
                'symbol': 'USDT',
                'name': 'Tether USD',
                'logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB/logo.png'
            },
            'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': {
                'symbol': 'BONK',
                'name': 'Bonk',
                'logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263/logo.png'
            },
            'So11111111111111111111111111111111111111112': {
                'symbol': 'SOL',
                'name': 'Solana',
                'logo': 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png'
            }
        }

        return token_metadata.get(token_mint, {
            'symbol': token_mint[:8] + '...',
            'name': 'Unknown Token',
            'logo': ''
        })

    def _find_recipient_address(self, accounts, pre_balances, post_balances, sender_index):
        """查找接收方地址"""
        try:
            for i, (pre, post) in enumerate(zip(pre_balances, post_balances)):
                if i != sender_index and post > pre:
                    return str(accounts[i])
            return 'Unknown'
        except:
            return 'Unknown'

    def _find_sender_address(self, accounts, pre_balances, post_balances, recipient_index):
        """查找发送方地址"""
        try:
            for i, (pre, post) in enumerate(zip(pre_balances, post_balances)):
                if i != recipient_index and pre > post:
                    return str(accounts[i])
            return 'Unknown'
        except:
            return 'Unknown'
