from typing import Dict, Any, List, Optional
from decimal import Decimal
import logging
import requests
from django.utils import timezone
from common.config import Config
from wallets.models import Token, WalletToken, Wallet, Chain

logger = logging.getLogger(__name__)

class KadenaBalanceService:
    """Kadena 余额服务"""

    def __init__(self, chain=None):
        """初始化 Kadena 服务"""
        self.config = Config()
        self.chain = chain

    def get_balance(self, chain: str, address: str, wallet_id: Optional[int] = None) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            logger.info(f"获取 {chain} 链上地址 {address} 的余额")

            # 如果提供了 wallet_id，检查钱包的 kadena_chain_id 设置
            specific_chain_id = None
            if wallet_id:
                try:
                    wallet = Wallet.objects.get(id=wallet_id)
                    logger.info(f"钱包 {wallet_id} 的 kadena_chain_id: {wallet.kadena_chain_id}")
                    if wallet.kadena_chain_id is not None:
                        specific_chain_id = wallet.kadena_chain_id
                        logger.info(f"🎯 钱包 {wallet_id} 设置了特定的 kadena_chain_id: {specific_chain_id}")
                    else:
                        logger.info(f"钱包 {wallet_id} 没有设置特定的 kadena_chain_id，将查询所有链")
                except Wallet.DoesNotExist:
                    logger.warning(f"钱包 {wallet_id} 不存在")

            # 使用 Kadena SDK 获取余额
            from kadena_sdk.kadena_sdk import KadenaSdk
            from decimal import Decimal
            kadena_config = self.config.get_kadena_config(chain)

            # 检查是否是多链架构
            if kadena_config.get('multi_chain', False):
                # 如果钱包设置了特定的 chain_id，只查询该链
                if specific_chain_id is not None:
                    logger.info(f"钱包设置了特定的 kadena_chain_id {specific_chain_id}，只查询该链的余额")

                    # 只查询指定链的余额
                    if chain == "KDA":
                        api_version = f"chainweb/0.0/mainnet01/chain/{specific_chain_id}"
                    else:  # KDA_TESTNET
                        api_version = f"chainweb/0.0/testnet04/chain/{specific_chain_id}"

                    sdk = KadenaSdk(
                        kadena_config['rpc_url'],
                        str(specific_chain_id),
                        kadena_config['network_id'],
                        api_version
                    )

                    balance = sdk.get_balance(address)
                    logger.info(f"Chain {specific_chain_id} 上的余额: {balance}")

                    return {
                        "balance": str(balance),
                        "chain": chain,
                        "address": address,
                        "kadena_chain_id": specific_chain_id
                    }

                # 如果没有设置特定链，查询所有链上的余额（原有逻辑）
                import concurrent.futures
                import time

                # 直接从链上获取数据，不使用缓存
                logger.info(f"直接从链上获取数据，不使用缓存")

                # 如果没有缓存，并行查询链
                logger.info(f"没有缓存，开始并行查询所有 Kadena 平行链上的余额")
                total_balance = Decimal('0')
                start_time = time.time()

                # 定义查询单个链的函数
                def query_chain_balance(kadena_chain_id):
                    try:
                        # 直接从链上获取数据，不使用缓存
                        logger.info(f"直接从 Kadena 平行链 {kadena_chain_id} 获取数据")

                        # 更新 API 版本以匹配当前 Kadena 链 ID
                        # 注意: 这里的 kadena_chain_id 是 Kadena 平行链的 ID（0-19）
                        # 不要与钱包模型中的 chain 字段（如 "KDA", "KDA_TESTNET"）混淆
                        if chain == "KDA":
                            api_version = f"chainweb/0.0/mainnet01/chain/{kadena_chain_id}"
                        else:  # KDA_TESTNET
                            api_version = f"chainweb/0.0/testnet04/chain/{kadena_chain_id}"

                        # 对Chain 0的查询进行特殊处理
                        if kadena_chain_id == 0:
                            logger.info(f"开始查询 Kadena 平行链 0 (主链) 上的余额")
                        else:
                            logger.info(f"开始查询 Kadena 平行链 {kadena_chain_id} 上的余额")

                        # 创建 SDK 实例
                        sdk = KadenaSdk(
                            kadena_config['rpc_url'],
                            str(kadena_chain_id),  # 使用当前 Kadena 链 ID
                            kadena_config['network_id'],
                            api_version
                        )

                        # 获取当前链上的余额
                        chain_balance = sdk.get_balance(address)

                        # 不使用缓存
                        logger.info(f"获取到 Kadena 平行链 {kadena_chain_id} 的余额: {chain_balance}")

                        # 对Chain 0的结果进行特殊处理
                        if kadena_chain_id == 0:
                            logger.info(f"Kadena 平行链 0 (主链) 上的余额: {chain_balance}")
                            # 如果Chain 0有余额，打印更详细的日志
                            if chain_balance > 0:
                                logger.info(f"\u2605\u2605\u2605 发现Chain 0上有余额: {chain_balance} \u2605\u2605\u2605")
                        else:
                            logger.info(f"Kadena 平行链 {kadena_chain_id} 上的余额: {chain_balance}")

                        return kadena_chain_id, chain_balance
                    except Exception as chain_error:
                        # 对Chain 0的错误进行特殊处理
                        if kadena_chain_id == 0:
                            logger.error(f"\u2757\u2757\u2757 获取 Kadena 链 0 (主链) 上的余额失败: {str(chain_error)}")
                        else:
                            logger.error(f"获取 Kadena 链 {kadena_chain_id} 上的余额失败: {str(chain_error)}")
                        # 不使用缓存
                        logger.info(f"查询 Kadena 平行链 {kadena_chain_id} 失败，返回零余额")
                        return kadena_chain_id, Decimal('0')

                # 使用线程池并行查询
                with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:  # 增加并发线程数
                    # 提交所有查询任务
                    # 优先查询Chain 0，然后再查询其他链
                    # 将Chain 0放在列表的最前面，确保它首先被查询
                    kadena_chain_ids_to_query = [0] + [i for i in kadena_config['kadena_chain_ids'] if i != 0]
                    future_to_chain = {executor.submit(query_chain_balance, kadena_chain_id): kadena_chain_id for kadena_chain_id in kadena_chain_ids_to_query}

                    # 处理查询结果
                    chain0_balance = Decimal('0')  # 专门记录Chain 0的余额
                    other_chains_balance = Decimal('0')  # 记录其他链的余额

                    for future in concurrent.futures.as_completed(future_to_chain):
                        kadena_chain_id = future_to_chain[future]
                        try:
                            kadena_chain_id, chain_balance = future.result()
                            if chain_balance > 0:
                                logger.info(f"Kadena 链 {kadena_chain_id} 上的余额: {chain_balance}")

                                # 分开记录Chain 0和其他链的余额
                                if kadena_chain_id == 0:
                                    chain0_balance = chain_balance
                                    logger.info(f"Chain 0 余额: {chain0_balance}")
                                else:
                                    other_chains_balance += chain_balance
                                    logger.info(f"其他链累计余额: {other_chains_balance}")

                                total_balance += chain_balance
                        except Exception as exc:
                            logger.error(f"处理 Kadena 链 {kadena_chain_id} 的查询结果时出错: {str(exc)}")

                    # 如果所有链的总余额为0，但Chain 0有余额，使用Chain 0的余额
                    if total_balance == 0 and chain0_balance > 0:
                        logger.warning(f"总余额为0，但Chain 0有余额 {chain0_balance}，使用Chain 0的余额")
                        total_balance = chain0_balance

                end_time = time.time()
                logger.info(f"并行查询所有链上的余额耗时: {end_time - start_time:.2f} 秒")
                logger.info(f"所有链上的总余额: {total_balance}")

                # 记录查询的链数量
                logger.info(f"共查询了 {len(kadena_chain_ids_to_query)} 个 Kadena 平行链")

                # 不使用缓存
                logger.info(f"所有链上的总余额: {total_balance}")

                return {
                    "balance": str(total_balance),
                    "chain": chain,
                    "address": address
                }
            else:
                # 如果不是多链架构，只查询指定链上的余额
                sdk = KadenaSdk(
                    kadena_config['rpc_url'],
                    kadena_config['chain_id'],
                    kadena_config['network_id'],
                    kadena_config['api_version']
                )

                balance = sdk.get_balance(address)
                logger.info(f"获取到余额: {balance}")

                return {
                    "balance": str(balance),
                    "chain": chain,
                    "address": address
                }
        except Exception as e:
            logger.error(f"获取 Kadena 余额失败: {str(e)}")
            raise Exception(f"获取 Kadena 余额失败: {str(e)}")

    def get_all_balances(self, chain: str, address: str, force_refresh: bool = False) -> dict:
        """获取所有代币余额"""
        try:
            # 获取所有代币余额
            token_balances = self.get_all_token_balances(chain, address, force_refresh=force_refresh)

            # 获取隐藏的代币列表
            from wallets.models import WalletToken
            hidden_tokens = WalletToken.objects.filter(
                wallet__address=address,
                is_visible=False
            ).values_list('token_address', flat=True)

            # 过滤掉隐藏的代币和余额为0的代币
            visible_tokens = [
                token for token in token_balances
                if token.get('token_address', '') not in hidden_tokens and
                   float(token.get('balance', '0')) > 0
            ]

            # 计算总价值（USD）
            from decimal import Decimal
            total_value_usd = Decimal('0')
            total_value_change_24h = Decimal('0')

            # 计算代币总价值
            for token in visible_tokens:
                # 计算代币价值，使用 current_price_usd 和 balance
                if token.get('current_price_usd') and token.get('balance'):
                    try:
                        price = Decimal(str(token['current_price_usd']))
                        balance = Decimal(str(token['balance']))
                        price_change = Decimal(str(token.get('price_change_24h', 0)))

                        # 计算代币价值
                        value_usd = price * balance

                        # 添加到代币数据中，便于前端显示
                        token['value_usd'] = str(value_usd)

                        # 累加总价值
                        total_value_usd += value_usd
                        total_value_change_24h += value_usd * price_change / 100

                        logger.info(f"计算代币 {token.get('symbol')} 价值: {price} * {balance} = {value_usd} USD")
                    except Exception as e:
                        logger.error(f"计算代币 {token.get('symbol')} 价值时出错: {str(e)}")

            return {
                'total_value_usd': str(total_value_usd),
                'total_value_change_24h': str(total_value_change_24h),
                'tokens': visible_tokens
            }

        except Exception as e:
            logger.error(f"Error getting all balances: {str(e)}")
            return {
                'total_value_usd': '0',
                'total_value_change_24h': '0',
                'tokens': []
            }

    def get_all_token_balances(self, chain: str, address: str, wallet_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取所有代币余额并更新数据库"""
        try:
            import time
            start_time = time.time()
            logger.info(f"开始获取 {chain} 链上地址 {address} 的所有代币余额")

            # 直接从链上获取数据，不使用缓存
            logger.info(f"直接从链上获取数据，不使用缓存")

            # 查询原生代币余额
            native_balance = self.get_balance(chain, address, wallet_id)
            logger.info(f"获取到原生代币余额: {native_balance}")

            # 获取代币列表
            from chains.kadena.services.token import KadenaTokenService
            token_service = KadenaTokenService()
            # 设置当前钱包 ID 以便 token_service 可以使用
            if wallet_id:
                token_service._current_wallet_id = wallet_id
            tokens = token_service.get_token_list(address, chain)  # 传递 chain 参数
            logger.info(f"获取到 {len(tokens)} 个代币")

            # 合并所有代币余额
            all_tokens = [native_balance] + tokens

            # 不使用缓存
            logger.info(f"共获取到 {len(all_tokens)} 个代币")

            # 如果提供了钱包ID，更新数据库
            if wallet_id:
                self._update_database(wallet_id, chain, all_tokens)

            end_time = time.time()
            logger.info(f"获取所有代币余额并更新数据库耗时: {end_time - start_time:.2f} 秒")
            return all_tokens
        except Exception as e:
            logger.error(f"获取所有代币余额失败: {str(e)}")
            raise Exception(f"获取所有代币余额失败: {str(e)}")

    def _update_database(self, wallet_id: int, chain: str, tokens: List[Dict[str, Any]]) -> None:
        """更新数据库中的代币余额"""
        try:
            logger.info(f"开始更新数据库中的代币余额，钱包ID: {wallet_id}")

            # 获取钱包对象
            wallet = Wallet.objects.get(id=wallet_id)

            # 获取链对象
            chain_obj = Chain.objects.get(chain=chain)

            # 更新原生代币（第一个代币）
            if tokens and len(tokens) > 0:
                self._update_native_token(wallet, chain_obj, tokens[0])

            # 获取链上返回的所有代币地址
            current_token_addresses = [token.get('token_address', '') for token in (tokens[1:] if len(tokens) > 1 else [])]

            # 更新其他代币
            for token in tokens[1:] if len(tokens) > 1 else []:
                self._update_token(wallet, chain_obj, token)

            # 处理零余额的代币
            try:
                # 获取数据库中当前钱包的所有代币（排除原生代币）
                wallet_tokens = WalletToken.objects.filter(wallet=wallet).exclude(token_address="")
                logger.info(f"数据库中的代币数量: {wallet_tokens.count()}")

                # 找出数据库中存在但链上没有返回的代币（可能是零余额的代币）
                for wt in wallet_tokens:
                    logger.info(f"检查数据库中的代币: {wt.token_address}, 当前余额: {wt.balance}")
                    if wt.token_address not in current_token_addresses and wt.token_address != "coin":
                        logger.info(f"删除零余额代币: {wt.token_address}, 原余额: {wt.balance}")
                        # 删除零余额代币
                        wt.delete()
                        logger.info(f"已删除代币 {wt.token_address}")
            except Exception as e:
                logger.error(f"Error processing zero balance tokens: {e}")

            logger.info(f"数据库更新完成，共处理 {len(tokens)} 个代币")
        except Exception as e:
            logger.error(f"更新数据库失败: {str(e)}")
            raise Exception(f"更新数据库失败: {str(e)}")

    def _update_native_token(self, wallet: Wallet, chain: Chain, balance_data: Dict[str, Any]) -> None:
        """更新原生代币信息"""
        try:
            logger.info(f"开始更新钱包 {wallet.address} 的原生 KDA 代币信息")
            logger.info(f"从链上获取的余额数据: {balance_data}")

            # 检查数据库中是否已有该代币
            token, created = Token.objects.get_or_create(
                address="",  # 原生 KDA 代币使用空字符串作为地址
                chain=chain,
                defaults={
                    "name": "Kadena",
                    "symbol": "KDA",
                    "decimals": 12,  # Kadena 默认精度为 12
                    "logo_url": "https://assets.coingecko.com/coins/images/3693/small/kadena.png",  # KDA 的 logo URL
                    "current_price_usd": 0,
                    "price_change_24h": 0,
                    "is_active": True
                }
            )

            if created:
                logger.info(f"创建了新的 KDA 代币记录")
            else:
                logger.info(f"使用现有的 KDA 代币记录 (ID: {token.id})")
                # 确保现有记录也有正确的logo_url
                if not token.logo_url:
                    token.logo_url = "https://assets.coingecko.com/coins/images/3693/small/kadena.png"
                    token.save(update_fields=['logo_url'])
                    logger.info(f"更新了 KDA 代币的 logo_url")

            # 获取KDA的价格和24小时涨跌
            try:
                import requests
                from django.conf import settings

                # 使用CryptoCompare API获取KDA的价格
                cryptocompare_url = "https://min-api.cryptocompare.com/data/price"
                params = {
                    "fsym": "KDA",
                    "tsyms": "USD"
                }

                # 添加API密钥（如果有）
                if hasattr(settings, 'CRYPTOCOMPARE_API_KEY') and settings.CRYPTOCOMPARE_API_KEY:
                    params['api_key'] = settings.CRYPTOCOMPARE_API_KEY

                response = requests.get(cryptocompare_url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    if 'USD' in data:
                        current_price_usd = data['USD']
                        logger.info(f"获取到KDA当前价格: {current_price_usd} USD")

                        # 获取24小时价格变化
                        histohour_url = "https://min-api.cryptocompare.com/data/v2/histohour"
                        histohour_params = {
                            "fsym": "KDA",
                            "tsym": "USD",
                            "limit": 24
                        }

                        # 添加API密钥（如果有）
                        if hasattr(settings, 'CRYPTOCOMPARE_API_KEY') and settings.CRYPTOCOMPARE_API_KEY:
                            histohour_params['api_key'] = settings.CRYPTOCOMPARE_API_KEY

                        histohour_response = requests.get(histohour_url, params=histohour_params)
                        if histohour_response.status_code == 200:
                            histohour_data = histohour_response.json()
                            if histohour_data.get('Response') == 'Success' and histohour_data.get('Data') and histohour_data['Data'].get('Data'):
                                price_24h_ago = histohour_data['Data']['Data'][0]['open']
                                if price_24h_ago > 0:
                                    price_change_24h = ((current_price_usd - price_24h_ago) / price_24h_ago) * 100
                                    logger.info(f"KDA 24小时价格变化: {price_change_24h:.2f}%")

                                    # 更新代币价格和24小时变化
                                    token.current_price_usd = current_price_usd
                                    token.price_change_24h = price_change_24h
                                    token.save()
                                    logger.info(f"更新了KDA代币价格和24小时变化")

                                    # 确保也更新了WalletToken记录
                                    wallet_token = WalletToken.objects.filter(wallet=wallet, token_address="").first()
                                    if wallet_token:
                                        wallet_token.token = token
                                        wallet_token.save()
                                        logger.info(f"更新了WalletToken记录的Token关联")
            except Exception as e:
                logger.error(f"获取KDA价格和24小时变化失败: {str(e)}")

            # 更新钱包代币关系
            # 尝试查找现有的钱包代币关系
            # 同时查找空地址和"coin"地址的记录
            empty_tokens = WalletToken.objects.filter(wallet=wallet, token_address="")
            coin_tokens = WalletToken.objects.filter(wallet=wallet, token_address="coin")
            logger.info(f"现有的空地址原生代币记录数量: {empty_tokens.count()}")
            logger.info(f"现有的'coin'地址原生代币记录数量: {coin_tokens.count()}")

            # 如果同时存在空地址和"coin"地址的记录，合并它们
            if empty_tokens.count() > 0 and coin_tokens.count() > 0:
                logger.warning(f"同时存在空地址和'coin'地址的记录，将合并它们")

                # 获取两种记录的余额
                empty_balance = Decimal('0')
                coin_balance = Decimal('0')

                for token_record in empty_tokens:
                    try:
                        empty_balance += Decimal(token_record.balance)
                    except:
                        pass

                for token_record in coin_tokens:
                    try:
                        coin_balance += Decimal(token_record.balance)
                    except:
                        pass

                logger.info(f"空地址记录的余额: {empty_balance}")
                logger.info(f"'coin'地址记录的余额: {coin_balance}")

                # 如果'coin'记录有余额，但空地址记录没有余额，将余额转移到空地址记录
                if coin_balance > 0 and empty_balance == 0:
                    logger.info(f"将'coin'记录的余额转移到空地址记录")
                    empty_token = empty_tokens.first()
                    empty_token.balance = str(coin_balance)
                    empty_token.save()

                # 删除'coin'记录
                for token_record in coin_tokens:
                    logger.info(f"删除'coin'地址记录 ID: {token_record.id}")
                    token_record.delete()

            # 如果只有'coin'地址的记录，将其转换为空地址记录
            elif empty_tokens.count() == 0 and coin_tokens.count() > 0:
                logger.warning(f"只有'coin'地址的记录，将其转换为空地址记录")

                for token_record in coin_tokens:
                    # 创建新的空地址记录
                    WalletToken.objects.create(
                        wallet=token_record.wallet,
                        token=token,
                        token_address="",
                        balance=token_record.balance,
                        is_visible=token_record.is_visible
                    )

                    # 删除'coin'记录
                    logger.info(f"删除'coin'地址记录 ID: {token_record.id}")
                    token_record.delete()

            # 如果有多个空地址记录，删除多余的
            if empty_tokens.count() > 1:
                logger.warning(f"发现多个空地址原生代币记录，将删除多余的")
                for token_record in empty_tokens[1:]:
                    logger.info(f"删除多余的空地址原生代币记录 ID: {token_record.id}")
                    token_record.delete()

            # 创建或更新钱包代币关系
            # 正确格式化余额
            balance = balance_data["balance"]
            try:
                decimal_balance = Decimal(balance)
                # 如果是整数，直接显示整数
                if decimal_balance == decimal_balance.to_integral_value():
                    balance_formatted = str(int(decimal_balance))
                else:
                    # 否则保留小数点后 4 位
                    balance_formatted = str(round(decimal_balance, 4))
            except:
                # 如果转换失败，直接使用原始值
                balance_formatted = balance

            wallet_token, created = WalletToken.objects.get_or_create(
                wallet=wallet,
                token_address="",  # 原生代币使用空字符串作为 token_address
                defaults={
                    "token": token,
                    "balance": balance,
                    "balance_formatted": balance_formatted,
                    "is_visible": True
                }
            )

            # 如果已存在，更新余额
            if not created:
                old_balance = wallet_token.balance
                new_balance = balance_data["balance"]

                # 如果新余额为0但旧余额大于0，保留旧余额
                if Decimal(new_balance) == 0 and Decimal(old_balance) > 0:
                    logger.warning(f"\u2757 新余额为0，但旧余额为 {old_balance}，保留旧余额")
                    # 不更新余额，保留旧值
                else:
                    wallet_token.balance = new_balance

                    # 正确格式化余额，保留整数部分
                    try:
                        decimal_balance = Decimal(new_balance)
                        # 如果是整数，直接显示整数
                        if decimal_balance == decimal_balance.to_integral_value():
                            wallet_token.balance_formatted = str(int(decimal_balance))
                        else:
                            # 否则保留小数点后 4 位
                            wallet_token.balance_formatted = str(round(decimal_balance, 4))
                    except:
                        # 如果转换失败，直接使用原始值
                        wallet_token.balance_formatted = new_balance

                    wallet_token.save()
                    logger.info(f"更新了现有的 KDA 余额记录，从 {old_balance} 变为 {new_balance}")
            else:
                logger.info(f"创建了新的 KDA 余额记录，余额为 {balance_data['balance']}")

            logger.info(f"更新原生代币 KDA 成功，余额: {balance_data['balance']}")
        except Exception as e:
            logger.error(f"更新原生代币失败: {str(e)}")
            raise Exception(f"更新原生代币失败: {str(e)}")

    async def _get_token_prices(self, token_addresses: List[str]) -> Dict[str, Dict[str, Any]]:
        """获取代币价格

        Args:
            token_addresses: 代币地址列表

        Returns:
            Dict[str, Dict[str, Any]]: 代币价格字典，格式为 {token_address: {'current_price': float, 'price_change_24h': float}}
        """
        try:
            logger.info(f"开始获取 {len(token_addresses)} 个 Kadena 代币的价格")

            # 初始化结果字典
            result = {}

            # 使用 CryptoCompare API 获取代币价格
            import aiohttp
            import asyncio
            from django.conf import settings

            # 获取代币符号映射
            token_symbols = {}
            for token_address in token_addresses:
                try:
                    token = Token.objects.filter(address=token_address).first()
                    if token and token.symbol:
                        token_symbols[token_address] = token.symbol
                except Exception as e:
                    logger.error(f"获取代币 {token_address} 符号时出错: {str(e)}")

            # 定义获取单个代币价格的异步函数
            async def get_token_price(session, token_address):
                try:
                    # 如果没有找到代币符号，返回默认值
                    if token_address not in token_symbols:
                        logger.warning(f"未找到代币 {token_address} 的符号，无法获取价格")
                        return token_address, {"current_price": 0, "price_change_24h": 0}

                    symbol = token_symbols[token_address]

                    # 使用 CryptoCompare API 获取价格
                    url = "https://min-api.cryptocompare.com/data/price"
                    params = {
                        "fsym": symbol,
                        "tsyms": "USD"
                    }

                    # 添加 API 密钥（如果有）
                    if hasattr(settings, 'CRYPTOCOMPARE_API_KEY') and settings.CRYPTOCOMPARE_API_KEY:
                        params['api_key'] = settings.CRYPTOCOMPARE_API_KEY

                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if 'USD' in data:
                                current_price = data['USD']

                                # 获取 24 小时价格变化
                                histohour_url = "https://min-api.cryptocompare.com/data/v2/histohour"
                                histohour_params = {
                                    "fsym": symbol,
                                    "tsym": "USD",
                                    "limit": 24
                                }

                                # 添加 API 密钥（如果有）
                                if hasattr(settings, 'CRYPTOCOMPARE_API_KEY') and settings.CRYPTOCOMPARE_API_KEY:
                                    histohour_params['api_key'] = settings.CRYPTOCOMPARE_API_KEY

                                async with session.get(histohour_url, params=histohour_params) as histohour_response:
                                    if histohour_response.status == 200:
                                        histohour_data = await histohour_response.json()
                                        price_change_24h = 0
                                        if histohour_data.get('Response') == 'Success' and histohour_data.get('Data') and histohour_data['Data'].get('Data'):
                                            price_24h_ago = histohour_data['Data']['Data'][0]['open']
                                            if price_24h_ago > 0:
                                                price_change_24h = ((current_price - price_24h_ago) / price_24h_ago) * 100

                                        return token_address, {"current_price": current_price, "price_change_24h": price_change_24h}

                    # 如果没有返回价格数据，返回默认值
                    return token_address, {"current_price": 0, "price_change_24h": 0}
                except Exception as e:
                    logger.error(f"获取代币 {token_address} 价格时出错: {str(e)}")
                    return token_address, {"current_price": 0, "price_change_24h": 0}

            # 并行获取所有代币价格
            async with aiohttp.ClientSession() as session:
                tasks = [get_token_price(session, token_address) for token_address in token_addresses]
                results = await asyncio.gather(*tasks)

                # 处理结果
                for token_address, price_data in results:
                    result[token_address] = price_data

            logger.info(f"成功获取 {len(result)} 个 Kadena 代币的价格")
            return result

        except Exception as e:
            logger.error(f"获取 Kadena 代币价格失败: {str(e)}")
            # 返回空结果
            return {token_address: {"current_price": 0, "price_change_24h": 0} for token_address in token_addresses}

    def _update_token(self, wallet: Wallet, chain: Chain, token_data: Dict[str, Any]) -> None:
        """更新代币信息"""
        try:
            token_address = token_data.get("token_address")
            if not token_address:
                logger.warning(f"代币数据缺少地址: {token_data}")
                return

            logger.info(f"开始更新钱包 {wallet.address} 的代币 {token_address} 信息")
            logger.info(f"从链上获取的代币数据: {token_data}")

            # 获取代币元数据
            from chains.kadena.services.token import KadenaTokenService
            token_service = KadenaTokenService()
            metadata = token_service.get_token_metadata(token_address)
            logger.info(f"获取到代币元数据: {metadata}")

            # 检查数据库中是否已有该代币
            token, created = Token.objects.get_or_create(
                address=token_address,
                chain=chain,
                defaults={
                    "name": metadata.get("name", "Unknown"),
                    "symbol": metadata.get("symbol", "Unknown"),
                    "decimals": metadata.get("decimals", 12),  # Kadena 默认精度为 12
                    "logo_url": metadata.get("logo", ""),
                    "is_active": True,
                    "last_updated": timezone.now()
                }
            )

            if created:
                logger.info(f"创建了新的代币记录: {token.symbol}")
            else:
                logger.info(f"使用现有的代币记录: {token.symbol} (ID: {token.id})")

                # 检查是否需要更新元数据
                from wallets.cache_utils import should_update_token_metadata
                if should_update_token_metadata(token, force_update=False, cache_ttl_hours=24):
                    # 更新代币元数据
                    token.name = metadata.get("name", token.name)
                    token.symbol = metadata.get("symbol", token.symbol)
                    token.decimals = metadata.get("decimals", token.decimals)
                    token.logo_url = metadata.get("logo", token.logo_url)
                    token.last_updated = timezone.now()
                    token.save()
                    logger.info(f"更新了代币 {token.symbol} 的元数据")

            # 更新钱包代币关系
            # 尝试查找现有的钱包代币关系
            existing_tokens = WalletToken.objects.filter(wallet=wallet, token_address=token_address)
            logger.info(f"现有的代币 {token_address} 记录数量: {existing_tokens.count()}")

            # 如果有多个记录，删除多余的
            if existing_tokens.count() > 1:
                logger.warning(f"发现多个代币 {token_address} 记录，将删除多余的")
                for token_record in existing_tokens[1:]:
                    logger.info(f"删除多余的代币记录 ID: {token_record.id}")
                    token_record.delete()

            # 创建或更新钱包代币关系
            wallet_token, created = WalletToken.objects.get_or_create(
                wallet=wallet,
                token_address=token_address,  # 使用代币地址作为 token_address
                defaults={
                    "token": token,
                    "balance": token_data.get("balance", "0"),
                    "is_visible": True
                }
            )

            # 如果已存在，更新余额
            if not created:
                old_balance = wallet_token.balance
                new_balance = token_data.get("balance", "0")

                # 如果新余额为0但旧余额大于0，保留旧余额
                if Decimal(new_balance) == 0 and Decimal(old_balance) > 0:
                    logger.warning(f"\u2757 代币 {token.symbol} 的新余额为0，但旧余额为 {old_balance}，保留旧余额")
                    # 不更新余额，保留旧值
                else:
                    wallet_token.balance = new_balance
                    wallet_token.save()
                    logger.info(f"更新了现有的 {token.symbol} 余额记录，从 {old_balance} 变为 {new_balance}")
            else:
                logger.info(f"创建了新的 {token.symbol} 余额记录，余额为 {token_data.get('balance', '0')}")

            logger.info(f"更新代币 {token.symbol} 成功，余额: {token_data.get('balance', '0')}")
        except Exception as e:
            logger.error(f"更新代币失败: {str(e)}")
            # 不抛出异常，继续处理其他代币