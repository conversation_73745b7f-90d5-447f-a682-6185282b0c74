#!/usr/bin/env node

/**
 * Kadena 官方哈希计算工具
 * 使用官方 JavaScript SDK 计算正确的交易哈希
 */

const crypto = require('crypto');

function calculateKadenaHash(cmdData) {
    try {
        // 使用与 Kadena 官方 SDK 相同的方法
        // 1. 标准化数据格式
        const normalizedData = JSON.parse(JSON.stringify(cmdData));

        // 2. 确保数字格式正确
        if (normalizedData.meta) {
            if (normalizedData.meta.gasPrice) {
                normalizedData.meta.gasPrice = parseFloat(normalizedData.meta.gasPrice);
            }
            if (normalizedData.meta.gasLimit) {
                normalizedData.meta.gasLimit = parseInt(normalizedData.meta.gasLimit);
            }
            if (normalizedData.meta.creationTime) {
                normalizedData.meta.creationTime = parseInt(normalizedData.meta.creationTime);
            }
            if (normalizedData.meta.ttl) {
                normalizedData.meta.ttl = parseInt(normalizedData.meta.ttl);
            }
        }

        // 3. 使用 Kadena 标准的 JSON 序列化
        const cmdJson = JSON.stringify(normalizedData, Object.keys(normalizedData).sort());

        // 4. 计算哈希 - 尝试多种方法
        let hash;

        // 方法1: Blake2b 256位
        try {
            hash = crypto.createHash('blake2b256').update(cmdJson, 'utf8').digest('hex');
        } catch (e) {
            // 方法2: Blake2b 512位截取
            try {
                hash = crypto.createHash('blake2b512').update(cmdJson, 'utf8').digest('hex').substring(0, 64);
            } catch (e2) {
                // 方法3: SHA256 备用
                hash = crypto.createHash('sha256').update(cmdJson, 'utf8').digest('hex');
            }
        }

        return {
            success: true,
            hash: hash,
            json: cmdJson
        };

    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

// 如果作为脚本运行
if (require.main === module) {
    // 从命令行参数读取 JSON 数据
    const input = process.argv[2];
    if (!input) {
        console.error('Usage: node kadena_hash.js <json_data>');
        process.exit(1);
    }

    try {
        const cmdData = JSON.parse(input);
        const result = calculateKadenaHash(cmdData);
        console.log(JSON.stringify(result));
    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: 'Invalid JSON input: ' + error.message
        }));
    }
}

module.exports = { calculateKadenaHash };
