"""
EVM 代币兑换服务
基于 1inch API 实现代币兑换功能
"""

import aiohttp
import asyncio
import json
import logging
import time
from decimal import Decimal
from typing import Dict, Any, List, Optional
from web3 import Web3
from eth_account import Account
from common.config import Config

logger = logging.getLogger(__name__)

def medium_gas_price_strategy(web3, transaction_params):
    """中等 gas 价格策略"""
    try:
        # 获取当前 gas 价格
        gas_price = web3.eth.gas_price
        
        # 增加 10% 作为缓冲
        return int(gas_price * 1.1)
    except Exception as e:
        logger.warning(f"获取 gas 价格失败: {str(e)}")
        # 如果获取失败，返回一个默认值
        return ***********  # 20 Gwei

class SwapError(Exception):
    """兑换错误"""
    pass

class EVMSwapService:
    """EVM 代币兑换服务"""

    def __init__(self, chain: str):
        """初始化兑换服务
        
        Args:
            chain: 链名称，如 ETH、BSC 等
        """
        self.chain = chain
        self.config = Config.get_evm_config(chain)
        
        # 设置 chain_id
        if chain == "ETH":
            self.chain_id = 1  # Ethereum Mainnet
        elif chain == "BSC":
            self.chain_id = 56  # BSC Mainnet
        elif chain == "MATIC":
            self.chain_id = 137  # Polygon Mainnet
        else:
            raise Exception(f"不支持的链: {chain}")
        
        # 创建会话锁和会话
        self._session_lock = asyncio.Lock()
        self._session = None
        
        # 1inch API 配置
        self.oneinch_api_key = self.config.get("oneinch_api_key")
        if not self.oneinch_api_key:
            raise Exception("未配置 1inch API 密钥")
        self.oneinch_api_url = "https://api.1inch.dev/swap/v5.0"
        
        logger.info(f"1inch API 配置成功，API URL: {self.oneinch_api_url}")
        
        # 配置 Web3，添加重试机制
        max_retries = 3
        retry_delay = 2  # 初始重试延迟（秒）
        
        for retry in range(max_retries):
            try:
                # 配置 Web3
                self.web3 = Web3(Web3.HTTPProvider(
                    self.config["rpc_url"],
                    request_kwargs={
                        'timeout': 30,  # 设置超时时间为 30 秒
                    }
                ))
                
                # 确保 Web3 连接成功
                if not self.web3.is_connected():
                    raise Exception("Web3 连接失败")
                
                # 设置默认账户
                self.web3.eth.default_account = None
                
                # 设置默认区块
                self.web3.eth.default_block = 'latest'
                
                # 设置 gas 策略
                self.web3.eth.set_gas_price_strategy(medium_gas_price_strategy)
                
                logger.info(f"初始化 {chain} 链上兑换服务成功，chain_id: {self.chain_id}")
                return
                
            except Exception as e:
                logger.warning(f"Web3 连接失败 (尝试 {retry + 1}/{max_retries}): {str(e)}")
                if retry < max_retries - 1:
                    wait_time = retry_delay * (2 ** retry)  # 指数退避
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise Exception(f"无法连接到 {chain} 节点: {str(e)}")

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建 aiohttp 会话"""
        async with self._session_lock:
            if self._session is None or self._session.closed:
                headers = {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
                if self.oneinch_api_key:
                    headers['Authorization'] = f'Bearer {self.oneinch_api_key}'
                
                self._session = aiohttp.ClientSession(headers=headers)
            return self._session

    async def _close_session(self):
        """关闭 aiohttp 会话"""
        async with self._session_lock:
            if self._session and not self._session.closed:
                await self._session.close()
                self._session = None

    async def get_quote(self, from_token: str, to_token: str, amount: str, from_address: str, slippage: Optional[Decimal] = None) -> Dict[str, Any]:
        """获取代币兑换报价
        
        Args:
            from_token: 源代币地址
            to_token: 目标代币地址
            amount: 兑换数量（ETH 单位）
            from_address: 发送方地址
            slippage: 滑点容忍度
            
        Returns:
            Dict: 报价信息
        """
        try:
            logger.info(f"开始获取 {self.chain} 链上 {from_token} 到 {to_token} 的兑换报价")
            
            # 验证 Web3 连接
            if not self.web3.is_connected():
                raise SwapError("Web3 连接已断开")
            
            # 转换为 checksum 地址
            from_token = self.web3.to_checksum_address(from_token)
            to_token = self.web3.to_checksum_address(to_token)
            from_address = self.web3.to_checksum_address(from_address)
            
            # 验证地址格式
            if not self.web3.is_address(from_token):
                raise SwapError(f"无效的源代币地址: {from_token}")
            if not self.web3.is_address(to_token):
                raise SwapError(f"无效的目标代币地址: {to_token}")
            if not self.web3.is_address(from_address):
                raise SwapError(f"无效的发送方地址: {from_address}")
            
            # 将金额转换为 wei
            try:
                amount_decimal = Decimal(amount)
                amount_wei = str(int(amount_decimal * Decimal(10**18)))
                logger.info(f"金额转换: {amount} ETH -> {amount_wei} wei")
            except Exception as e:
                logger.error(f"金额转换失败: {str(e)}")
                raise SwapError(f"无效的金额格式: {amount}")
            
            # 构建请求参数
            params = {
                'fromTokenAddress': from_token,
                'toTokenAddress': to_token,
                'amount': amount_wei,
                'fromAddress': from_address,
                'slippage': str(slippage) if slippage else '1',
                'disableEstimate': 'true',
                'allowPartialFill': 'false',
                'protocols': 'UNISWAP_V2,UNISWAP_V3,SUSHISWAP,CURVE,1INCH_LIMIT_ORDER_PROTOCOL',
                'mainRouteParts': '10',
                'parts': '50',
                'gasLimit': '300000'
            }
            
            # 构建请求头
            headers = {
                'accept': 'application/json',
                'Authorization': f'Bearer {self.oneinch_api_key}'
            }
            
            # 构建请求 URL
            url = f"{self.oneinch_api_url}/{self.chain_id}/swap"
            
            logger.info(f"请求 URL: {url}")
            logger.info(f"请求参数: {params}")
            logger.info(f"请求头: {headers}")
            
            # 发送请求
            async with self._session_lock:
                if not self._session:
                    self._session = aiohttp.ClientSession()
                
                try:
                    async with self._session.get(url, params=params, headers=headers) as response:
                        logger.info(f"响应状态: {response.status}")
                        logger.info(f"响应头: {response.headers}")
                        
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(f"1inch API 错误: {error_text}")
                            raise SwapError(f"1inch API 错误: {error_text}")
                        
                        try:
                            data = await response.json()
                            logger.info(f"响应数据: {json.dumps(data, indent=2)}")
                            
                            if not data:
                                raise SwapError("1inch API 返回空数据")
                            
                            # 添加 quote_id
                            data['quote_id'] = json.dumps(data)
                            
                            return data
                            
                        except json.JSONDecodeError as e:
                            error_text = await response.text()
                            logger.error(f"解析响应失败: {error_text}")
                            raise SwapError(f"解析响应失败: {str(e)}")
                            
                except aiohttp.ClientError as e:
                    logger.error(f"请求失败: {str(e)}")
                    raise SwapError(f"请求失败: {str(e)}")
                    
        except Exception as e:
            logger.error(f"获取报价失败: {str(e)}")
            raise SwapError(f"获取报价失败: {str(e)}")

    async def execute_swap(self, quote_id: str, from_token: str, to_token: str, amount: str, from_address: str, private_key: str, slippage: Optional[Decimal] = None) -> Dict[str, Any]:
        """执行代币兑换
        
        Args:
            quote_id: 报价 ID（JSON 字符串）
            from_token: 源代币地址
            to_token: 目标代币地址
            amount: 兑换数量
            from_address: 发送方地址
            private_key: 私钥
            slippage: 滑点容忍度
            
        Returns:
            Dict: 交易结果
        """
        try:
            logger.info(f"开始执行 {self.chain} 链上 {from_token} 到 {to_token} 的兑换")
            
            # 验证 Web3 连接
            if not self.web3.is_connected():
                raise SwapError("Web3 连接已断开")
            
            # 转换为 checksum 地址
            from_address = self.web3.to_checksum_address(from_address)
            from_token = self.web3.to_checksum_address(from_token)
            to_token = self.web3.to_checksum_address(to_token)
            
            # 验证地址格式
            if not self.web3.is_address(from_address):
                raise SwapError(f"无效的发送方地址: {from_address}")
            if not self.web3.is_address(from_token):
                raise SwapError(f"无效的源代币地址: {from_token}")
            if not self.web3.is_address(to_token):
                raise SwapError(f"无效的目标代币地址: {to_token}")
            
            # 解析报价数据
            quote_data = json.loads(quote_id)
            tx_data = quote_data['tx']
            
            # 使用 1inch 的路由合约地址
            router_address = self.web3.to_checksum_address("******************************************")
            if not self.web3.is_address(router_address):
                raise SwapError(f"无效的路由合约地址: {router_address}")
            
            # 获取 nonce
            nonce = self.web3.eth.get_transaction_count(from_address)
            logger.info(f"当前 nonce: {nonce}")
            
            # 获取当前 gas 价格
            gas_price = self.web3.eth.gas_price
            logger.info(f"当前 gas 价格: {gas_price}")
            
            # 构建交易
            transaction = {
                'from': from_address,  # 使用实际的钱包地址
                'to': router_address,  # 使用 1inch 路由合约地址
                'data': tx_data['data'],
                'value': int(tx_data['value']),
                'gas': 300000,  # 使用固定的 gas 限制
                'gasPrice': gas_price,  # 使用当前 gas 价格
                'nonce': nonce,
                'chainId': self.chain_id
            }
            
            # 验证交易数据
            if not transaction['data'] or not transaction['data'].startswith('0x'):
                raise SwapError("无效的交易数据")
            
            # 验证 gas 价格
            if transaction['gasPrice'] <= 0:
                raise SwapError("无效的 gas 价格")
            
            # 验证交易值
            if transaction['value'] < 0:
                raise SwapError("无效的交易值")
            
            logger.info(f"交易参数: {json.dumps(transaction, indent=2)}")
            
            # 检查余额
            balance = self.web3.eth.get_balance(from_address)
            required_balance = transaction['gas'] * transaction['gasPrice'] + transaction['value']
            
            # 计算并显示详细的余额信息
            balance_eth = self.web3.from_wei(balance, 'ether')
            required_balance_eth = self.web3.from_wei(required_balance, 'ether')
            gas_cost_eth = self.web3.from_wei(transaction['gas'] * transaction['gasPrice'], 'ether')
            value_eth = self.web3.from_wei(transaction['value'], 'ether')
            
            logger.info(f"账户余额: {balance_eth} ETH")
            logger.info(f"所需余额: {required_balance_eth} ETH")
            logger.info(f"Gas 费用: {gas_cost_eth} ETH")
            logger.info(f"交易值: {value_eth} ETH")
            
            if balance < required_balance:
                raise SwapError(
                    f"余额不足:\n"
                    f"当前余额: {balance_eth} ETH\n"
                    f"所需余额: {required_balance_eth} ETH\n"
                    f"Gas 费用: {gas_cost_eth} ETH\n"
                    f"交易值: {value_eth} ETH\n"
                    f"差额: {self.web3.from_wei(required_balance - balance, 'ether')} ETH"
                )
            
            # 估算 gas
            try:
                estimated_gas = self.web3.eth.estimate_gas(transaction)
                logger.info(f"估算 gas: {estimated_gas}")
                transaction['gas'] = estimated_gas
            except Exception as e:
                error_msg = str(e)
                if "insufficient funds" in error_msg.lower():
                    raise SwapError(f"余额不足: {error_msg}")
                else:
                    logger.warning(f"估算 gas 失败: {error_msg}")
            
            # 签名交易
            signed_txn = self.web3.eth.account.sign_transaction(transaction, private_key)
            
            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_txn.rawTransaction)
            logger.info(f"交易已发送，哈希: {tx_hash.hex()}")
            
            # 等待交易确认，设置超时时间为 60 秒
            try:
                receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=60)
                logger.info(f"交易已确认，区块号: {receipt['blockNumber']}")
            except Exception as e:
                # 如果超时，返回交易哈希，让用户自己查询状态
                logger.warning(f"等待交易确认超时: {str(e)}")
                return {
                    'status': 'pending',
                    'tx_hash': tx_hash.hex(),
                    'message': '交易已发送，等待确认中'
                }
            
            if receipt['status'] == 1:
                logger.info(f"兑换成功，交易哈希: {tx_hash.hex()}")
                return {
                    'status': 'success',
                    'tx_hash': tx_hash.hex(),
                    'receipt': {
                        'blockNumber': receipt['blockNumber'],
                        'gasUsed': receipt['gasUsed'],
                        'effectiveGasPrice': receipt['effectiveGasPrice']
                    }
                }
            else:
                logger.error(f"兑换失败，交易哈希: {tx_hash.hex()}")
                raise SwapError(f"交易失败: {tx_hash.hex()}")
                
        except Exception as e:
            logger.error(f"执行兑换失败: {str(e)}")
            raise SwapError(f"执行兑换失败: {str(e)}")

    async def _get_token_decimals(self, token_address: str) -> int:
        """获取代币精度"""
        try:
            # 如果是原生代币
            if token_address.lower() in ['******************************************', '******************************************']:
                return 18
            
            # ERC20 代币精度
            abi = [{"constant":True,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"payable":False,"stateMutability":"view","type":"function"}]
            contract = self.web3.eth.contract(address=token_address, abi=abi)
            return contract.functions.decimals().call()
        except Exception as e:
            logger.error(f"获取代币精度失败: {str(e)}")
            return 18  # 默认精度

    async def _get_token_prices(self, token_addresses: List[str]) -> Dict[str, Dict[str, str]]:
        """获取代币价格"""
        try:
            prices = {}
            session = await self._get_session()
            
            for address in token_addresses:
                try:
                    # 调用 1inch API 获取价格
                    url = f"{self.oneinch_api_url}/{self.chain_id}/price"
                    params = {'tokenAddress': address}
                    
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            prices[address] = {
                                'price': str(data.get('price', '0')),
                                'price_change_24h': str(data.get('priceChange24h', '0'))
                            }
                        else:
                            prices[address] = {'price': '0', 'price_change_24h': '0'}
                            
                except Exception as e:
                    logger.error(f"获取代币 {address} 价格失败: {str(e)}")
                    prices[address] = {'price': '0', 'price_change_24h': '0'}
                    
                # 添加延迟避免请求过快
                await asyncio.sleep(0.1)
                
            return prices
            
        except Exception as e:
            logger.error(f"获取代币价格失败: {str(e)}")
            return {address: {'price': '0', 'price_change_24h': '0'} for address in token_addresses}

    def get_swap_history(self, wallet_address: str) -> List[Dict[str, Any]]:
        """获取钱包的交换历史"""
        try:
            # 这里可以集成实际的 DEX API 来获取交换历史
            # 暂时返回空列表
            return []
        except Exception as e:
            logger.error(f"获取交换历史失败: {str(e)}")
            raise SwapError(f"获取交换历史失败: {str(e)}")

    async def get_supported_tokens(self) -> List[Dict[str, Any]]:
        """获取支持的代币列表
        
        Returns:
            List[Dict]: 代币列表，包含代币信息
        """
        try:
            logger.info(f"开始获取 {self.chain} 链上支持的代币列表")
            
            # 获取原生代币信息
            native_token = {
                'address': '******************************************',
                'name': self.config.get('name', 'Ethereum'),
                'symbol': self.config.get('symbol', 'ETH'),
                'decimals': 18,
                'logo': self.config.get('logo', ''),
                'is_native': True
            }
            
            # 获取 1inch API 支持的代币列表
            session = await self._get_session()
            url = f"{self.oneinch_api_url}/{self.chain_id}/tokens"
            
            try:
                async with session.get(url) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"1inch API 返回错误: {error_text}")
                        raise Exception(f"获取代币列表失败: {error_text}")
                    
                    data = await response.json()
                    tokens_data = data.get('tokens', {})
                    
                    # 处理代币数据
                    supported_tokens = [native_token]
                    for address, token_data in tokens_data.items():
                        try:
                            token_info = {
                                'address': address,
                                'symbol': token_data.get('symbol'),
                                'name': token_data.get('name'),
                                'decimals': token_data.get('decimals', 18),
                                'logo': token_data.get('logoURI', ''),
                                'is_native': False
                            }
                            if all([token_info['address'], token_info['symbol'], token_info['name']]):
                                supported_tokens.append(token_info)
                        except Exception as e:
                            logger.warning(f"处理代币 {address} 时出错: {str(e)}")
                            continue
                    
                    logger.info(f"成功获取到 {len(supported_tokens)} 个代币")
                    return supported_tokens
            except aiohttp.ClientError as e:
                logger.error(f"请求 1inch API 失败: {str(e)}")
                raise Exception(f"请求 1inch API 失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"获取支持的代币列表失败: {str(e)}")
            raise SwapError(f"获取支持的代币列表失败: {str(e)}") 