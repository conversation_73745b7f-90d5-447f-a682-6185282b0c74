"""
EVM 转账服务
"""

import logging
import os
import time
from typing import Dict, Any, List, Optional
from decimal import Decimal
from common.config import Config
from web3 import Web3
from eth_account import Account

logger = logging.getLogger(__name__)


class EVMTransferService:
    """EVM 转账服务"""

    def __init__(self, chain: str):
        self.config = Config()
        self.chain = chain
        self.web3 = self._get_web3()

    def _get_web3(self) -> Web3:
        """获取 Web3 实例"""
        config = self.config.get_evm_config(self.chain)
        return Web3(Web3.HTTPProvider(config['rpc_url']))

    def _get_account_from_private_key(self, private_key: str, payment_password: str = None) -> Account:
        """从私钥创建账户"""
        try:
            # 检查私钥是否是加密的（Fernet加密特征）
            if private_key.startswith('gAAAAA'):
                if not payment_password:
                    raise ValueError("私钥已加密，需要提供支付密码")

                # 解密私钥
                from wallets.utils import decrypt_private_key
                try:
                    private_key = decrypt_private_key(private_key, payment_password)
                    logger.info("私钥解密成功")
                except Exception as e:
                    raise ValueError(f"私钥解密失败: {str(e)}")

            # 确保私钥格式正确
            if not private_key.startswith('0x'):
                private_key = '0x' + private_key

            return Account.from_key(private_key)
        except Exception as e:
            raise ValueError(f"无效的私钥格式: {str(e)}")

    def transfer_native(self, from_address: str, to_address: str, amount: str, private_key: str, payment_password: str = None) -> Dict[str, Any]:
        """转账原生代币 (ETH, BNB, MATIC 等)"""
        try:
            logger.info(f"开始 {self.chain} 原生代币转账: {from_address} -> {to_address}, 金额: {amount}")
            
            # 验证金额
            try:
                amount_decimal = Decimal(amount)
                if amount_decimal <= 0:
                    raise ValueError("转账金额必须大于0")
                # 转换为 wei
                amount_wei = int(amount_decimal * Decimal(10**18))
            except (ValueError, TypeError) as e:
                raise ValueError(f"无效的转账金额: {amount}")

            # 获取账户
            account = self._get_account_from_private_key(private_key, payment_password)
            
            # 验证发送者地址
            if account.address.lower() != from_address.lower():
                raise ValueError("私钥与发送地址不匹配")

            # 检查余额
            balance = self.web3.eth.get_balance(from_address)
            if balance < amount_wei:
                raise ValueError(f"余额不足: 当前余额 {Web3.from_wei(balance, 'ether')} {self._get_native_symbol()}")

            # 获取 nonce
            nonce = self.web3.eth.get_transaction_count(from_address)

            # 估算 gas
            gas_estimate = self.web3.eth.estimate_gas({
                'from': from_address,
                'to': to_address,
                'value': amount_wei
            })

            # 获取 gas price
            gas_price = self.web3.eth.gas_price

            # 构建交易
            transaction = {
                'nonce': nonce,
                'to': to_address,
                'value': amount_wei,
                'gas': gas_estimate,
                'gasPrice': gas_price,
                'chainId': self._get_chain_id()
            }

            # 获取解密后的私钥用于签名
            decrypted_private_key = private_key
            if private_key.startswith('gAAAAA'):
                # 私钥是加密的，需要解密
                if not payment_password:
                    raise ValueError("私钥已加密，需要提供支付密码")
                from wallets.utils import decrypt_private_key
                decrypted_private_key = decrypt_private_key(private_key, payment_password)
                logger.info("使用支付密码解密私钥")
            else:
                # 私钥已经是明文，直接使用
                logger.info("使用明文私钥")

            # 确保私钥格式正确
            if not decrypted_private_key.startswith('0x'):
                decrypted_private_key = '0x' + decrypted_private_key

            # 签名交易
            signed_txn = self.web3.eth.account.sign_transaction(transaction, decrypted_private_key)

            # 发送交易
            try:
                tx_hash = self.web3.eth.send_raw_transaction(signed_txn.rawTransaction)
                tx_hash_hex = tx_hash.hex()

                logger.info(f"{self.chain} 交易已提交到网络: {tx_hash_hex}")

                # 验证交易是否真的存在于网络中
                try:
                    # 等待一小段时间让交易传播
                    import time
                    time.sleep(3)

                    # 尝试获取交易信息来验证
                    tx_info = self.web3.eth.get_transaction(tx_hash_hex)
                    if tx_info:
                        logger.info(f"{self.chain} 交易验证成功: {tx_hash_hex}")

                        # 进一步验证交易内容
                        if (tx_info.get('from', '').lower() == from_address.lower() and
                            tx_info.get('to', '').lower() == to_address.lower() and
                            tx_info.get('value') == amount_wei):
                            logger.info(f"{self.chain} 交易内容验证成功: {tx_hash_hex}")
                        else:
                            logger.warning(f"{self.chain} 交易内容不匹配: {tx_hash_hex}")
                    else:
                        logger.error(f"{self.chain} 交易验证失败，交易不存在: {tx_hash_hex}")
                        raise Exception(f"交易提交后无法在网络中找到: {tx_hash_hex}")

                except Exception as verify_error:
                    if "not found" in str(verify_error).lower():
                        logger.error(f"{self.chain} 交易在网络中不存在: {tx_hash_hex}")
                        raise Exception(f"交易提交失败，网络中找不到交易: {tx_hash_hex}")
                    else:
                        logger.warning(f"{self.chain} 交易验证出错: {verify_error}，但继续返回哈希: {tx_hash_hex}")

                logger.info(f"{self.chain} 原生代币转账成功: {tx_hash_hex}")
                return {
                    'status': 'success',
                    'transaction_hash': tx_hash_hex,
                    'from_address': from_address,
                    'to_address': to_address,
                    'amount': amount,
                    'amount_wei': amount_wei,
                    'gas_used': gas_estimate,
                    'gas_price': gas_price,
                    'chain': self.chain
                }

            except Exception as send_error:
                logger.error(f"{self.chain} 发送交易失败: {send_error}")
                raise Exception(f"发送交易失败: {send_error}")
                
        except Exception as e:
            logger.error(f"{self.chain} 原生代币转账异常: {str(e)}")
            raise Exception(f"{self.chain} 原生代币转账失败: {str(e)}")

    def transfer_token(self, from_address: str, to_address: str, token_address: str, 
                      amount: str, private_key: str) -> Dict[str, Any]:
        """转账 ERC20 代币"""
        try:
            logger.info(f"开始 {self.chain} ERC20 代币转账: {from_address} -> {to_address}, 代币: {token_address}, 金额: {amount}")
            
            # 验证金额
            try:
                amount_decimal = Decimal(amount)
                if amount_decimal <= 0:
                    raise ValueError("转账金额必须大于0")
            except (ValueError, TypeError) as e:
                raise ValueError(f"无效的转账金额: {amount}")

            # 获取账户
            account = self._get_account_from_private_key(private_key)
            
            # 验证发送者地址
            if account.address.lower() != from_address.lower():
                raise ValueError("私钥与发送地址不匹配")

            # 获取代币合约
            token_contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(token_address),
                abi=self._get_erc20_abi()
            )

            # 获取代币精度
            decimals = token_contract.functions.decimals().call()
            
            # 转换金额为最小单位
            amount_in_smallest_unit = int(amount_decimal * Decimal(10**decimals))

            # 检查代币余额
            token_balance = token_contract.functions.balanceOf(from_address).call()
            if token_balance < amount_in_smallest_unit:
                raise ValueError(f"代币余额不足: 当前余额 {token_balance / (10**decimals)}")

            # 获取 nonce
            nonce = self.web3.eth.get_transaction_count(from_address)

            # 构建转账交易
            transfer_function = token_contract.functions.transfer(
                Web3.to_checksum_address(to_address),
                amount_in_smallest_unit
            )

            # 估算 gas
            gas_estimate = transfer_function.estimate_gas({'from': from_address})

            # 获取 gas price
            gas_price = self.web3.eth.gas_price

            # 构建交易
            transaction = transfer_function.build_transaction({
                'from': from_address,
                'nonce': nonce,
                'gas': gas_estimate,
                'gasPrice': gas_price,
                'chainId': self._get_chain_id()
            })

            # 签名交易
            signed_txn = self.web3.eth.account.sign_transaction(transaction, private_key)

            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            logger.info(f"{self.chain} ERC20 代币转账成功: {tx_hash.hex()}")
            return {
                'status': 'success',
                'transaction_hash': tx_hash.hex(),
                'from_address': from_address,
                'to_address': to_address,
                'token_address': token_address,
                'amount': amount,
                'amount_in_smallest_unit': amount_in_smallest_unit,
                'decimals': decimals,
                'gas_used': gas_estimate,
                'gas_price': gas_price,
                'chain': self.chain
            }
                
        except Exception as e:
            logger.error(f"{self.chain} ERC20 代币转账异常: {str(e)}")
            raise Exception(f"{self.chain} ERC20 代币转账失败: {str(e)}")

    def estimate_fee(self, from_address: str, to_address: str, amount: str, 
                    token_address: Optional[str] = None) -> Dict[str, Any]:
        """估算转账手续费"""
        try:
            if token_address:
                # ERC20 代币转账
                token_contract = self.web3.eth.contract(
                    address=Web3.to_checksum_address(token_address),
                    abi=self._get_erc20_abi()
                )
                
                # 获取代币精度
                decimals = token_contract.functions.decimals().call()
                amount_decimal = Decimal(amount)
                amount_in_smallest_unit = int(amount_decimal * Decimal(10**decimals))
                
                # 估算 gas
                gas_estimate = token_contract.functions.transfer(
                    Web3.to_checksum_address(to_address),
                    amount_in_smallest_unit
                ).estimate_gas({'from': from_address})
            else:
                # 原生代币转账
                amount_decimal = Decimal(amount)
                amount_wei = int(amount_decimal * Decimal(10**18))
                
                # 估算 gas
                gas_estimate = self.web3.eth.estimate_gas({
                    'from': from_address,
                    'to': to_address,
                    'value': amount_wei
                })

            # 获取 gas price
            gas_price = self.web3.eth.gas_price
            
            # 计算手续费
            fee_wei = gas_estimate * gas_price
            fee_ether = Web3.from_wei(fee_wei, 'ether')
            
            return {
                'estimated_fee': str(fee_ether),
                'estimated_fee_wei': fee_wei,
                'gas_estimate': gas_estimate,
                'gas_price': gas_price,
                'chain': self.chain,
                'token_address': token_address
            }
            
        except Exception as e:
            logger.error(f"估算 {self.chain} 手续费失败: {str(e)}")
            raise Exception(f"估算手续费失败: {str(e)}")

    def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """
        获取交易状态

        Args:
            tx_hash: 交易哈希

        Returns:
            交易状态信息，包含status字段：success, failed, pending
        """
        try:
            logger.info(f"检查 {self.chain} 交易状态: {tx_hash}")

            # 如果交易哈希以pending_开头，说明是临时哈希，还没有真实的交易哈希
            if tx_hash.startswith('pending_'):
                logger.info(f"临时交易哈希，无法查询状态: {tx_hash}")
                return {'status': 'pending'}

            # 获取交易收据
            try:
                receipt = self.web3.eth.get_transaction_receipt(tx_hash)

                # 检查交易状态
                if receipt and receipt.get('status') == 1:
                    logger.info(f"交易已确认成功: {tx_hash}")
                    return {
                        'status': 'success',
                        'block_number': receipt.get('blockNumber'),
                        'gas_used': receipt.get('gasUsed')
                    }
                elif receipt and receipt.get('status') == 0:
                    logger.info(f"交易已确认但执行失败: {tx_hash}")
                    return {
                        'status': 'failed',
                        'block_number': receipt.get('blockNumber'),
                        'gas_used': receipt.get('gasUsed')
                    }
                else:
                    # 交易收据存在但状态未知
                    logger.info(f"交易状态未知: {tx_hash}")
                    return {'status': 'pending'}

            except Exception as e:
                # 如果获取交易收据失败，尝试获取交易信息来确认交易是否存在
                if "not found" in str(e).lower() or "transaction not found" in str(e).lower():
                    logger.info(f"交易收据未找到，检查交易是否存在: {tx_hash}")

                    # 尝试获取交易信息
                    try:
                        tx_info = self.web3.eth.get_transaction(tx_hash)
                        if tx_info:
                            # 检查交易是否有效（检查签名）
                            if (tx_info.get('blockNumber') is None and
                                tx_info.get('r') == b'\x00' and
                                tx_info.get('s') == b'\x00'):
                                logger.warning(f"交易签名无效，永远不会被打包: {tx_hash}")
                                return {'status': 'not_found'}
                            else:
                                logger.info(f"交易存在但未确认: {tx_hash}")
                                return {'status': 'pending'}
                        else:
                            logger.warning(f"交易不存在: {tx_hash}")
                            return {'status': 'not_found'}
                    except Exception as tx_error:
                        if "not found" in str(tx_error).lower():
                            logger.warning(f"交易在网络中不存在: {tx_hash}")
                            return {'status': 'not_found'}
                        else:
                            logger.error(f"检查交易存在性失败: {str(tx_error)}")
                            return {'status': 'pending'}
                else:
                    logger.error(f"获取交易收据失败: {str(e)}")
                    return {'status': 'pending'}

        except Exception as e:
            logger.error(f"检查 {self.chain} 交易状态失败: {str(e)}")
            return {'status': 'pending'}

    def get_transaction_history(self, address: str, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """获取交易历史"""
        try:
            logger.info(f"获取 {self.chain} 交易历史: {address}, 页码: {page}, 限制: {limit}")

            # 使用Moralis API获取交易历史
            moralis_api_key = os.getenv("MORALIS_API_KEY")
            if not moralis_api_key:
                logger.warning("未配置MORALIS_API_KEY，返回空交易历史")
                return {
                    'transactions': [],
                    'total': 0,
                    'page': page,
                    'limit': limit,
                    'chain': self.chain,
                    'address': address
                }

            # 构建Moralis API URL
            moralis_url = "https://deep-index.moralis.io/api/v2"
            headers = {
                "X-API-Key": moralis_api_key,
                "Content-Type": "application/json"
            }

            # 获取链ID映射
            chain_mapping = {
                'ETH': 'eth',
                'BSC': 'bsc',
                'MATIC': 'polygon',
                'BASE': 'base',
                'ARB': 'arbitrum',
                'OP': 'optimism',
                'AVAX': 'avalanche',
                'FTM': 'fantom'
            }

            chain_name = chain_mapping.get(self.chain, 'eth')

            # 调用Moralis API获取交易历史
            import requests
            response = requests.get(
                f"{moralis_url}/{address}",
                headers=headers,
                params={
                    "chain": chain_name,
                    "limit": limit,
                    "offset": (page - 1) * limit
                },
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                transactions = []

                for tx in data.get('result', []):
                    transaction_data = self._parse_moralis_transaction(tx, address)
                    if transaction_data:
                        transactions.append(transaction_data)

                return {
                    'transactions': transactions,
                    'total': data.get('total', len(transactions)),
                    'page': page,
                    'limit': limit,
                    'chain': self.chain,
                    'address': address
                }
            else:
                logger.error(f"Moralis API调用失败: {response.status_code} - {response.text}")
                return {
                    'transactions': [],
                    'total': 0,
                    'page': page,
                    'limit': limit,
                    'chain': self.chain,
                    'address': address
                }

        except Exception as e:
            logger.error(f"获取 {self.chain} 交易历史失败: {str(e)}")
            return {
                'transactions': [],
                'total': 0,
                'page': page,
                'limit': limit,
                'chain': self.chain,
                'address': address
            }

    def _parse_moralis_transaction(self, tx_data: Dict[str, Any], user_address: str) -> Dict[str, Any]:
        """解析Moralis交易数据"""
        try:
            # 获取基本信息
            tx_hash = tx_data.get('hash', '')
            block_timestamp = tx_data.get('block_timestamp', '')
            from_address = tx_data.get('from_address', '').lower()
            to_address = tx_data.get('to_address', '').lower()
            value = tx_data.get('value', '0')
            gas_price = tx_data.get('gas_price', '0')
            gas_used = tx_data.get('receipt_gas_used', '0')

            # 计算手续费
            fee = 0
            if gas_price and gas_used:
                fee = int(gas_price) * int(gas_used)

            # 确定交易类型
            user_address_lower = user_address.lower()
            is_outgoing = from_address == user_address_lower
            is_incoming = to_address == user_address_lower

            transaction_type = 'transaction'
            if is_outgoing:
                transaction_type = 'sent'
            elif is_incoming:
                transaction_type = 'received'

            # 转换金额（从wei到ETH/BNB等）
            amount_in_ether = 0
            if value and value != '0':
                amount_in_ether = int(value) / 10**18

            # 获取原生代币符号、名称、logo
            native_symbols = {
                'ETH': ('ETH', 'Ethereum', 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'),
                'BSC': ('BNB', 'BNB', 'https://assets.coingecko.com/coins/images/825/large/binance-coin-logo.png'),
                'MATIC': ('MATIC', 'Polygon', 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png'),
                'BASE': ('ETH', 'Ethereum', 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'),
                'ARB': ('ETH', 'Ethereum', 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'),
                'OP': ('ETH', 'Ethereum', 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'),
                'AVAX': ('AVAX', 'Avalanche', 'https://assets.coingecko.com/coins/images/12559/large/coin-round-red.png'),
                'FTM': ('FTM', 'Fantom', 'https://assets.coingecko.com/coins/images/4001/large/Fantom.png')
            }
            symbol, name, logo = native_symbols.get(self.chain, ('ETH', 'Ethereum', 'https://assets.coingecko.com/coins/images/279/large/ethereum.png'))

            # Moralis 可能返回 token_symbol, token_name, token_logo
            token_symbol = tx_data.get('token_symbol', symbol)
            token_name = tx_data.get('token_name', name)
            token_logo = tx_data.get('token_logo', logo)

            # 解析时间戳
            import datetime
            block_time = None
            if block_timestamp:
                try:
                    block_time = datetime.datetime.fromisoformat(block_timestamp.replace('Z', '+00:00')).timestamp()
                except:
                    block_time = None

            return {
                'signature': tx_hash,
                'hash': tx_hash,
                'block_time': block_time,
                'status': 'success',  # Moralis通常只返回成功的交易
                'type': transaction_type,
                'amount': str(amount_in_ether) if amount_in_ether > 0 else '0',
                'token_symbol': token_symbol,
                'token_name': token_name,
                'token_logo': token_logo,
                'from_address': from_address,
                'to_address': to_address,
                'fee': fee,
                'gas_price': gas_price,
                'gas_used': gas_used
            }

        except Exception as e:
            logger.warning(f"解析Moralis交易数据失败: {str(e)}")
            return None

    def _get_chain_id(self) -> int:
        """获取链 ID"""
        chain_ids = {
            'ETH': 1,
            'BSC': 56,
            'MATIC': 137,
            'ARB': 42161,
            'OP': 10,
            'AVAX': 43114,
            'BASE': 8453,
            'FTM': 250,
            'CRO': 25,
            # 测试网
            'ETH_SEPOLIA': 11155111,
            'BSC_TESTNET': 97,
            'MATIC_MUMBAI': 80001
        }
        return chain_ids.get(self.chain, 1)

    def _get_native_symbol(self) -> str:
        """获取原生代币符号"""
        symbols = {
            'ETH': 'ETH',
            'BSC': 'BNB',
            'MATIC': 'MATIC',
            'ARB': 'ETH',
            'OP': 'ETH',
            'AVAX': 'AVAX',
            'BASE': 'ETH',
            'FTM': 'FTM',
            'CRO': 'CRO'
        }
        base_chain = self.chain.split('_')[0]
        return symbols.get(base_chain, 'ETH')

    def _get_erc20_abi(self) -> List[Dict]:
        """获取 ERC20 ABI"""
        return [
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": False,
                "inputs": [
                    {"name": "_to", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "transfer",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            }
        ]
