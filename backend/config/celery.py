import os
from celery import Celery
from django.conf import settings

# 设置默认的Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

app = Celery('cocowallet')

# 使用字符串表示，这样worker不用序列化配置对象
app.config_from_object('django.conf:settings', namespace='CELERY')

# 从所有已注册的Django应用中加载任务模块
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

# Celery配置
app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',

    # 结果后端
    result_backend='redis://localhost:6379/0',

    # 消息代理
    broker_url='redis://localhost:6379/0',

    # 任务路由
    task_routes={
        'wallets.tasks.*': {'queue': 'celery'},
    },

    # 任务超时
    task_time_limit=300,  # 5分钟
    task_soft_time_limit=240,  # 4分钟

    # 工作进程配置
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,

    # 时区
    timezone='UTC',
)

# 配置定时任务
app.conf.beat_schedule = {
    'update-pending-transactions': {
        'task': 'wallets.tasks.update_pending_transactions',
        'schedule': 10.0,  # 每10秒执行一次
        'args': (),
    },
}

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
