# 钱包 API 技术文档

## 概述

本文档描述了钱包管理相关的 API 接口，包括钱包管理、余额查询、代币价格获取、转账等功能。

## 基础信息

- **Base URL**: `http://************:8000/api/v1/wallets/`
- **Content-Type**: `application/json`
- **支持的区块链**: ETH, BSC, MATIC, ARB, OP, AVAX, BASE, ZKSYNC, LINEA, MANTA, FTM, CRO, SOL, KDA

## 钱包管理接口

### 1. 获取钱包列表

获取用户的所有钱包列表。

**接口地址**: `GET /`

**请求示例**:
```bash
curl --location 'http://************:8000/api/v1/wallets/' \
--header 'Content-Type: application/json'
```

### 2. 导入私钥创建钱包

通过私钥导入创建新钱包。

**接口地址**: `POST /import_private_key/`

**请求参数**:
```json
{
    "private_key": "0x...",
    "chain": "ETH",
    "wallet_name": "我的钱包"
}
```

### 3. 通过助记词导入钱包

通过助记词导入创建新钱包。

**接口地址**: `POST /import_by_mnemonic/`

**请求参数**:
```json
{
    "mnemonic": "word1 word2 ... word12",
    "chain": "ETH",
    "wallet_name": "我的钱包"
}
```

### 4. 导入观察钱包

创建只读的观察钱包。

**接口地址**: `POST /import_watch_only/`

**请求参数**:
```json
{
    "address": "0x...",
    "chain": "ETH",
    "wallet_name": "观察钱包"
}
```

## 余额查询接口

### 5. 获取钱包原生代币余额

获取钱包的原生代币（如 ETH、BNB、SOL 等）余额。

**接口地址**: `GET /{pk}/get_balance/`

**请求参数**:
- `pk` (路径参数): 钱包 ID

**请求示例**:
```bash
curl --location 'http://************:8000/api/v1/wallets/5/get_balance/' \
--header 'Content-Type: application/json'
```

**响应示例**:
```json
{
    "token_address": "",
    "symbol": "ETH",
    "name": "Ethereum",
    "balance": "0.03",
    "balance_formatted": "0.0300",
    "decimals": 18,
    "logo": "https://cryptologos.cc/logos/ethereum-eth-logo.png",
    "current_price_usd": 2638.12,
    "price_change_24h": 1.25
}
```

### 6. 获取特定代币余额

获取钱包中特定代币的余额信息。

**接口地址**: `GET /{pk}/get_token_balance/`

**请求参数**:
- `pk` (路径参数): 钱包 ID
- `token_address` (查询参数): 代币合约地址

**请求示例**:
```bash
curl --location 'http://************:8000/api/v1/wallets/5/get_token_balance/?token_address=******************************************' \
--header 'Content-Type: application/json'
```

**响应示例**:
```json
{
    "balance": "1000.5",
    "decimals": 18,
    "symbol": "USDT"
}
```

### 7. 获取钱包所有代币余额

获取钱包中所有可见代币的余额和价格信息（从数据库读取）。

**接口地址**: `GET /{pk}/get_all_balances/`

**请求参数**:
- `pk` (路径参数): 钱包 ID

**请求示例**:
```bash
curl --location 'http://************:8000/api/v1/wallets/5/get_all_balances/' \
--header 'Content-Type: application/json'
```

**响应示例**:
```json
{
    "status": "success",
    "timestamp": 1749033746.310257,
    "wallet_id": 5,
    "chain": "ETH",
    "total_value_usd": "78.69",
    "total_value_change_24h": "0.34",
    "total_change_percentage": "0.43",
    "tokens": [
        {
            "id": 3,
            "token_address": "",
            "symbol": "ETH",
            "name": "Ethereum",
            "logo": "https://cryptologos.cc/logos/ethereum-eth-logo.png",
            "is_visible": true,
            "balance": 0.03,
            "balance_formatted": "0.0300",
            "current_price_usd": 2623.0,
            "value_usd": 78.69,
            "value_change_24h": 0.34,
            "price_change_percentage_24h": 0.43
        }
    ]
}
```

**响应字段说明**:
- `total_value_usd`: 钱包总价值（美元）
- `total_value_change_24h`: 24小时价值变化（美元）
- `total_change_percentage`: 24小时变化百分比
- `tokens`: 代币列表
  - `token_address`: 代币合约地址（原生代币为空字符串）
  - `balance`: 原始余额
  - `balance_formatted`: 格式化后的余额
  - `current_price_usd`: 当前美元价格
  - `value_usd`: 代币总价值（余额 × 价格）
  - `price_change_percentage_24h`: 24小时价格变化百分比

### 8. 刷新钱包余额和价格

从区块链网络同步获取最新的余额和价格信息，并更新到数据库。

**接口地址**: `POST /{pk}/refresh_balances/`

**请求参数**:
- `pk` (路径参数): 钱包 ID

**请求示例**:
```bash
curl --location --request POST 'http://************:8000/api/v1/wallets/5/refresh_balances/' \
--header 'Content-Type: application/json' \
--data '{}'
```

**响应示例**:
```json
{
    "wallet_address": "******************************************",
    "refresh_time": "2025-06-04 10:42:16",
    "token_count": 1,
    "status": "success",
    "execution_time": "4.90 秒"
}
```

**功能说明**:
- 从区块链网络获取最新余额
- 同步更新代币价格（使用 CryptoCompare API）
- 更新数据库中的余额和价格信息
- 处理代币元数据（名称、符号、logo 等）

### 9. 获取代币价格

同步获取钱包中所有代币的最新价格，不更新余额数据。

**接口地址**: `GET /{pk}/get_token_prices/`

**请求参数**:
- `pk` (路径参数): 钱包 ID

**请求示例**:
```bash
curl --location 'http://************:8000/api/v1/wallets/5/get_token_prices/' \
--header 'Content-Type: application/json'
```

**响应示例**:
```json
{
    "status": "success",
    "wallet_id": 5,
    "chain": "ETH",
    "total_value_usd": "78.69",
    "total_value_change_24h": "0.34",
    "total_change_percentage": "0.43",
    "prices_updated": 1,
    "execution_time": "2.70 秒"
}
```

**功能说明**:
- 只更新价格，不更新余额
- 支持原生代币和 ERC20 代币
- 立即返回计算后的总价值

## 代币管理接口

### 10. 获取代币元数据

获取特定代币的详细元数据信息。

**接口地址**: `GET /{pk}/token_metadata/`

**请求参数**:
- `pk` (路径参数): 钱包 ID
- `token_address` (查询参数): 代币合约地址

### 11. 获取代币价格历史

获取代币的历史价格数据。

**接口地址**: `GET /{pk}/token_price_history/`

**请求参数**:
- `pk` (路径参数): 钱包 ID
- `token_address` (查询参数): 代币合约地址
- `period` (查询参数): 时间周期（可选）

### 12. 代币可见性管理

设置代币在钱包中的可见性。

**接口地址**: `POST /{wallet_id}/set-token-visibility/`

**请求参数**:
```json
{
    "token_address": "0x...",
    "is_visible": true
}
```

### 13. 代币管理列表

获取钱包中所有代币的管理信息。

**接口地址**: `GET /{wallet_id}/token-management/`

## 转账接口

### 14. 发起转账

发起代币转账交易。

**接口地址**: `POST /{pk}/transfer/`

**请求参数**:
```json
{
    "to_address": "0x...",
    "amount": "1.0",
    "token_address": "",
    "password": "支付密码"
}
```

### 15. 估算手续费

估算转账所需的手续费。

**接口地址**: `GET /{pk}/estimate_fee/`

**请求参数**:
- `to_address` (查询参数): 接收地址
- `amount` (查询参数): 转账金额
- `token_address` (查询参数): 代币地址（可选，空为原生代币）

### 16. 交易历史

获取钱包的交易历史记录。

**接口地址**: `GET /{pk}/transaction_history/`

**请求参数**:
- `pk` (路径参数): 钱包 ID
- `page` (查询参数): 页码（可选）
- `limit` (查询参数): 每页数量（可选）

## 钱包操作接口

### 17. 显示私钥

显示钱包的私钥（需要支付密码验证）。

**接口地址**: `POST /{pk}/show_private_key/`

**请求参数**:
```json
{
    "password": "支付密码"
}
```

### 18. 重命名钱包

修改钱包名称。

**接口地址**: `POST /{pk}/rename_wallet/`

**请求参数**:
```json
{
    "wallet_name": "新的钱包名称"
}
```

### 19. 删除钱包

删除钱包（需要支付密码验证）。

**接口地址**: `POST /{pk}/delete_wallet/`

**请求参数**:
```json
{
    "password": "支付密码"
}
```

### 20. 更新 Kadena 链 ID

更新 Kadena 钱包的链 ID。

**接口地址**: `POST /{pk}/update_kadena_chain_id/`

**请求参数**:
```json
{
    "chain_id": "1"
}
```

## 系统接口

### 21. 获取支持的链

获取系统支持的所有区块链列表。

**接口地址**: `GET /get_supported_chains/`

### 22. 选择链

选择要操作的区块链。

**接口地址**: `POST /select_chain/`

**请求参数**:
```json
{
    "chain": "ETH"
}
```

### 23. 验证助记词

验证助记词的有效性。

**接口地址**: `POST /verify_mnemonic/`

**请求参数**:
```json
{
    "mnemonic": "word1 word2 ... word12"
}
```

## 支付密码接口

### 24. 设置支付密码

设置或修改支付密码。

**接口地址**: `POST /set_password/`

**请求参数**:
```json
{
    "device_id": "设备ID",
    "password": "新密码"
}
```

### 25. 验证支付密码

验证支付密码是否正确。

**接口地址**: `POST /verify_password/`

**请求参数**:
```json
{
    "device_id": "设备ID",
    "password": "密码"
}
```

### 26. 修改支付密码

修改支付密码。

**接口地址**: `POST /change_password/`

**请求参数**:
```json
{
    "device_id": "设备ID",
    "old_password": "旧密码",
    "new_password": "新密码"
}
```

### 27. 支付密码状态

获取设备的支付密码设置状态。

**接口地址**: `GET /payment_password/status/{device_id}/`

**请求参数**:
- `device_id` (路径参数): 设备 ID

## 错误响应

所有接口在出错时都会返回统一的错误格式：

```json
{
    "status": "failed",
    "error": "错误描述信息",
    "total_value_usd": "0",
    "total_value_change_24h": "0",
    "total_change_percentage": "0",
    "tokens": []
}
```

**常见错误码**:
- `404`: 钱包不存在
- `400`: 不支持的链类型
- `500`: 服务器内部错误

## 使用建议

### 前端调用流程

1. **初始加载**: 调用 `get_all_balances` 获取缓存的余额和价格
2. **用户刷新**: 调用 `refresh_balances` 获取最新数据
3. **价格更新**: 定期调用 `get_token_prices` 更新价格（可选）

### 性能优化

- `get_all_balances`: 快速响应，从数据库读取
- `refresh_balances`: 较慢，需要访问区块链网络
- `get_token_prices`: 中等速度，只更新价格

### 数据一致性

- 调用 `refresh_balances` 后，立即调用 `get_all_balances` 可获取最新数据
- 价格数据来源于 CryptoCompare API，实时性较好
- 余额数据来源于区块链网络，准确性最高

## 特殊说明

### 原生代币处理

不同链的原生代币：
- **ETH 系列**: ETH (Ethereum, Arbitrum, Optimism, Base, zkSync, Linea, Manta)
- **BSC**: BNB
- **MATIC**: MATIC
- **AVAX**: AVAX
- **FTM**: FTM
- **CRO**: CRO
- **SOL**: SOL
- **KDA**: KDA

原生代币在 API 中的 `token_address` 字段为空字符串 `""`。

### 支持的链类型

| 链名称 | 链标识 | 测试网 | 原生代币 |
|--------|--------|--------|----------|
| Ethereum | ETH | ETH_SEPOLIA, ETH_GOERLI | ETH |
| Binance Smart Chain | BSC | BSC_TESTNET | BNB |
| Polygon | MATIC | MATIC_MUMBAI | MATIC |
| Arbitrum | ARB | ARB_SEPOLIA | ETH |
| Optimism | OP | OP_GOERLI | ETH |
| Avalanche | AVAX | AVAX_FUJI | AVAX |
| Base | BASE | BASE_SEPOLIA | ETH |
| zkSync | ZKSYNC | ZKSYNC_TESTNET | ETH |
| Linea | LINEA | LINEA_GOERLI | ETH |
| Manta | MANTA | MANTA_TESTNET | ETH |
| Fantom | FTM | FTM_TESTNET | FTM |
| Cronos | CRO | CRO_TESTNET | CRO |
| Solana | SOL | SOL_DEVNET, SOL_TESTNET | SOL |
| Kadena | KDA | KDA_TESTNET | KDA |

### 价格数据源

- **原生代币**: CryptoCompare API
- **ERC20/SPL 代币**: Moralis API（如果可用）+ CryptoCompare API（备用）
- **更新频率**: 实时获取，无缓存

### 余额精度

- **原生代币**: 通常 18 位小数（ETH、BNB 等）
- **ERC20 代币**: 根据代币合约的 decimals 字段
- **Solana 代币**: 通常 6-9 位小数
- **Kadena 代币**: 通常 12 位小数

## 前端集成示例

### React/JavaScript 示例

```javascript
class WalletAPI {
  constructor(baseURL = 'http://************:8000/api/v1/wallets') {
    this.baseURL = baseURL;
  }

  // 获取钱包余额
  async getWalletBalances(walletId) {
    const response = await fetch(`${this.baseURL}/${walletId}/get_all_balances/`);
    return await response.json();
  }

  // 刷新钱包余额
  async refreshWalletBalances(walletId) {
    const response = await fetch(`${this.baseURL}/${walletId}/refresh_balances/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });
    return await response.json();
  }

  // 获取代币价格
  async getTokenPrices(walletId) {
    const response = await fetch(`${this.baseURL}/${walletId}/get_token_prices/`);
    return await response.json();
  }
}

// 使用示例
const walletAPI = new WalletAPI();

// 获取钱包余额
walletAPI.getWalletBalances(5).then(data => {
  console.log('钱包总价值:', data.total_value_usd);
  console.log('代币列表:', data.tokens);
});

// 刷新余额
walletAPI.refreshWalletBalances(5).then(data => {
  console.log('刷新完成:', data.status);
  // 刷新后重新获取余额
  return walletAPI.getWalletBalances(5);
}).then(data => {
  console.log('最新余额:', data);
});
```

### Vue.js 示例

```javascript
// 在 Vue 组件中使用
export default {
  data() {
    return {
      walletId: 5,
      balances: null,
      loading: false,
      error: null
    }
  },
  methods: {
    async loadBalances() {
      this.loading = true;
      this.error = null;
      try {
        const response = await fetch(`/api/v1/wallets/${this.walletId}/get_all_balances/`);
        this.balances = await response.json();
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async refreshBalances() {
      this.loading = true;
      try {
        await fetch(`/api/v1/wallets/${this.walletId}/refresh_balances/`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: '{}'
        });
        // 刷新后重新加载
        await this.loadBalances();
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    }
  },
  mounted() {
    this.loadBalances();
  }
}
```

## 注意事项

1. **网络延迟**: `refresh_balances` 接口可能需要 3-10 秒，请在前端显示加载状态
2. **错误处理**: 请妥善处理网络错误和 API 错误响应
3. **频率限制**: 避免频繁调用 `refresh_balances`，建议用户主动触发
4. **数据格式**: 所有金额字段都是字符串或数字，请注意类型转换
5. **时区**: 时间戳为 UTC 时间，请根据需要转换为本地时间
