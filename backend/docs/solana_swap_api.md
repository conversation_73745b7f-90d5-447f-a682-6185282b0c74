# Solana 代币兑换接口文档

## 基础信息

- 基础URL: `/api/v1/wallets`
- 认证方式: 不需要认证
- 请求格式: JSON
- 响应格式: JSON

## 通用请求头

```
Content-Type: application/json
Accept: application/json
```

## 1. 获取支持的代币列表

获取支持兑换的代币列表。

### 请求信息

- 方法: `GET`
- 路径: `/{wallet_id}/swap/tokens/`

### 请求参数

#### Query Parameters

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| device_id | string | 是 | 设备ID |

### 请求示例

```
GET /api/v1/wallets/123/swap/tokens/?device_id=device123
```

### 响应示例

```json
{
    "status": "success",
    "data": {
        "tokens": [
            {
                "address": "So11111111111111111111111111111111111111112",
                "name": "Solana",
                "symbol": "SOL",
                "decimals": 9,
                "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
            },
            {
                "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "name": "USD Coin",
                "symbol": "USDC",
                "decimals": 6,
                "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png"
            }
        ]
    }
}
```

## 2. 获取兑换报价

获取两个代币之间的兑换报价信息。

### 请求信息

- 方法: `GET`
- 路径: `/{wallet_id}/swap/quote/`

### 请求参数

#### Query Parameters

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| device_id | string | 是 | 设备ID |
| from_token | string | 是 | 源代币地址 |
| to_token | string | 是 | 目标代币地址 |
| amount | string | 是 | 兑换数量 |
| slippage | string | 否 | 滑点容忍度（可选，例如："0.5"表示0.5%） |

### 请求示例

```
GET /api/v1/wallets/123/swap/quote/?device_id=device123&from_token=So11111111111111111111111111111111111111112&to_token=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=1.5&slippage=0.5
```

### 响应示例

```json
{
    "status": "success",
    "quote_id": "{\"inputMint\":\"So11111111111111111111111111111111111111112\",\"outputMint\":\"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v\",\"inAmount\":\"1500000000\",\"outAmount\":\"150000000\"}",
    "from_token": "So11111111111111111111111111111111111111112",
    "to_token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "in_amount": "1.5",
    "out_amount": "150.0",
    "price_impact": "0.1",
    "slippage": "0.5",
    "route_plan": [],
    "other_amount_threshold": "149250000",
    "swap_mode": "ExactIn",
    "fees": {},
    "timestamp": 1640995200
}
```

## 3. 执行代币兑换

执行实际的代币兑换交易。

### 请求信息

- 方法: `POST`
- 路径: `/{wallet_id}/swap/execute/`

### 请求参数

#### Body Parameters

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| device_id | string | 是 | 设备ID |
| quote_id | string | 是 | 报价ID（从获取报价接口返回） |
| from_token | string | 是 | 源代币地址 |
| to_token | string | 是 | 目标代币地址 |
| amount | string | 是 | 兑换数量 |
| payment_password | string | 是 | 支付密码 |
| slippage | string | 否 | 滑点容忍度（可选） |

### 请求示例

```json
{
    "device_id": "device123",
    "quote_id": "{\"inputMint\":\"So11111111111111111111111111111111111111112\",\"outputMint\":\"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v\",\"inAmount\":\"1500000000\",\"outAmount\":\"150000000\"}",
    "from_token": "So11111111111111111111111111111111111111112",
    "to_token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "amount": "1.5",
    "payment_password": "123456",
    "slippage": "0.5"
}
```

### 响应示例

```json
{
    "status": "success",
    "data": {
        "status": "success",
        "message": "Swap executed successfully",
        "transaction_data": {
            "signature": "5j7s1QjCeKjqXGAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAqQKAq"
        }
    }
}
```

## 4. 获取代币价格

获取指定代币的价格信息。

### 请求信息

- 方法: `GET`
- 路径: `/{wallet_id}/swap/prices/`

### 请求参数

#### Query Parameters

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| device_id | string | 是 | 设备ID |
| token_addresses | string | 是 | 代币地址列表（用逗号分隔） |

### 请求示例

```
GET /api/v1/wallets/123/swap/prices/?device_id=device123&token_addresses=So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
```

### 响应示例

```json
{
    "status": "success",
    "data": {
        "prices": {
            "So11111111111111111111111111111111111111112": {
                "price": "100.00",
                "price_change_24h": "5.2"
            },
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": {
                "price": "1.00",
                "price_change_24h": "0.1"
            }
        }
    }
}
```

## 错误响应

所有接口在出错时都会返回以下格式的错误响应：

```json
{
    "status": "error",
    "message": "错误描述",
    "code": "ERROR_CODE"
}
```

### 常见错误代码

| 错误代码 | 描述 |
|---------|------|
| MISSING_DEVICE_ID | 缺少设备ID参数 |
| MISSING_PARAMETERS | 缺少必要参数 |
| WALLET_ACCESS_DENIED | 无权访问该钱包 |
| INVALID_CHAIN | 该接口仅支持 Solana 钱包 |
| TOKENS_FETCH_FAILED | 获取代币列表失败 |
| QUOTE_FAILED | 获取报价失败 |
| SWAP_FAILED | 执行兑换失败 |
| MISSING_PRIVATE_KEY | 钱包私钥不存在 |
| INVALID_AMOUNT | 无效的数量或滑点参数 |
| WALLET_NOT_FOUND | 钱包不存在 |
| PRICE_FETCH_FAILED | 获取价格失败 |

## 使用流程

1. **获取支持的代币列表**: 调用 `/swap/tokens/` 接口获取可兑换的代币
2. **获取兑换报价**: 调用 `/swap/quote/` 接口获取兑换报价
3. **执行兑换**: 调用 `/swap/execute/` 接口执行实际兑换
4. **查看价格**: 可选调用 `/swap/prices/` 接口查看代币价格

## 注意事项

- 所有金额都使用字符串格式传递，避免精度丢失
- 滑点参数为百分比，例如 "0.5" 表示 0.5%
- 执行兑换需要提供支付密码进行身份验证
- 仅支持 Solana 链的钱包进行兑换操作
